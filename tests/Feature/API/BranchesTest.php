<?php

namespace Tests\Feature\API;

use Tests\TestCase;
use App\Models\Branch;
use App\Permissions\BranchPermissions;

class BranchesTest extends TestCase
{
    private $baseUrl = '/api/v1/branches';

    public function test_that_branches_index_success(): void
    {
        $this->setAuth();
        $response = $this->get($this->baseUrl);
        $response->assertOk()->assertJsonIsArray('data');
    }

    public function test_that_family_titles_store_validated(): void
    {
        $this->setAuth();
        $response = $this->post($this->baseUrl);
        $response->assertUnprocessable()->assertJsonIsObject('errors');
    }

    public function test_that_family_titles_created(): void
    {
        $faker = \Faker\Factory::create('ar_SA');
        $user = $this->setAuth();
        $user->givePermissionTo([BranchPermissions::namesWithSpaces]);
        $data = [
            'name' => $faker->name,
            'city_id' => $this->getFirstCity()->id,
        ];
        $response = $this->post($this->baseUrl, $data);
        $response->assertCreated()->assertJson(['success' => true]);
        $this->assertDatabaseHas('branches', $data);
    }

    public function test_that_family_titles_restored(): void
    {
        $this->setAuth();
        $branch = Branch::withTrashed()->create(['name' => 'Test']);
        $id = $branch->id;
        if (!$branch->trashed())
            $branch->delete();
        $response = $this->delete($this->baseUrl . "/$id/permanently-delete");
        $response->assertOk()->assertJson(['success' => true]);
        $this->assertDatabaseMissing('branches', [
            'id' => $id,
        ]);
        $branch->forceDelete();
    }

    public function test_that_family_titles_permanently_deleted(): void
    {
        $this->setAuth();
        $branch = Branch::withTrashed()->create(['name' => 'Test']);
        $id = $branch->id;
        if (!$branch->trashed())
            $branch->delete();
        $response = $this->delete($this->baseUrl . "/$id/permanently-delete");
        $response->assertOk()->assertJson(['success' => true]);
        $this->assertDatabaseMissing('branches', [
            'id' => $id,
        ]);
    }
}
