services:
    laravel.test:
        build:
            context: ./docker/8.3
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: sail-8.3/app
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${APP_PORT:-80}:80'
            - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
            - '${REVERB_SERVER_PORT:-8080}:8080'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
#            - mysql
            - redis
            - minio
            - pgsql
#    mysql:
#        image: 'mysql/mysql-server:8.0'
#        ports:
#            - '${FORWARD_DB_PORT:-3306}:3306'
#        environment:
#            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
#            MYSQL_ROOT_HOST: '%'
#            MYSQL_DATABASE: '${DB_DATABASE}'
#            MYSQL_USER: '${DB_USERNAME}'
#            MYSQL_PASSWORD: '${DB_PASSWORD}'
#            MYSQL_ALLOW_EMPTY_PASSWORD: 1
#        volumes:
#            - 'sail-mysql:/var/lib/mysql'
#            - './vendor/laravel/sail/database/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
#        networks:
#            - sail
#        healthcheck:
#            test:
#                - CMD
#                - mysqladmin
#                - ping
#                - '-p${DB_PASSWORD}'
#            retries: 3
#            timeout: 5s
    redis:
        image: 'redis:alpine'
        ports:
            - '${FORWARD_REDIS_PORT:-6379}:6379'
        volumes:
            - 'sail-redis:/data'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - redis-cli
                - ping
            retries: 3
            timeout: 5s
    minio:
        image: 'minio/minio:latest'
        ports:
            - '${FORWARD_MINIO_PORT:-9000}:9000'
            - '${FORWARD_MINIO_CONSOLE_PORT:-8900}:8900'
        environment:
            MINIO_ROOT_USER: sail
            MINIO_ROOT_PASSWORD: password
        volumes:
            - 'sail-minio:/data/minio'
        networks:
            - sail
        command: 'minio server /data/minio --console-address ":8900"'
        healthcheck:
            test:
                - CMD
                - curl
                - '-f'
                - 'http://localhost:9000/minio/health/live'
            retries: 3
            timeout: 5s
    pgsql:
        image: 'postgres:15'
        ports:
            - '${FORWARD_DB_PORT:-5432}:5432'
        environment:
            PGPASSWORD: '${DB_PASSWORD:-secret}'
            POSTGRES_DB: '${DB_DATABASE}'
            POSTGRES_USER: '${DB_USERNAME}'
            POSTGRES_PASSWORD: '${DB_PASSWORD:-secret}'
        volumes:
            - 'sail-pgsql:/var/lib/postgresql/data'
            - './vendor/laravel/sail/database/pgsql/create-testing-database.sql:/docker-entrypoint-initdb.d/10-create-testing-database.sql'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - pg_isready
                - '-q'
                - '-d'
                - '${DB_DATABASE}'
                - '-U'
                - '${DB_USERNAME}'
            retries: 3
            timeout: 5s
networks:
    sail:
        driver: bridge
volumes:
#    sail-mysql:
#        driver: local
    sail-redis:
        driver: local
    sail-minio:
        driver: local
    sail-pgsql:
        driver: local
