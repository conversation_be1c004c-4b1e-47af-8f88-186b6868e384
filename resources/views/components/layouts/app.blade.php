<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8" />

        <meta name="application-name" content="{{ config('app.name') }}" />
        <meta name="csrf-token" content="{{ csrf_token() }}" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        <title>{{ config('app.name') }}</title>

        <style>
            [x-cloak] {
                display: none !important;
            }
        </style>

        @filamentStyles
        {{
            Vite::useHotFile(public_path('filament/hot'))
            ->useBuildDirectory('filament')
            ->withEntryPoints(['resources/css/app.css'])
        }}
        <style>
            :root {
            @foreach($cssVariables as $name => $value)
                {{ $name }}: {{ $value }};
            @endforeach
        }

            .female-avatar {
                color: var(--female-avatar-color);
            }

            .male-avatar {
                color: var(--male-avatar-color);
            }
        </style>
    </head>

    <body class="antialiased">
        {{ $slot }}

        @livewire('notifications')

        @filamentScripts
        {{
            Vite::useHotFile(public_path('filament/hot'))
            ->useBuildDirectory('filament')
            ->withEntryPoints(['resources/js/app.js'])
        }}
        {{--@vite('resources/js/app.js', 'filament')--}}
    </body>
</html>
