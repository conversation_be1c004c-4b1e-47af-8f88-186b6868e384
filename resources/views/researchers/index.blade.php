@extends('layouts.contentLayoutMaster')

@section('title', 'قائمة الباحثين')

@section('content')
    <div class="card">
        <div class="card-body">
            @if(session()->has('message'))
                <div class="alert alert-success" role="alert">
                    <div class="alert-body">
                        <h4>{{ session('message') }}</h4>
                    </div>
                </div>
            @endif
            <table class="datatables-ajax table" id="ajaxTable">
                <thead>
                <tr>
                    {{--<th>#</th>--}}
                    <th>@lang('userColumns.family_user_id')</th>
                    <th>@lang('userColumns.full_name')</th>
                    <th class="no-search">@lang('userColumns.responsible_family_cases_count')</th>
                    <th class="no-search">@lang('userColumns.responsible_zwaj_cases_count')</th>
                    <th class="no-search">@lang('userColumns.responsible_debt_discharges_count')</th>
                    <th class="no-search">@lang('userColumns.responsible_support_services_count')</th>
                    @foreach($templates as $template)
                        <th class="no-search">{{ $template->title }}</th>
                    @endforeach
                    <th class="no-search">#</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
@endsection

@section('vendor-style')
    {{-- vendor css files --}}
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/dataTables.bootstrap5.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/responsive.bootstrap5.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/buttons.bootstrap5.min.css')) }}">
@endsection

@section('vendor-script')
    {{-- vendor files --}}
    <script src="{{ url(mix('vendors/js/tables/datatable/jquery.dataTables.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.bootstrap5.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.responsive.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/datatables.buttons.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/buttons.bootstrap5.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.rowGroup.min.js')) }}"></script>
    {{--<script src="{{ asset('vendors/js/tables/datatable/custom.js') }}"></script>--}}
    {{--<script src="{{ asset('vendors/js/bootstrap-confirmation/bootstrap-confirmation.js') }}"></script>--}}
@endsection

@section('page-script')
    {{-- Page js files --}}
    <script>
        $(function () {
            if (window.innerWidth > 480) {
                let _head = $('#ajaxTable thead tr').clone(true);
                _head.appendTo('#ajaxTable thead');
                $('#ajaxTable thead tr:eq(0) th.no-search').attr('rowspan', '2').css('text-align', 'center');
                $('#ajaxTable thead tr:eq(1) th').each(function (i, v) {
                    window.dtTableSearcher.bind(this)(() => dtTable, i)
                });
            }
            let dtTable = $('#ajaxTable').dataTable({
                dom: '<"card-header border-bottom p-1"<"head-label"><"dt-action-buttons text-right"B>><"d-flex justify-content-between align-items-center mx-0 row"<"col-sm-12 col-md-auto"l><"row align-items-center"<"col-sm-12 col-md-auto roles-filter">>>t<"d-flex justify-content-between mx-0 row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
                language: {
                    "url": '{{ url(mix('vendors/js/tables/datatable/Arabic.json')) }}'
                },
                ajax: '{{ route('admin.researchers.index') }}',
                search: {smart: false},
                processing: true,
                serverSide: true,
                sort: false,
                columns: [
                    /*{data: 'id', searchable: true},*/
                    {
                        data: 'family_user_id', searchable: true, orderable: false,
                        render: function (data, type, row) {
                            if (row.view_url)
                                return `<a style="color: -webkit-link;" href="${row.view_url}">${data}</a>`;
                            return data;
                        }
                    },
                    {
                        data: 'name', searchable: true, orderable: false,
                        render: function (data, type, row) {
                            if (row.view_url)
                                return `<a style="color: -webkit-link;" href="${row.view_url}">${data}</a>`;
                            return data;
                        }
                    },
                    {data: 'responsible_family_cases_count', searchable: true, orderable: false},
                    {data: 'responsible_zwaj_cases_count', searchable: true, orderable: false},
                    {data: 'responsible_debt_discharges_count', searchable: true, orderable: false},
                    {data: 'responsible_support_services_count', searchable: true, orderable: false},
                    @foreach($templates as $template)
                        {data: 'help_forms_count', searchable: true, orderable: false, render: (data) => data['id_{{ $template->id }}'] ?? 0},
                    @endforeach
                    {
                        data: null, searchable: false, orderable: false, render: function (data, type, row) {
                            let _btns = [];
                            if (row.view_url)
                                _btns.push(`<a href="${row.view_url}">${feather.icons['eye'].toSvg({
                                    width: 64,
                                    height: 64,
                                    style: 'width:2rem;height:2rem;'
                                })}</a>`);
                            return _btns.join('&nbsp;&nbsp;')
                        }
                    },
                ],
                buttons: [
                    //ReloadButton,
                ]
            });
            $('div.head-label').html('<h2 class="mb-0">الباحثين</h2>');
        })
    </script>
@endsection
