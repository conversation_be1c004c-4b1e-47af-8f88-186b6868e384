@if($familyCase->reasons->count() > 0 || can('status', $familyCase))
    <!-- Case Reasons -->
    <div class="col-12">
        <h6 class="section-label">أسباب قبول / رفض المستفيد</h6>
        <div class="card">
            <div class="card-body">
                @if($familyCase->reasons->count() > 0)
                    @foreach($familyCase->reasons as $reason)
                        <div class="d-flex align-items-start">
                            <div class="avatar me-75">
                                <x-user-avatar :user="$reason->user" width="24" />
                            </div>
                            <div class="author-info">
                                <h6 class="fw-bolder mb-25">
                                    {{ $reason->user->identifier }}
                                </h6>
                                <p class="card-text">
                                    {{ $reason->created_at->timezone(app('timezone'))->toDayDateTimeString() }}
                                    |
                                    @isset($reason->status)
                                        @switch($reason->status)
                                            @case(\App\Enums\CaseStatus::Approved)
                                                <span class="badge badge-pill badge-light-success me-50">مقبولة</span>
                                                @break
                                            @case(\App\Enums\CaseStatus::Declined)
                                                <span class="badge badge-pill badge-light-danger me-50">مرفوضة</span>
                                                @break
                                        @endswitch
                                    @endif
                                </p>
                                <p class="card-text">
                                    {{ $reason->content }}
                                </p>
                            </div>
                        </div>
                        <hr class="my-1"/>
                    @endforeach
                @endif
                @can('status', $familyCase)
                    <form action="{{ route('admin.family-cases.status-update', $familyCase) }}" class="form"
                          method="post">
                        @csrf
                        <div class="row">
                            <div class="col-12">
                            <textarea class="form-control @error('reason') is-invalid @enderror" rows="4" name="reason"
                                      aria-label="السبب" placeholder="السبب">{{ old('reason') }}</textarea>
                                @error('reason')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-12">
                                <div class="demo-inline-spacing">
                                    <div class="form-check form-check-inline form-check-success">
                                        <input type="radio" id="familyCaseStatusAccepted" name="status"
                                               class="form-check-input"
                                               value="{{ App\Enums\CaseStatus::Approved }}"/>
                                        <label class="form-check-label"
                                               for="familyCaseStatusAccepted">مقبولة</label>
                                    </div>
                                    <div class="form-check form-check-inline form-check-danger">
                                        <input type="radio" id="familyCaseStatusRejected" name="status"
                                               class="form-check-input"
                                               value="{{ App\Enums\CaseStatus::Declined }}"/>
                                        <label class="form-check-label"
                                               for="familyCaseStatusRejected">مرفوضة</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <button type="submit" class="btn btn-primary">إضافة</button>
                            </div>
                        </div>
                    </form>
                @endcan
            </div>
        </div>
    </div>
    <!--/ Case Reasons -->
@endif
