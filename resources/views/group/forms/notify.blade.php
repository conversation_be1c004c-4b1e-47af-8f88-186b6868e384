<form action="{{ route('admin.groups.notify', $group) }}" method="post" class="col-12">
    @csrf
    <div class="row">
        @foreach($group->users->reject(function($user){ return is_null($user->user->phone); }) as $user)
            @php($UserFieldId = (isset($group) && isset($user)) ? sprintf('GroupNotify%s-%s', $group->id, $user->user_id) : '')
            <div class="col-xl-3 col-lg-4 col-md-6 col-12 mb-2">
                <div class="form-check form-check-inline">
                    <input type="checkbox" class="form-check-input attendance-box"
                           name="notify_users[]" value="{{ $user->user_id }}"
                           @if($errors->has('notify_txt') || $errors->has('notify_users'))
                               @if(array_search($user->user_id, old('notify_users') ?? []) !== false)
                               checked
                               @endif
                           @else
                            checked
                           @endif
                           id="{{$UserFieldId}}"/>
                    <label class="form-check-label"
                           for="{{$UserFieldId}}">{{ $user->user->getName(5, true) }}</label>
                </div>
            </div>
        @endforeach
    </div>
    @error('notify_users')
    <div class="invalid-feedback" style="display: block">{{ $message }}</div>
    @enderror
    @error('notify_users.*')
    <div class="invalid-feedback" style="display: block">{{ $message }}</div>
    @enderror
    <div class="col-12 mt-2">
        <label for="groupNotifyTxt">@lang('groupColumns.notify_txt')</label>
        <textarea type="text" name="notify_txt" autocomplete="off"
                  class="form-control @error('notify_txt') is-invalid @enderror"
                  id="groupNotifyTxt" placeholder="@lang('groupColumns.notify_txt')"
                  style="height: 80px;min-height: 80px;max-height: 120px;"
                  maxlength="1000">{{ old('notify_txt') }}</textarea>
        @error('notify_txt')
        <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
    <button class="btn btn-primary mt-2 col-auto" type="submit">إرسال</button>
</form>
