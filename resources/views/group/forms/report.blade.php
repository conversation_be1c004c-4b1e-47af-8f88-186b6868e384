<form method="post" id="groupReportCreateForm"
      action="{{route('admin.groups.report.store', $group)}}">
    @csrf
    <div class="mb-1">
        <label for="groupReportTitle">@lang('groupReportColumns.title')</label>
        <input type="text" class="form-control @error('title', 'groupReport') is-invalid @enderror"
               id="groupReportTitle" placeholder="@lang('groupReportColumns.title')" autocomplete="off" required
               name="title" value="{{ !$errors->groupReport->isEmpty() ? old('title') : '' }}"/>
        @error('title', 'groupReport')
        <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
    <div class="mb-1">
        <label for="groupReportLocation">@lang('groupReportColumns.location')</label>
        <select name="location" id="groupReportLocation" class="group-report-location-select"
                data-placeholder="@lang('groupReportColumns.location')">
            @foreach($locationData as $location)
                <option @if(!$errors->groupReport->isEmpty() &&  old('location') == $location) selected @endif
                value="{{ trim($location) }}">{{ trim($location) }}</option>
            @endforeach
        </select>
        @error('location', 'groupReport')
        <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
        @enderror
    </div>
    <div class="mb-1 repeater">
        <div data-repeater-list="discussion">
            <label>@lang('groupReportColumns.discussion')</label>
            @forelse(old('discussion', []) as $i => $discussion)
                <div data-repeater-item class="row me-0 ms-0 mb-1">
                    <input type="text" autocomplete="off"
                           class="col me-1 form-control @error('discussion.'.$i, 'groupReport') is-invalid @enderror"
                           value="{{ $discussion }}" name="discussion[]" required/>
                    <input data-repeater-delete class="col-auto btn btn-danger" type="button" value="x"/>
                    @error('discussion.'.$i, 'groupReport')
                    <div class="col-12 invalid-feedback" style="display: block;">{{ $message }}</div>
                    @enderror
                </div>
            @empty
                <div data-repeater-item class="row me-0 ms-0 mb-1">
                    <input type="text" class="col me-1 form-control" autocomplete="off"
                           value="" name="discussion[]" required/>
                    <input data-repeater-delete class="col-auto btn btn-danger" type="button" value="x"/>
                </div>
            @endforelse
        </div>
        <input data-repeater-create class="btn btn-primary" type="button" value="+"/>
    </div>
    <div class="mb-1 repeater">
        <div data-repeater-list="decision">
            <label>@lang('groupReportColumns.decision')</label>
            @forelse(old('decision', []) as $i => $decision)
                <div data-repeater-item class="row me-0 ms-0 mb-1">
                    <input type="text" autocomplete="off"
                           class="col me-1 form-control @error('decision.'.$i, 'groupReport') is-invalid @enderror"
                           value="{{ $decision }}" name="decision[]" required/>
                    <input data-repeater-delete class="col-auto btn btn-danger" type="button" value="x"/>
                    @error('decision.'.$i, 'groupReport')
                    <div class="col-12 invalid-feedback" style="display: block;">{{ $message }}</div>
                    @enderror
                </div>
            @empty
                <div data-repeater-item class="row me-0 ms-0 mb-1">
                    <input type="text" class="col me-1 form-control" autocomplete="off"
                           value="" name="decision[]" required/>
                    <input data-repeater-delete class="col-auto btn btn-danger" type="button" value="x"/>
                </div>
            @endforelse
        </div>
        <input data-repeater-create class="btn btn-primary" type="button" value="+"/>
    </div>
    <button id="editorFormReset" class="btn btn-secondary mt-4" type="reset">إلغاء</button>
    <div class="float-end">
        <button class="btn btn-primary mt-4" type="submit">حفظ التقرير</button>
    </div>
</form>
