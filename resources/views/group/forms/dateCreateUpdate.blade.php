<form method="post" id="groupDateCreateForm"
      action="{{route('admin.groups.date.store', $group)}}">
    @csrf
    <div class="row">
        <div class="col-lg-6 col-12 mb-1">
            <label for="groupDateScheduleAt">@lang('groupDateColumns.schedule_at')</label>
            <div class="input-group @error('schedule_at') is-invalid @enderror">
                <input type="text" class="form-control @error('schedule_at') is-invalid @enderror"
                       id="groupDateScheduleAt" placeholder="YYYY-MM-DD"
                       style="direction: ltr;text-align: left;"
                       autocomplete="off" required name="schedule_at"
                       value="{{ $errors->isNotEmpty() ? old('schedule_at') : optional(@$groupDate->schedule_at)->format('Y-m-d') }}"/>
                <button
                    class="btn @error('schedule_at') btn-outline-danger @else btn-outline-dark @enderror" type="button">
                    <i data-feather="x"></i>
                </button>
            </div>
            @error('schedule_at')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-lg-6 col-12 mb-1">
            <label for="groupRemindAt">@lang('groupDateColumns.remind_at')</label>
            <div class="input-group @error('remind_at') is-invalid @enderror">
                <input type="text" class="form-control @error('remind_at') is-invalid @enderror"
                       id="groupRemindAt" placeholder="YYYY-MM-DD HH:MM"
                       style="direction: ltr;text-align: left;"
                       autocomplete="off" required name="remind_at"
                       value="{{ $errors->isNotEmpty() ? old('remind_at') : optional(optional(@$groupDate->remind_at)->timezone(app('timezone')))->format('Y-m-d G:i') }}"/>
                <button class="btn @error('remind_at') btn-outline-danger @else btn-outline-dark @enderror"
                        type="button">
                    <i data-feather="x"></i>
                </button>
            </div>
            @error('remind_at')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-12 mb-1">
            <label for="groupRemindTxt">@lang('groupDateColumns.remind_txt')</label>
            <textarea type="text" class="form-control @error('remind_txt') is-invalid @enderror"
                      id="groupRemindTxt" placeholder="@lang('groupDateColumns.remind_txt')"
                      maxlength="1000" name="remind_txt" autocomplete="off"
                      style="height: 80px;min-height: 80px;max-height: 120px;"
            >{{ $errors->isNotEmpty() ? old('remind_txt') : @$groupDate->remind_txt }}</textarea>
            @error('remind_txt')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>
    <button id="groupDateCreateFormReset" class="btn btn-secondary mt-4" type="reset">إلغاء</button>
    <div class="float-end">
        <button class="btn btn-primary mt-4" type="submit">حفظ الموعد</button>
    </div>
</form>
