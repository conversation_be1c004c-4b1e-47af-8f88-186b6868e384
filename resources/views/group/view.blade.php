@extends('layouts.contentLayoutMaster')
@section('title', "لجنة: {$group->name}")
@section('vendor-style')
    <!-- vendor css files -->
    <link rel="stylesheet" href="{{ url(mix('vendors/css/forms/wizard/bs-stepper.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('css/base/plugins/forms/form-wizard.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/pickers/flatpickr/flatpickr.css')) }}">
    @datepickerCSS()
@endsection
@section('content')
    @if(session()->has('message'))
        <div class="alert alert-success" role="alert">
            <div class="alert-body">
                <h4>{{ session('message') }}</h4>
            </div>
        </div>
    @endif
    <section class="vertical-wizard">
        <div class="bs-stepper vertical" id="GroupWizard">
            <div class="bs-stepper-header">
                <div class="step" data-target="#group-details">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-box">1</span>
                        <span class="bs-stepper-label">
                            <span class="bs-stepper-title">بيانات اللجنة</span>
                            @if($group->users->count() > 0)
                                <span class="bs-stepper-subtitle">{{ $group->users->count() }} عضو</span>
                            @endif
                        </span>
                    </button>
                </div>
                <div class="step" data-target="#groups-attendance">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-box">2</span>
                        <span class="bs-stepper-label">
                            <span class="bs-stepper-title">تسجيل الحضور</span>
                            @if($group->group_dates->count() > 0)
                                <span class="bs-stepper-subtitle">{{ $group->group_dates->count() }} موعد</span>
                            @endif
                        </span>
                    </button>
                </div>
                <div class="step" data-target="#groups-reports">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-box">3</span>
                        <span class="bs-stepper-label">
                        <span class="bs-stepper-title">@lang('groupColumns.reports')</span>
                        @if($group->reports->count() > 0)
                                <span class="bs-stepper-subtitle">{{ $group->reports->count() }} تقرير</span>
                            @endif
                    </span>
                    </button>
                </div>
                <div class="step" data-target="#groups-notification">
                    <button type="button" class="step-trigger">
                        <span class="bs-stepper-box">4</span>
                        <span class="bs-stepper-label">
                            <span class="bs-stepper-title">الرسائل</span>
                        </span>
                    </button>
                </div>
            </div>
            <div class="bs-stepper-content">
                <div id="group-details" class="content">
                    <div class="content-header">
                        <h5 class="mb-0">{{ $group->name }}</h5>
                        <small class="text-muted">
                            <div class="media">
                                <div class="avatar me-50" @if($group->created_at)style="margin-top: 5px;"@endif>
                                    <x-user-avatar :user="$group->user" width="24" />
                                </div>
                                <div class="media-body">
                                    @if($group->user)
                                        <small>
                                            <a href="javascript:void(0);"
                                               class="text-body">{{ $group->user->identifier }}</a>
                                        </small>
                                    @endif
                                    <br>
                                    @if($group->created_at)
                                        <small class="text-muted">
                                            {{ $group->created_at->timezone(app('timezone'))->toDayDateTimeString() }}
                                        </small>
                                    @endif
                                </div>
                            </div>
                        </small>
                    </div>
                    <div class="row">
                        {{--@if(!is_null($group->users) && $group->users->count() > 0)
                            <div class="form-group col-md-6 col-12">
                                <label class="form-label"
                                       for="attendance_id_count">@lang('groupColumns.users_count')</label>
                                <input id="attendance_id_count" class="form-control" disabled
                                       value="{{ $group->users->count() }}"/>
                            </div>
                        @endif
                        @isset($group->updated_at)
                            <div class="form-group col-md-6 col-12">
                                <label class="form-label" for="updated_at">@lang('groupColumns.updated_at')</label>
                                <input id="updated_at" class="form-control" disabled
                                       value="{{ $group->updated_at->timezone(app('timezone'))->toDayDateTimeString() }}"/>
                            </div>
                        @endisset--}}
                        @isset($group->notes)
                            <div class="form-group col-12">
                                <label class="form-label" for="notes">@lang('groupColumns.notes')</label>
                                <textarea class="form-control" style="height: 80px;min-height: 80px;max-height: 120px;"
                                          id="notes" disabled>{{$group->notes}}</textarea>
                            </div>
                        @endisset
                    </div>
                    {{--<hr>--}}
                    @if($group->users->count() > 0)
                        <table class="table table-bordered mt-1">
                            <tr>
                                <td>#</td>
                                <td>@lang('userColumns.family_user_id')</td>
                                <td>إسم العضو</td>
                                <td>الصفة</td>
                            </tr>
                            @foreach($group->users as $user)
                                <tr @class(['text-danger' => !$user->user->trashed() && $user->user->is_dead, 'text-warning' => $user->user->trashed()])>
                                    <td>{{ $loop->index + 1 }}</td>
                                    <td>{{ $user->user->family_user_id }}</td>
                                    <td>{{ $user->user->getName(5, true) }}</td>
                                    <td>@lang("groupColumns.user-role.{$user->role}")</td>
                                </tr>
                            @endforeach
                        </table>
                    @endif
                </div>
                <div id="groups-attendance" class="content">
                    <div id="groupDatesView">
                        @if($group->group_dates->count() > 0)
                            <table class="table table-bordered mb-1">
                                <thead>
                                <tr>
                                    <th>#</th>
                                    <th>موعد اللجنة</th>
                                    <th>عدد الحضور</th>
                                    <th>موعد الرسالة التذكيرية</th>
                                    <th>الإضافة</th>
                                    <th>تسجيل الحضور</th>
                                    <th>الإجراءات</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($group->group_dates->sortByDesc('schedule_at') as $date)
                                    <tr>
                                        <td>{{ $loop->index+1 }}</td>
                                        <td>{{ optional($date->schedule_at)->toDateString() }}</td>
                                        <td>{{ count($date->attendance_id ?: []) }}</td>
                                        <td>{{ optional(optional($date->remind_at)->timezone(app('timezone')))->format('Y-m-d G:i') ?: 'بدون تذكير' }}</td>
                                        <td>{{ optional(optional($date->created_at)->timezone(app('timezone')))->toDayDateTimeString() }}</td>
                                        <td>{{ optional(optional($date->done_at)->timezone(app('timezone')))->toDayDateTimeString() }}</td>
                                        <td>
                                            @can('attendance', $group)
                                                @if(is_null($date->done_at))
                                                    <a class="btn btn-sm btn-outline-info date-attendance-btn"
                                                       data-link="{{ route('admin.groups.date.attendance', [$group, $date]) }}"
                                                       href="javascript:void(0)">تسجيل الحضور</a>&nbsp;
                                                @endif
                                            @endcan
                                            @can('dateDelete', $group)
                                                <a href="javascript:void(0)" class="del-btn">
                                                    <i data-feather="trash" style="width:2rem;height:2rem;"></i>
                                                </a>
                                                <form style="display: none;" class="del-from"
                                                      action="{{ signedRoute('admin.groups.date.destroy', [$group, $date]) }}"
                                                      method="post">
                                                    @csrf
                                                    @method('delete')
                                                </form>
                                            @endcan
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        @endif
                        @can('dateCreate', $group)
                            <button id="newGroupDate" class="btn btn-info">تسجيل موعد جديد</button>
                        @endcan
                    </div>
                    <div id="groupDateCreate" style="display: none;">
                        @include('group.forms.dateCreateUpdate')
                    </div>
                    <div id="groupDateAttendance" style="display: none;">

                    </div>
                </div>
                <div id="groups-reports" class="content">
                    <div id="reportsView">
                        @if($group->reports->count() > 0)
                            <table class="table table-bordered mb-1">
                                <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم التقرير</th>
                                    <th>مقدم التقرير</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($group->reports->sortByDesc('created_at') as $report)
                                    <tr>
                                        <td>{{ $loop->index+1 }}</td>
                                        <td>
                                            {{ $report->report_id }}<br>
                                            {{ $report->name }}
                                        </td>
                                        <td>{{ optional($report->user)->identifier }}</td>
                                        <td>{{ optional(optional($report->created_at)->timezone(app('timezone')))->toDayDateTimeString() }}</td>
                                        <td>
                                            <a href="{{ route('admin.groups.report.show', [$group, $report]) }}"
                                               target="_blank" class="view-popup">
                                                <i data-feather="eye" style="width:2rem;height:2rem;"></i>
                                            </a>
                                            <a href="{{ route('admin.groups.report.edit', [$group, $report]) }}">
                                                <i data-feather="edit" style="width:2rem;height:2rem;"></i>
                                            </a>
                                            @can('reportDelete', $group)
                                                <a href="javascript:void(0)" class="del-btn">
                                                    <i data-feather="trash" style="width:2rem;height:2rem;"></i>
                                                </a>
                                                <form method="post" style="display: none;" class="del-form"
                                                      action="{{ signedRoute('admin.groups.report.destroy', [$group, $report]) }}">
                                                    @csrf
                                                    @method('delete')
                                                </form>
                                            @endcan
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        @endif
                        @can('reportCreate', $group)
                            <button id="newReport" class="btn btn-info">تسجيل تقرير جديد</button>
                        @endcan
                    </div>
                    <div id="reportsCreate" style="display: none;">
                        @include('group.forms.report')
                    </div>
                    @if(isset($groupReport))
                        <div id="reportsEdit" style="display: none;">
                            @include('group.forms.reportEdit', ['groupReport' => $groupReport])
                        </div>
                    @endif
                </div>
                <div id="groups-notification" class="content">
                    <div class="content-header">
                        <h5 class="mb-0">إرسال رسالة للمجموعة</h5>
                    </div>
                    <div class="row">
                        @include('group.forms.notify')
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('vendor-script')
    <script src="{{ url(mix('vendors/js/forms/wizard/bs-stepper.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/pickers/flatpickr/flatpickr.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/pickers/flatpickr/ar.js')) }}"></script>
    {{--<script src="{{ asset('vendors/js/bootstrap-confirmation/bootstrap-confirmation.js') }}"></script>--}}
    @datepickerJS()
    <script src="{{ url(mix('vendors/js/jquery.repeater.min.js')) }}"></script>
@endsection
@section('page-script')
    <script>
        $('.group-report-location-select').select2({
            theme: 'bootstrap-5',
            tags: true
        });

        function PopupCenter(url, title, w, h) {
            // Fixes dual-screen position      Most browsers      Firefox
            let dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX;
            let dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY;

            let width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
            let height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

            let systemZoom = width / window.screen.availWidth;
            let left = (width - w) / 2 / systemZoom + dualScreenLeft;
            let top = (height - h) / 2 / systemZoom + dualScreenTop;
            return window.open(url, title, 'scrollbars=yes, width=' + w / systemZoom + ', height=' + h / systemZoom + ', top=' + top + ', left=' + left);
        }

        $(function () {
            let _groupDateScheduleAt = $('#groupDateScheduleAt');
            _groupDateScheduleAt.hijriDatePicker(datepickerOptions)
            $(_groupDateScheduleAt.next()).click(function () {
                if (typeof _groupDateScheduleAt[0]._flatpickr !== "undefined")
                    _groupDateScheduleAt[0]._flatpickr.clear();
                else
                    $(_groupDateScheduleAt).val('');
            });

            let _groupRemindAt = $('#groupRemindAt');
            _groupRemindAt.flatpickr({
                enableTime: true,
                disableMobile: true,
                locale: 'ar',
            });
            $(_groupRemindAt.next()).click(function () {
                if (typeof _groupRemindAt[0]._flatpickr !== "undefined")
                    _groupRemindAt[0]._flatpickr.clear();
                else
                    $(_groupRemindAt).val('');
            });

            let verticalWizard = document.querySelector('#GroupWizard');
            if (typeof verticalWizard !== undefined && verticalWizard !== null) {
                let stepper = new Stepper(verticalWizard, {linear: false});
                @if($errors->has('schedule_at') || $errors->has('remind_at') || $errors->has('remind_txt'))
                stepper.to(2)
                $('#groupDatesView').hide();
                $('#groupDateCreate').show();
                @elseif(!$errors->groupReport->isEmpty() || !$errors->groupReportEdit->isEmpty() || isset($groupReport))
                stepper.to(3)
                $('#reportsView').hide();
                $('#{{ !$errors->groupReport->isEmpty() ? 'reportsCreate' : 'reportsEdit' }}').show();
                @elseif($errors->has('notify_users') || $errors->has('notify_txt'))
                stepper.to(4)
                @endif
            }
            let form = document.querySelector('#groupReportCreateForm');

            $('#newGroupDate').click(function () {
                $('#groupDatesView').hide();
                $('#groupDateCreate').show();
            });
            $('#groupDateCreateFormReset').click(function () {
                $('#groupDateCreate').hide();
                $('#groupDateCreateForm .invalid-feedback').remove();
                $('#groupDateCreateForm input').val('');
                $('#groupDateCreateForm .is-invalid').removeClass('is-invalid');
                $('#groupDateCreateForm button.btn-outline-danger').removeClass('btn-outline-danger').addClass('btn-outline-dark')
                $('#groupDatesView').show();
            });
            $('a.date-attendance-btn').click(function () {
                $.get($(this).data('link')).done(function (res) {
                    $('#groupDateAttendance').html(res).show();
                    $('#groupDatesView').hide();
                });
            });
            $('#groupDateAttendance').on('click', 'button[type=reset]', function () {
                $('#groupDateAttendance').html('').hide();
                $('#groupDatesView').show();
            }).on('click', 'button#AttendanceCheckAll', function () {
                $('#groupDateAttendance input.attendance-box').prop('checked', true);
            }).on('submit', 'form', function (e) {
                e.preventDefault();
                $.ajax({
                    type: 'POST',
                    data: $(this).serializeArray(),
                    url: this.action,
                    success: function (res) {
                        $('#groupDateAttendance').html(res).show();
                        $('#groupDatesView').hide();
                    }
                });
            });

            $('#newReport').click(function () {
                $('#reportsView').hide();
                $('#reportsCreate').show();
            });

            $('#editorFormReset').click(function () {
                //todo clear form
                $('#reportsCreate').hide();
                $('#groupReportCreateForm .invalid-feedback').remove();
                $('#groupReportCreateForm .repeater:eq(0) div[data-repeater-item]:not(:eq(0))').remove();
                $('#groupReportCreateForm .repeater:eq(1) div[data-repeater-item]:not(:eq(0))').remove();
                $('#groupReportCreateForm input[type=text]').val('');
                $('#reportsView').show();
            });

            $("a.view-popup").click(function (e) {
                e.preventDefault();
                PopupCenter(this.href, this.href, 800, 600)
            });

            $("a.del-btn").confirmation({
                onConfirm: function (value) {
                    let form = $(this).next('form.del-form')
                    if (form)
                        form.submit();
                },
                rootSelector: 'a.del-btn',
                title: 'تأكيد الحذف ؟',
                popout: true,
                singleton: true,
                btnOkLabel: 'نعم',
                btnCancelLabel: 'لا',
                btnOkClass: 'btn btn-sm btn-danger',
                btnCancelClass: 'btn btn-sm btn-light',
            });

            $('.repeater').repeater({
                initEmpty: false,
                show: function () {
                    $('input.is-invalid', this).removeClass('is-invalid')
                    $('.invalid-feedback', this).remove()
                    $(this).slideDown();
                },
                defaultValues: {
                    'discussion[]': 'foo'
                },
                /*hide: function (deleteElement) {
                    if(confirm('Are you sure you want to delete this element?')) {
                        $(this).slideUp(deleteElement);
                    }
                },*/
                isFirstItemUndeletable: false
            });
        })
    </script>
@endsection
