@extends('layouts.contentLayoutMaster')

@section('title', 'تعديل بيانات التحويلات')

@section('content')
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">{{ $bankTransactionImport->file_name }}</h4>
        </div>
        <div class="card-body">
            <div class="col-12">
                <hr>
            </div>
            <div class="row">
                <div class="col-md-6 col-12">
                    @if($bankTransactionImport->items()->whereNull('error')->count() > 0 &&
                        $bankTransactionImport->items()->whereNull('error')->whereNull('supporter_id')->count() === 0)
                        <button class="btn btn-primary mt-1 w-100" type="submit" id="ConfirmBtn">
                            <i class="far fa-check"></i>
                            تأكيد بيانات الملف
                        </button>
                    @endif
                </div>
                <div class="col-md-6 col-12">
                    <button class="btn btn-danger mt-1 w-100 me-25" type="submit" id="CancelBtn">
                        <i class="far fa-trash"></i>
                        إلغاء العملية & حذف الملف
                    </button>
                </div>
            </div>
            <div class="col-12">
                <hr>
            </div>
            <form method="post"
                  action="{{ route('admin.bank-transaction-import.update', ['bank_transaction_import' => $bankTransactionImport]) }}">
                @method('PUT')
                @csrf
                <div class="row">
                    <div class="col-12">
                        <h2>العمليات :</h2>
                    </div>
                    <div class="col-12 mb-2 mt-2">
                        <table class="table table-striped table-bordered">
                            <thead>
                            <tr>
                                <th>#</th>
                                <th>مبلغ التحويل</th>
                                <th>التفاصيل</th>
                                <th style="width: 180px;">تاريخ التحويل</th>
                                <th>الداعم</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($bankTransactionImport->items as $k => $item)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td style="width: 160px;">{{ number_format($item->amount) }}&nbsp;{{ 'ر.س' }}</td>
                                    <td style="max-width: 160px;direction: ltr;">{{ nl2br($item->notes) }}</td>
                                    @if(!empty($item->error))
                                        <td class="text-danger text-center" colspan="2">{{ $item->error }}</td>
                                    @else
                                        <td style="direction: ltr;">
                                            {{ $item->due_at->toDateTimeString('minute') }}
                                        </td>
                                        <td>
                                            <input type="hidden" name="items[{{ $k }}][id]"
                                                   value="{{ $item->id }}">
                                            <select @class(['form-control', 'supporter-input' => is_null($item->supporter)])
                                                    @disabled(!is_null($item->supporter))
                                                    name="items[{{ $k }}][supporter_id]">
                                                @isset($item->supporter)
                                                    <option value="{{ $item->supporter->id }}"
                                                            selected>{{ $item->supporter->name }}
                                                        ({{ $item->supporter->account }})
                                                    </option>
                                                @endisset
                                            </select>
                                            @error("items.$k.supporter_id")
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                            @enderror
                                        </td>
                                    @endif
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                        @if($bankTransactionImport->items()->whereNull('error')->whereNull('supporter_id')->count() > 0)
                            <button class="btn btn-primary mt-1" type="submit">حفظ بيانات التحويل</button>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('vendor-style')

@endsection

@section('vendor-script')

@endsection

@section('page-script')
    {{-- Page js files --}}
    <script>
        $(function () {
            $('.supporter-input').select2({
                theme: 'bootstrap-5',
                width: '100%',
                ajax: {
                    url: "{!! signedRoute('admin.web-api.supporters', [], now()->addHour()) !!}",
                    dataType: 'json',
                    method: 'post',
                    delay: 250,
                    cache: true,
                    data: function (params) {
                        return {
                            q: params.term,
                            _token: '{{ csrf_token() }}',
                            page: params.page
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data,
                            pagination: {
                                more: (params.page * 15) < data.total
                            }
                        };
                    }
                },
                escapeMarkup: markup => markup,
                minimumInputLength: 1,
                templateResult: function (supporter) {
                    if (supporter.loading)
                        return supporter.text;
                    let markup = "<div class='select2-result-fabricsitory clearfix d-flex'>" +
                        "<div class='select2-result-fabricsitory__meta'>" +
                        "<div class='select2-result-fabricsitory__title fs-lg fw-500'>" + (supporter.name) + "</div>";
                    if (supporter.account)
                        markup += "<div class='select2-result-fabricsitory__description fs-xs opacity-80 mb-1'>(" + supporter.account + ")</div>";
                    markup += "</div></div>";
                    return markup;
                },
                templateSelection: function (supporter) {
                    return supporter.text ? supporter.text : `${supporter.name} (${supporter.account})`;
                },
            });
            $('#ConfirmBtn').confirmation({
                rootSelector: '#ConfirmBtn',
                title: 'تأكيد بيانات الملف ؟',
                popout: true,
                singleton: true,
                btnOkLabel: 'نعم',
                btnCancelLabel: 'لا',
                btnOkClass: 'btn btn-sm btn-primary',
                btnCancelClass: 'btn btn-sm btn-light',
                onConfirm: (event) => {
                    window.location = '{!! signedRoute('admin.bank-transaction-import.actions', ['bank_transaction_import' => $bankTransactionImport, 'action' => 'confirm']) !!}'
                }
            });
            $('#CancelBtn').confirmation({
                rootSelector: '#CancelBtn',
                title: 'تأكيد إلغاء & حذف الملف ؟',
                popout: true,
                singleton: true,
                btnOkLabel: 'نعم',
                btnCancelLabel: 'لا',
                btnOkClass: 'btn btn-sm btn-danger',
                btnCancelClass: 'btn btn-sm btn-light',
                onConfirm: (event) => {
                    window.location = '{!! signedRoute('admin.bank-transaction-import.actions', ['bank_transaction_import' => $bankTransactionImport, 'action' => 'cancel']) !!}'
                }
            });
        })
    </script>
@endsection
