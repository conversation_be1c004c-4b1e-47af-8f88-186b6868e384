@php($rnd = \Illuminate\Support\Str::random())
<div class="col-md-12" style="text-align: right">
    @if(isset($message) && !empty($message))
        <div id="{{$rnd}}_alert" class="alert alert-info col-12 mt-2 p-1">{{$message}}</div>
    @endif
    <form class="form-horizontal row swal-submit-form" id="{{$rnd}}_form"
          method="{{ isset($bankAccount) ? 'PUT' : 'POST' }}"
          data-url="{{ isset($bankAccount) ? route('admin.bank-accounts.update', $bankAccount) : route('admin.bank-accounts.store') }}">
        <div class="form-group col-12">
            <label class="form-label" for="{{$rnd}}_title">اسم الحساب</label>
            <input id="{{$rnd}}_title" name="title" placeholder="اسم الحساب" autocomplete="off"
                   value="{{ (request()->isMethod('post') && $errors->isNotEmpty()) ? request('title') : @$bankAccount->title }}"
                   class="form-control input-md @error('title') is-invalid @enderror">
            @error('title')
            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <div class="form-group col-12">
            <label class="form-label" for="{{$rnd}}_account">رقم الحساب</label>
            <input id="{{$rnd}}_account" name="account" placeholder="رقم الحساب" autocomplete="off"
                   value="{{ (request()->isMethod('post') && $errors->isNotEmpty()) ? request('account') : @$bankAccount->account }}"
                   class="form-control input-md @error('account') is-invalid @enderror">
            @error('account')
            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <div class="form-group col-6">
            <label class="form-label" for="{{$rnd}}_type">نوع الحساب</label>
            <select class="select2 form-control" name="type" id="{{$rnd}}_type"
                    data-minimum-results-for-search="-1" data-allow-clear="true" data-placeholder="نوع الحساب">
                <option value=""></option>
                @foreach(['zakat', 'charity', 'zwaj', 'other'] as $item)
                    <option
                        @if(((request()->isMethod('post') && $errors->isNotEmpty()) ? request('type') : @$bankAccount->type) === $item) selected
                        @endif
                        value="{{$item}}">{{__('bankAccountColumns.types.'.$item)}}</option>
                @endforeach
            </select>
            @error('type')
            <span class="invalid-feedback" role="alert" style="display: block;"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <div class="form-group col-6">
            <label class="form-label" for="{{$rnd}}_bank">البنك</label>
            <select class="select2 form-control" name="bank" id="{{$rnd}}_bank"
                    data-minimum-results-for-search="-1" data-allow-clear="true" data-placeholder="البنك">
                <option value=""></option>
                @foreach(['albilad', 'alrajhi', 'anb', 'alahli', 'alinma', 'aljazira', 'riyad'] as $item)
                    <option
                        @if(((request()->isMethod('post') && $errors->isNotEmpty()) ? request('bank') : @$bankAccount->bank) === $item) selected
                        @endif
                        value="{{$item}}">{{__('bankAccountColumns.banks.'.$item)}}</option>
                @endforeach
            </select>
            @error('bank')
            <span class="invalid-feedback" role="alert" style="display: block;"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <input type="submit" style="display: none;">
    </form>
</div>

<script>
    $(function () {
        $('select.select2', '#{{$rnd}}_form').select2({theme:'bootstrap-5'}).each(function (k, v) {
            $($(v).data('select2').$dropdown).css('z-index', '9001');
        });
        $('#{{$rnd}}_form').submit(function (e) {
            e.preventDefault();
            swal.clickConfirm();
        });
        setTimeout(function () {
            $('#{{$rnd}}_alert').hide(function () {
                $(this).remove();
            })
        }, 5000);
    });
</script>
