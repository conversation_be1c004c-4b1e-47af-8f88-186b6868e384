@php
    $rnd = \Illuminate\Support\Str::random();
@endphp
<div class="col-md-12" style="text-align: right">
    @if(isset($message) && !empty($message))
        <div id="{{$rnd}}_alert" class="alert alert-info col-12 mt-2 p-1">{{$message}}</div>
    @endif
    <form class="form-horizontal row swal-submit-form" id="{{$rnd}}_form" method="post"
          data-url="{{ route('admin.help-form-templates.store') }}">
        @include('help-form-templates.form')
        <input type="submit" style="display: none;">
    </form>
</div>

<script>
    $(function () {
        $('#{{$rnd}}_form').closest('.swal2-html-container').css('z-index', 2)
        $('select.select2', '#{{$rnd}}_form').select2({theme: 'bootstrap-5'}).each(function (k, v) {
            $($(v).data('select2').$dropdown).css('z-index', '9001');
        });
        $('#{{$rnd}}_form').submit(function (e) {
            e.preventDefault();
            swal.clickConfirm();
        });
        setTimeout(function () {
            $('#{{$rnd}}_alert').hide(function () {
                $(this).remove();
            })
        }, 5000);
    });
</script>
@yield('form-scripts')
