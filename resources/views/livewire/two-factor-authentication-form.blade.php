<div class="card">
    <div class="card-header border-bottom">
        <h4 class="card-title">مولد الرموز (2FA)</h4>
    </div>
    <div class="card-body my-2 py-25">
        <h3 class="text-lg font-medium text-gray-900">
            @if ($this->enabled)
                @if ($showingConfirmation)
                    امسح الرمز لاستكمال تفعيل مولد الرموز .
                @else
                    لقد فعلت مولد الرموز .
                @endif
            @else
                مولد الرموز غير مفعل !
            @endif
        </h3>

        @if ($this->enabled)
            @if ($showingQrCode)
                <div class="mt-4 max-w-xl text-sm text-gray-600">
                    <p class="font-semibold">
                        @if ($showingConfirmation)
                            لإنهاء تفعيل مولد الرموز ، امسح رمز الاستجابة السريعة التالي باستخدام تطبيق المصادقة على
                            هاتفك أو أدخل مفتاح الإعداد وقدم رمز OTP الذي تم إنشاؤه.
                        @else
                            تم تمكين المصادقة الثنائية الآن. امسح رمز الاستجابة السريعة باستخدام تطبيق
                            المصادقة على هاتفك أو أدخل مفتاح الإعداد.
                        @endif
                    </p>
                </div>

                <div class="mt-4">
                    {!! $this->user->twoFactorQrCodeSvg() !!}
                </div>

                <div class="mt-4 max-w-xl text-sm text-gray-600">
                    <p class="font-semibold">
                        مفتاح الإعداد
                        : {{ decrypt($this->user->two_factor_secret) }}
                    </p>
                </div>

                @if ($showingConfirmation)
                    <div class="mt-4">
                        <x-label for="code" value="{{ __('Code') }}"/>

                        <x-input id="code" type="text" name="code" class="block mt-1 w-1/2" inputmode="numeric"
                                     autofocus autocomplete="one-time-code"
                                     wire:model="code"
                                     wire:keydown.enter="confirmTwoFactorAuthentication"/>

                        <x-input-error for="code" class="mt-2"/>
                    </div>
                @endif
            @endif

            {{--@if ($showingRecoveryCodes)
                <div class="mt-4 max-w-xl text-sm text-gray-600">
                    <p class="font-semibold">
                        {{ __('Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.') }}
                    </p>
                </div>

                <div class="grid gap-1 max-w-xl mt-4 px-4 py-4 font-mono text-sm bg-gray-100 rounded-lg">
                    @foreach (json_decode(decrypt($this->user->two_factor_recovery_codes), true) as $code)
                        <div>{{ $code }}</div>
                    @endforeach
                </div>
            @endif--}}
        @endif

        <div class="mt-5">
            @if (! $this->enabled)
                <x-confirms-password wire:then="enableTwoFactorAuthentication">
                    <x-button type="button" wire:loading.attr="disabled">
                        {{ __('Enable') }}
                    </x-button>
                </x-confirms-password>
            @else
                @if ($showingRecoveryCodes)
                    {{--<x-confirms-password wire:then="regenerateRecoveryCodes">
                        <x-secondary-button class="mr-3">
                            {{ __('Regenerate Recovery Codes') }}
                        </x-secondary-button>
                    </x-confirms-password>--}}
                @elseif ($showingConfirmation)
                    <x-confirms-password wire:then="confirmTwoFactorAuthentication">
                        <x-button type="button" class="mr-3" wire:loading.attr="disabled">
                            {{ __('Confirm') }}
                        </x-button>
                    </x-confirms-password>
                @else
                    {{--<x-confirms-password wire:then="showRecoveryCodes">
                        <x-secondary-button class="mr-3">
                            {{ __('Show Recovery Codes') }}
                        </x-secondary-button>
                    </x-confirms-password>--}}
                @endif

                @if ($showingConfirmation)
                    <x-confirms-password wire:then="disableTwoFactorAuthentication">
                        <x-secondary-button wire:loading.attr="disabled">
                            {{ __('Cancel') }}
                        </x-secondary-button>
                    </x-confirms-password>
                @else
                    <x-confirms-password wire:then="disableTwoFactorAuthentication">
                        <x-danger-button wire:loading.attr="disabled">
                            {{ __('Disable') }}
                        </x-danger-button>
                    </x-confirms-password>
                @endif

            @endif
        </div>
    </div>
</div>
