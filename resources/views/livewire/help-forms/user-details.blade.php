<div>
    <div class="row">
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label class="form-label">@lang('userColumns.name')</label>
            <input type="text" class="form-control" disabled readonly value="{{ $user->full_name }}"/>
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label class="form-label">@lang('userColumns.family_user_id')</label>
            <input type="text" class="form-control" disabled readonly value="{{ $user->family_user_id }}"/>
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label for="userDOD" class="w-100">
                @lang('userColumns.dod')
                <div class="float-end">
                    <div class="form-check form-check-inline m-0">
                        <input type="checkbox" class="form-check-input" id="userIsDead"
                               disabled readonly @checked($this->user->is_user_dead)>
                        <label class="form-check-label" for="userIsDead">متوفي</label>
                    </div>
                </div>
            </label>
            <input type="text" class="form-control @error('dod') is-invalid @enderror"
                   disabled readonly value="{{ $this->user->dod }}"
                   id="userDOD" placeholder="@lang('userColumns.dod')"/>
            @error('dod')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label for="userNationalId">@lang('userColumns.national_id')</label>
            <input type="text" class="form-control @error('national_id') is-invalid @enderror"
                   @disabled(!empty($user->national_id)) wire:model="national_id"
                   id="userNationalId" placeholder="@lang('userColumns.national_id')"/>
            @error('national_id')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label for="userPhone">@lang('userColumns.phone')</label>
            <input type="text" class="form-control @error('phone') is-invalid @enderror"
                   @disabled(!empty($user->phone)) wire:model="phone"
                   id="userPhone" placeholder="@lang('userColumns.phone')"/>
            @error('phone')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label for="userDOB" data-required>@lang('userColumns.dob')</label>
            <input type="text" class="form-control @error('dob') is-invalid @enderror"
                   @disabled(!empty($user->dob)) wire:model="dob"
                   id="userDOB" placeholder="@lang('userColumns.dob')"/>
            @error('dob')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label data-required>@lang('userColumns.educational_status')</label>
            <div wire:ignore>
                <select data-placeholder="@lang('userColumns.educational_status')"
                        class="form-control wire-select2 @error('educational_status') is-invalid @enderror"
                        data-minimum-results-for-search="-1" data-allow-clear="true"
                        @disabled(!empty($user->educational_status))
                        wire:model="educational_status">
                    <option value=""></option>
                    @foreach(all_educational_status() as $i)
                        <option value="{{ $i }}">{{ $i }}</option>
                    @endforeach
                </select>
            </div>
            @error('educational_status')
            <div class=" invalid-feedback d-block">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label data-required>@lang('userColumns.health_status')</label>
            <div wire:ignore>
                <select data-placeholder="@lang('userColumns.health_status')"
                        class="form-control wire-select2 @error('health_status') is-invalid @enderror"
                        data-minimum-results-for-search="-1" data-allow-clear="true"
                        @disabled(!empty($user->health_status))
                        wire:model="health_status">
                    <option value=""></option>
                    @foreach(all_health_status() as $i)
                        <option value="{{ $i }}">{{ $i }}</option>
                    @endforeach
                </select>
            </div>
            @error('health_status')
            <div class=" invalid-feedback d-block">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <div class="form-group" wire:ignore>
                <label for="userGender" data-required>@lang('userColumns.gender')</label>
                <select class="form-control wire-select2 @error('gender') is-invalid @enderror"
                        @disabled(!empty($user->gender)) wire:model="gender"
                        id="userGender">
                    <option value="">أختر النوع ذكر/أنثى</option>
                    @foreach(\App\Enums\Gender::all() as $gender)
                        <option value="{{ $gender }}">@lang("userColumns.gender.{$gender}")</option>
                    @endforeach
                </select>
            </div>
            @error('gender')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <div class="form-group" wire:ignore>
                <label for="userMaritalStatus">@lang('userColumns.marital_status')</label>
                <select class="form-control wire-select2 @error('marital_status') is-invalid @enderror"
                        id="userMaritalStatus"
                        @disabled(!empty($user->marital_status)) data-minimum-results-for-search="-1"
                        data-placeholder="@lang('userColumns.marital_status')" data-allow-clear="1"
                        wire:model="marital_status">
                    <option value=""></option>
                    @foreach(\App\Enums\UserMaritalStatus::all() as $status)
                        <option
                                value="{{ $status }}">@lang("userColumns.marital_status." . \App\Enums\Gender::Male . ".$status")</option>
                        @unset($status)
                    @endforeach
                </select>
            </div>
            @error('marital_status')
            <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-12">
            <hr>
        </div>
        {{--<div class="col-12">
            {{ $errors->first() }}
        </div>--}}
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label class="form-label">
                @if($user->gender === \App\Enums\Gender::Male)
                    عدد الزوجات
                @else
                    عدد الأزواج
                @endif
            </label>
            <input type="text" class="form-control" disabled readonly
                   value="{{ $user->gender === \App\Enums\Gender::Male ? $user->wives()->count() : $user->husbands()->count() }}"/>
        </div>
        <div class="col-xl-4 col-lg-6 col-12 mb-1">
            <label class="form-label">عدد الأبناء</label>
            <input type="text" class="form-control" disabled readonly
                   value="{{ $user->gender === \App\Enums\Gender::Male ? $user->children()->count() : $user->mother_children()->count() }}"/>
        </div>
    </div>
    <button class="btn btn-success btn-submit" style="margin-right: calc(100% - 80px);"
            wire:click="submit"
            wire:loading.attr="disabled">
        التالي
    </button>
    <script>
        $(function () {
            $('select.wire-select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
            }).on('change', function (e) {
                //@formatter:off
                if ($(e.target).attr('wire:model.live') !== undefined)
                    @this.set($(e.target).attr('wire:model.live'), e.target.value, true);
                else if ($(e.target).attr('wire:model') !== undefined)
                    @this.set($(e.target).attr('wire:model'), e.target.value, false);
                //@formatter:on
            });
            let _userDOB = $('#userDOB');
            if (_userDOB.data('HijriDatePicker') === undefined) {
                _userDOB.hijriDatePicker(datepickerOptions).on('dp.change', function (e) {
                    //@formatter:off
                    if ($(e.target).attr('wire:model.live') !== undefined)
                        @this.set($(e.target).attr('wire:model.live'), e.target.value, true);
                    else if ($(e.target).attr('wire:model') !== undefined)
                        @this.set($(e.target).attr('wire:model'), e.target.value, false);
                    //@formatter:on
                })
            }
            /*if (_userDOB[0]._flatpickr === undefined)
                _userDOB.flatpickr({
                    locale: {
                        firstDayOfWeek: 6
                    }
                })*/
        })
    </script>
</div>
