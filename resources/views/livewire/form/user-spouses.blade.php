<div class="col-12" id="UserWivesForm">
    <h6>{{ $user->gender === \App\Enums\Gender::Male ? 'زوجات' : 'أزواج' }} {{ $user->full_name }}</h6>
    <div class="clearfix"></div>
    @if($showAddFamilySpouse)
        <livewire:form.spouses.family-user-select :gender="$user->gender" key="family-form"/>
    @elseif($showAddNonFamilySpouse)
        <livewire:form.spouses.non-family-user-select :gender="$user->gender" key="non-family-form"/>
    @else
        <a href="javascript:void(0)" class="float-start" wire:click="AddFamilySpouse">إضافة من العائلة</a>
        <a href="javascript:void(0)" class="float-end" wire:click="AddNonFamilySpouse">إضافة من خارج العائلة</a>
        <div class="clearfix"></div>
        @if(!is_null($errorMessage))
            <div class="alert alert-warning mt-1" role="alert">
                <div class="alert-body"><h4>{{ $errorMessage }}</h4></div>
            </div>
        @endif
    @endif
    <div class="clearfix"></div>
    <hr>
    @if($user->gender === \App\Enums\Gender::Male)
        @if($user->wives)
            @foreach($user->wives as $wife)
                <livewire:form.spouse-record :$user :spouse="$wife" :key="'record-'.$wife->id"/>
                @unset($wife)
            @endforeach
        @endif
    @else
        @if($user->husbands)
            @foreach($user->husbands as $husband)
                <livewire:form.spouse-record :$user :spouse="$husband" :key="'record-'.$husband->id"/>
                @unset($husband)
            @endforeach
        @endif
    @endif
</div>
