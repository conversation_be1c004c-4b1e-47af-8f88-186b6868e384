@if($helpForm->template->enabled_fields->residence)
    <p class="r-t"
       style="margin-top: 2px;margin-right: 15px; border: 1px solid #565656;padding: 2.5px;text-align: center;width:180px;background: #565656;color: white;">
        بيانات السكن
    </p>
    @php($data = collect(array_filter(array_merge($helpForm->only([
                'residence', 'residence_type', 'residence_status', 'residence_size', 'residence_rent_cost', 'residence_services',
        ]), [

        ]), function($val){ return !is_null($val); }))->sortBy(function($v,$k){
            $index = array_search($k, [
                'residence', 'residence_type', 'residence_status', 'residence_size', 'residence_rent_cost', 'residence_services',
            ]);
            return $index === false ? 999 : $index;
        })->chunk(2))
    @include('pdf.case-table', ['case' => $helpForm, 'trans' => 'helpFormColumns', 'data' => $data, 'count' => $data->collapse()->count()])
@endif
