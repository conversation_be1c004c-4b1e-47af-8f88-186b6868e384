@if($helpForm->template->has_invoices && $helpForm->invoices->count() > 0)
    <p class="r-t"
       style="margin-top: 2px;margin-right: 15px; border: 1px solid #565656;padding: 2.5px;text-align: center;width:180px;background: #565656;color: white;">
        الفواتير
    </p>
    @foreach($helpForm->invoices as $invoice)
        <div style="width: 100%;display: table-cell;">
            <div
                style="float:right;width: 256px;text-align: center;" @class(['b-t b-r', 'b-b r-rb' => $loop->last, 'r-rt' => $loop->first])>
                {{ $invoice->type }}
            </div>
            <div style="float:right;width: 256px;text-align: center;" @class(['b-t b-r', 'b-b' => $loop->last])>
                {{ $invoice->no }}
            </div>
            <div style="float:right;width: 120px;text-align: center;" @class(['b-t b-r', 'b-b' => $loop->last])>
                {{ $invoice->due_at->toDateString() }}
            </div>
            <div
                style="float:right;width: 100px;text-align: center;" @class(['b-t b-r b-l', 'b-b r-lb' => $loop->last, 'r-lt' => $loop->first])>
                {{ number_format($invoice->value) }}
            </div>
        </div>
    @endforeach
@endif
