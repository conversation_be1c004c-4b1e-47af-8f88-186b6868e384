<table>
    <thead>
    <tr>
        <th>الرقم التعريفي</th>
        <th>الاسم</th>
        <th>الجنس</th>
        <th>موثق برقم الجوال</th>
        <th>الحالة الإجتماعية</th>
        <th>متوفي</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        @php($userStatus = ($user->{$user->gender === \App\Enums\Gender::Male ? 'active_wives' : 'active_husbands'}()->count() ? \App\Enums\UserMaritalStatus::Married : $user->marital_status) ?: \App\Enums\UserMaritalStatus::Single)
        <td>{{ $user->family_user_id }}</td>
        <td>{{ $user->getName(4, true) }}</td>
        <td>@lang("userColumns.gender.$user->gender")</td>
        <td>{{ !is_null($user->phone) ? 'نعم' : 'لا' }}</td>
        <td>@lang("userColumns.marital_status.{$user->gender}.$userStatus")</td>
        <td>{{ $user->is_user_dead ? 'نعم' : 'لا' }}</td>
    </tr>
    @foreach($user->nested_children as $child)
        @php($childStatus = ($child->{$child->gender === \App\Enums\Gender::Male ? 'active_wives' : 'active_husbands'}()->count() ? \App\Enums\UserMaritalStatus::Married : $child->marital_status) ?: \App\Enums\UserMaritalStatus::Single)
        <tr>
            <td>{{ $child->family_user_id }}</td>
            <td>{{ $child->getName(4, true) }}</td>
            <td>@lang("userColumns.gender.$child->gender")</td>
            <td>{{ !is_null($child->phone) ? 'نعم' : 'لا' }}</td>
            <td>@lang("userColumns.marital_status.{$child->gender}.$childStatus")</td>
            <td>{{ $child->is_user_dead ? 'نعم' : 'لا' }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
