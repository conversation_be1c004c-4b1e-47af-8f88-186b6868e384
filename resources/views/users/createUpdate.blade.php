@extends('layouts.contentLayoutMaster')
@section('title', (isset($user) ? "تعديل مستخدم: {$user->getName(5, true)}" : 'إضافة مستخدم جديد'))
@section('content')
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">بيانات المستخدم</h4>
        </div>
        <div class="card-body">
            @if(session()->has('message'))
                <div class="alert alert-success" role="alert">
                    <div class="alert-body">
                        <h4>{{ session('message') }}</h4>
                    </div>
                </div>
            @endif
            <form method="post" enctype="multipart/form-data" novalidate id="userCreateUpdateForm"
                  action="{{isset($user) ? route('admin.users.update', $user): route('admin.users.store', request()->all())}}">
                @csrf
                @isset($user)
                    @method('put')
                @endisset
                <div class="row">
                    <div class="col-12 mb-1" id="userProfilePhoto">
                        <label class="label" data-toggle="tooltip" title="تغيير صورة المستخدم">
                            <x-user-avatar :user="@$user" :width="128" :withoutATag="true"/>
                            <input type="file" class="sr-only" id="profilePhoto" accept="image/*">
                        </label>
                        <button class="btn btn-danger"
                                @if(is_null(@$user->profile_photo_path)) style="display: none;" @endif
                                type="button" id="DeleteProfilePhoto">
                            حذف الصورة
                        </button>
                        <input type="hidden" name="delete_profile_photo" value="0">
                        <input type="file" class="d-none" name="profile_photo" accept="image/*">
                        @error('profile_photo')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    @if(isset($user) && $user->family_user_id)
                        <div class="col-xl-4 col-lg-6 col-12 mb-1">
                            <label for="userFamilyId">@lang('userColumns.family_user_id')</label>
                            <input type="text" class="form-control" id="userFamilyId"
                                   placeholder="@lang('userColumns.family_user_id')" disabled readonly
                                   value="{{ $user->family_user_id }}"/>
                        </div>
                    @endif
                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="userName">@lang('userColumns.name')</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="userName"
                               placeholder="@lang('userColumns.name')" autocomplete="user-name" required
                               name="name" value="{{ $errors->isNotEmpty() ? old('name') : @$user->name }}"/>
                        @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="userEmail">@lang('userColumns.email')</label>
                        <input type="text" class="form-control @error('email') is-invalid @enderror" id="userEmail"
                               placeholder="@lang('userColumns.email')" autocomplete="user-email"
                               name="email" value="{{ $errors->isNotEmpty() ? old('email') : @$user->email }}"/>
                        @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="userNationalId">@lang('userColumns.national_id')</label>
                        <input type="text" class="form-control @error('national_id') is-invalid @enderror"
                               id="userNationalId" placeholder="@lang('userColumns.national_id')"
                               autocomplete="user-national_id" name="national_id" required
                               value="{{ $errors->isNotEmpty() ? old('national_id') : @$user->national_id }}"/>
                        @error('national_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="userPhone">@lang('userColumns.phone')</label>
                        <input type="text" class="form-control @error('phone') is-invalid @enderror"
                               id="userPhone" placeholder="@lang('userColumns.phone')"
                               autocomplete="user-phone" name="phone" required
                               value="{{ $errors->isNotEmpty() ? old('phone') : @$user->phone }}"/>
                        @error('phone')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <div class="form-group">
                            <label for="userGender">@lang('userColumns.gender')</label>
                            <select class="form-control @error('gender') is-invalid @enderror" id="userGender"
                                    required name="gender">
                                <option value="">أختر النوع ذكر/أنثى</option>
                                <option value="{{ \App\Enums\Gender::Male }}"
                                        @selected(($errors->isNotEmpty() ? old('gender') : @$user->gender) === \App\Enums\Gender::Male)>
                                    ذكر
                                </option>
                                <option value="{{ \App\Enums\Gender::Female }}"
                                        @selected(($errors->isNotEmpty() ? old('gender') : @$user->gender) === \App\Enums\Gender::Female)>
                                    أنثى
                                </option>
                            </select>
                            @error('gender')
                            <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <div class="form-group">
                            <label for="userMaritalStatus">@lang('userColumns.marital_status')</label>
                            <select class="form-control @error('marital_status') is-invalid @enderror"
                                    id="userMaritalStatus"
                                    required name="marital_status">
                                <option value="">أختر الحالة الاجتماعية</option>
                                @foreach(\App\Enums\UserMaritalStatus::all() as $status)
                                    <option value="{{ $status }}"
                                            @selected(($errors->isNotEmpty() ? old('marital_status') : @$user->marital_status) === $status)>@lang("userColumns.marital_status." . \App\Enums\Gender::Male . ".$status")</option>
                                    @unset($status)
                                @endforeach
                            </select>
                            @error('marital_status')
                            <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="userDOB">@lang('userColumns.dob')</label>
                        <input type="text" class="form-control @error('dob') is-invalid @enderror"
                               id="userDOB"
                               placeholder="@lang('userColumns.dob')"
                               autocomplete="off" required name="dob"
                               value="{{ $errors->isNotEmpty() ? old('dob') : @$user->dob }}"/>
                        @error('dob')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <div class="row">
                            <label class="col" for="userDOD">@lang('userColumns.dod')</label>
                            <div class="col-auto form-check form-check-inline">
                                <input type="checkbox" class="form-check-input" id="userIsDead" name="is_user_dead"
                                       @if((old('is_user_dead') && $errors->isNotEmpty()) || !is_null(@$user->dod) || @$user->is_dead) checked @endif>
                                <label class="form-check-label" for="userIsDead"></label>
                            </div>
                        </div>
                        <input type="text" class="form-control @error('dod') is-invalid @enderror"
                               id="userDOD"
                               placeholder="@lang('userColumns.dod')"
                               autocomplete="off" required name="dod"
                               value="{{ $errors->isNotEmpty() ? old('dod') : @$user->dod }}"/>
                        @error('dod')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        @php
                            $oldFatherId = ($errors->isNotEmpty() ? old('father_id') : @$user->father_id);
                            if(is_null($oldFatherId) && request()->has('father_id'))
                                $oldFatherId = request()->get('father_id');
                            if(isset($oldFatherId) && !empty($oldFatherId))
                                $oldParent = \App\Models\User::query()->withTrashed()->find($oldFatherId);
                        @endphp
                        <div class="form-group @error('father_id') is-invalid @enderror">
                            <label for="UserFatherId">
                                @lang('userColumns.father_id')
                                @if($errors->isEmpty() && !is_null(@$oldParent) && !is_null(@$user))
                                    : <a href="{{ route('admin.users.edit', $oldParent) }}" target="_blank">تعديل</a>
                                @endif
                            </label>
                            <select
                                    class="form-control @error('father_id') is-invalid @enderror @if(request()->has('father_id')) readonly-select @endif"
                                    id="UserFatherId" data-placeholder="@lang('userColumns.father_id')"
                                    name="father_id" data-allow-clear="true">
                                <option value=""></option>
                                @isset($oldParent)
                                    <option value="{{$oldParent->id}}" selected>{{ $oldParent->identifier }}</option>
                                @endisset
                            </select>
                            @error('father_id')
                            <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    @if(isset($oldParent) && !is_null($oldParent) && $oldParent->wives->count() > 0)
                        <div class="col-xl-4 col-lg-6 col-12 mb-1">
                            <div class="form-group">
                                <label for="userMotherId" class="form-label">
                                    @lang('userColumns.mother_id')
                                </label>
                                <select class="form-control select2 @error('mother_id') is-invalid @enderror"
                                        id="userMotherId" data-placeholder="@lang('userColumns.mother_id')"
                                        data-minimum-results-for-search="-1" name="mother_id" data-allow-clear="true">
                                    <option value=""></option>
                                    @foreach($oldParent->wives as $userWife)
                                        @php
                                            $selected = false;
                                            $userWifeVal = $userWife->wife_id;
                                            if($errors->isNotEmpty())
                                                $selected = old('mother_id') === $userWifeVal;
                                            elseif(isset($user))
                                                $selected = $user->mother_id == $userWife->wife_id;
                                        @endphp
                                        <option value="{{ $userWifeVal }}" @if(@$selected) selected @endif>
                                            {{ $userWife->wife->full_name }}
                                        </option>
                                        @unset($selected)
                                        @unset($userWifeVal)
                                        @unset($userWife)
                                    @endforeach
                                </select>
                                @error('mother_id')
                                <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    @endif

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="userHealthStatus" data-required>@lang('userColumns.health_status')</label>
                        <select data-placeholder="@lang('userColumns.health_status')" id="userHealthStatus"
                                class="form-control select2 @error('health_status') is-invalid @enderror"
                                data-minimum-results-for-search="-1" data-allow-clear="true" name="health_status">
                            <option value=""></option>
                            @foreach(all_health_status() as $i)
                                <option value="{{ $i }}"
                                        @selected(((request()->isMethod('post') && $errors->isNotEmpty()) ? old('health_status') : optional(@$user)->health_status) === $i)
                                >{{ $i }}</option>
                            @endforeach
                        </select>
                        @error('health_status')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label data-required for="userEducationalStatus">@lang('userColumns.educational_status')</label>
                        <select data-placeholder="@lang('userColumns.educational_status')" id="userEducationalStatus"
                                class="form-control select2 @error('educational_status') is-invalid @enderror"
                                data-minimum-results-for-search="-1" data-allow-clear="true" name="educational_status">
                            <option value=""></option>
                            @foreach(all_educational_status() as $i)
                                <option value="{{ $i }}"
                                        @selected(((request()->isMethod('post') && $errors->isNotEmpty()) ? old('educational_status') : optional(@$user)->educational_status) === $i)
                                >{{ $i }}</option>
                            @endforeach
                        </select>
                        @error('educational_status')
                        <div class=" invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12 mt-1 mb-1"></div>
                    <div class="col-lg-6 col-12 mb-1">
                        <label for="userBio">@lang('userColumns.bio')</label>
                        <textarea type="text" class="form-control @error('bio') is-invalid @enderror"
                                  id="userBio" placeholder="@lang('userColumns.bio')"
                                  maxlength="240" name="bio"
                                  autocomplete="user-bio"
                                  style="height: 80px;min-height: 80px;max-height: 120px;"
                                  required>{{ $errors->isNotEmpty() ? old('bio') : @$user->bio }}</textarea>
                        @error('bio')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        <hr>
                    </div>
                </div>
                @can('updatePermission', \App\Models\User::class)
                    <div class="form-group">
                        <label for="userRoles">الصلاحيات</label>
                        <select class="select2 form-control @error('roles') is-invalid @enderror"
                                multiple id="userRoles" name="roles[]">
                            @foreach(\Spatie\Permission\Models\Role::all() as $role)
                                <option value="{{ $role->name }}"
                                        @if(isset($user) && $user->hasRole($role->name)) selected @endif>
                                    {{ $role->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('roles.*')
                        <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
                        @enderror
                    </div>
                @endcan
                <div class="float-end">
                    <button class="btn btn-primary mt-4" type="submit">
                        @isset($user)
                            تعديل
                        @else
                            إضافة
                        @endisset
                    </button>
                </div>
            </form>
        </div>
    </div>
    <div class="modal fade" id="profileImageModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalLabel">قص الصورة</h5>
                </div>
                <div class="modal-body">
                    <div class="img-container">
                        <img id="image" src="{{ url(mix('images/portrait/small/avatar.png')) }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"
                            onclick="$('#profileImageModal').modal('hide');">إلغاء
                    </button>
                    <button type="button" class="btn btn-primary" id="crop">قص</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('vendor-style')
    <!-- vendor css files -->
    <style>
        .img-container img {
            max-width: 100%;
        }

        .cropper-view-box,
        .cropper-face {
            border-radius: 50%;
        }

        /* The css styles for `outline` do not follow `border-radius` on iOS/Safari (#979). */
        .cropper-view-box {
            outline: 0;
            box-shadow: 0 0 0 1px #39f;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css"/>
    @datepickerCSS()
@endsection

@section('vendor-script')
    <!-- vendor files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    @datepickerJS()
@endsection

@section('page-script')
    <script>
        $(function () {
            $('.select2').each(function () {
                var $this = $(this);
                $this.wrap('<div class="position-relative"></div>');
                $this.select2({
                    theme: 'bootstrap-5',
                    //dropdownAutoWidth: true,
                    width: '100%',
                    dropdownParent: $this.parent()
                });
            });
            let UserFatherId = $('#UserFatherId')[0];
            $(UserFatherId).select2({
                theme: 'bootstrap-5',
                //dropdownAutoWidth: true,
                width: '100%',
                disabled: {{ request()->has('father_id') ? 'true' : 'false' }},
                dropdownParent: UserFatherId.parentElement,
                ajax: {
                    url: "{{ route('admin.web-api.users') }}",
                    dataType: 'json',
                    method: 'post',
                    delay: 250,
                    cache: true,
                    data: function (params) {
                        return {
                            q: params.term,
                            _token: '{{ csrf_token() }}',
                            page: params.page
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data,
                            pagination: {
                                more: (params.page * 15) < data.total
                            }
                        };
                    }
                },
                escapeMarkup: markup => markup,
                minimumInputLength: 1,
                templateResult: function (user) {
                    if (user.loading)
                        return user.text;
                    let markup = "<div class='select2-result-fabricsitory clearfix d-flex'>" +
                        "<div class='select2-result-fabricsitory__meta'>" +
                        "<div class='select2-result-fabricsitory__title fs-lg fw-500'>" + (user.full_name || user.name) + "</div>";
                    if (user.family_user_id)
                        markup += "<div class='select2-result-fabricsitory__description fs-xs opacity-80 mb-1'>" + user.family_user_id + "</div>";
                    markup += "</div></div>";
                    return markup;
                },
                templateSelection: function (user) {
                    return user.text ? user.text : `#${user.family_user_id} ${(user.full_name || user.name)}`;
                },
            });
            let _userDOB = $('#userDOB'),
                _userDOD = $('#userDOD');
            _userDOB.hijriDatePicker(datepickerOptions)
            _userDOD.hijriDatePicker(datepickerOptions)

            let cropper,
                input = document.getElementById('profilePhoto'),
                avatar = $('img', '#userProfilePhoto')[0],
                image = $('img', '#profileImageModal')[0],
                $modal = $('#profileImageModal');
            input.addEventListener('change', function (e) {
                var files = e.target.files;
                var done = function (url) {
                    input.value = '';
                    image.src = url;
                    $modal.modal('show');
                };
                var reader;
                var file;
                var url;

                if (files && files.length > 0) {
                    file = files[0];

                    if (URL) {
                        done(URL.createObjectURL(file));
                    } else if (FileReader) {
                        reader = new FileReader();
                        reader.onload = function (e) {
                            done(reader.result);
                        };
                        reader.readAsDataURL(file);
                    }
                }
            });
            $modal.on('shown.bs.modal', function () {
                cropper = new Cropper($('img', '#profileImageModal')[0], {
                    aspectRatio: 1,
                    viewMode: 3,
                    dragMode: 'move',
                    autoCropArea: 1,
                    cropBoxMovable: false,
                    cropBoxResizable: false,
                    toggleDragModeOnDblclick: false,
                });
            }).on('hidden.bs.modal', function () {
                cropper.destroy();
                cropper = null;
            });
            document.getElementById('crop').addEventListener('click', function () {
                $modal.modal('hide');
                if (cropper) {
                    let canvas = cropper.getCroppedCanvas({
                        width: 160,
                        height: 160,
                    });
                    avatar.src = canvas.toDataURL();
                    canvas.toBlob(function (blob) {
                        let file = new File([blob], `avatar-${Date.now()}.jpg`, {
                            type: "image/jpeg",
                            lastModified: new Date().getTime()
                        });
                        let container = new DataTransfer();
                        container.items.add(file);
                        document.getElementsByName('profile_photo')[0].files = container.files;
                        $('#DeleteProfilePhoto').show()
                    });
                }
            });
            $('#DeleteProfilePhoto').click(function () {
                $(this).hide()
                $('input[name=delete_profile_photo]').val('1')
                avatar.src = '{{ url(mix('images/portrait/small/avatar.png')) }}'
            })
        })
    </script>
@endsection
