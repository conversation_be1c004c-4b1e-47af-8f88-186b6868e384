@extends('layouts.contentLayoutMaster')
@section('title', $user->getName(3, true))
@section('content')
    @if(session()->has('message'))
        <div class="alert alert-success" role="alert">
            <div class="alert-body">
                <h4>{{ session('message') }}</h4>
            </div>
        </div>
    @endif
    <section class="app-user-view-account">
        <div class="order-0 order-md-1">
            <ul class="nav nav-pills mb-2" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" href="#userInfo" id="userInfo-tab" role="tab" data-bs-toggle="tab">
                        <i data-feather="user" class="font-medium-3 me-50"></i>
                        <span class="fw-bold">الحساب</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#userTree" id="userTree-tab" role="tab" data-bs-toggle="tab">
                        <i data-feather="git-branch" class="font-medium-3 me-50"></i>
                        <span class="fw-bold">
                            شجرة العائلة
                            @if($user->nested_children_count > 0)
                                <small class="text-muted">({{ $user->nested_children_count }} ابن/ـة)</small>
                            @endif
                        </span>
                    </a>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="userInfo" aria-labelledby="userInfo-tab" role="tabpanel">
                    <div class="row">
                        <div class="col-xl-4 col-lg-4 col-md-5 order-1 order-md-0">
                            <div class="card">
                                <div class="card-body p-1">
                                    <div class="user-avatar-section">
                                        @can('update', $user)
                                            <a href="{{ route('admin.users.edit', $user) }}" style="float:left">
                                                <i class="fas fa fa-pen"></i>
                                            </a>
                                        @endcan
                                        <div class="d-flex align-items-center flex-column">
                                            <x-user-avatar :user="$user" :width="96" :class="['avatar img mb-1']"/>
                                            <div class="user-info text-center">
                                                <h4>{{ $user->name }}</h4>
                                                <p>{{ $user->bio }}</p>
                                                @if($user->is_user_dead)
                                                    <span class="badge bg-light-danger">
                                                        @lang("userColumns.dead.$user->gender")
                                                    </span>
                                                @endif
                                                @foreach($user->roles as $role)
                                                    <span class="badge bg-light-primary">
                                                        {{ $role->title }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    @if($user->transactions_sum_amount > 0)
                                        @can('viewAny', \App\Models\Supporter::class)
                                            <div class="d-flex justify-content-around my-2 pt-75">
                                                <div class="d-flex align-items-start me-2">
                                                <span class="badge bg-light-primary p-75 rounded">
                                                    <i data-feather="dollar-sign" class="font-medium-2"></i>
                                                </span>
                                                    <div class="ms-75">
                                                        <h4 class="mb-0">
                                                            {{ number_format($user->transactions_sum_amount) . ' ' }}
                                                            ريال
                                                        </h4>
                                                        <small>مجموع التبرعات</small>
                                                    </div>
                                                </div>
                                            </div>
                                        @endcan
                                    @endif
                                    <h4 class="fw-bolder border-bottom pb-50 mb-1">نبذة عن {{ $user->name }}</h4>
                                    <table class="table table-borderless align-middle mb-0">
                                        <tbody>
                                        <tr>
                                            <td class="fw-bolder p-25" style="min-width: 100px;">الاسم خماسي:</td>
                                            <td><span>{{ $user->getFullName(4, false, true) }}</span></td>
                                        </tr>
                                        @isset($user->gender)
                                            <tr>
                                                <td class="fw-bolder p-25">الجنس:</td>
                                                <td>
                                                    <span @class(['badge bg-info' => $user->gender === \App\Enums\Gender::Male, 'badge bg-light-danger' => $user->gender === \App\Enums\Gender::Female])>
                                                        @lang("userColumns.gender.$user->gender")
                                                    </span>
                                                </td>
                                            </tr>
                                        @endisset
                                        @isset($user->marital_status)
                                            <tr>
                                                <td class="fw-bolder p-25">الحالة الاجتماعية:</td>
                                                <td>
                                                    <span>@lang('userColumns.marital_status.' . ($user->gender ?: \App\Enums\Gender::Male) . '.' . $user->marital_status)</span>
                                                </td>
                                            </tr>
                                        @endisset
                                        @isset($user->father)
                                            <tr>
                                                <td class="fw-bolder p-25">الأب:</td>
                                                <td>
                                                    @can('view', $user->father)
                                                        <a href="{{ route('admin.users.show', $user->father) }}">
                                                            {{ $user->father->name }}
                                                        </a>
                                                    @else
                                                        {{ $user->father->name }}
                                                    @endcan
                                                </td>
                                            </tr>
                                        @endisset
                                        @isset($user->family_user_id)
                                            <tr>
                                                <td class="fw-bolder p-25">الرقم التعريفي:</td>
                                                <td><span class="badge bg-primary">{{ $user->family_user_id }}</span>
                                                </td>
                                            </tr>
                                        @endisset
                                        @isset($user->branch)
                                            <tr>
                                                <td class="fw-bolder p-25">الفرع:</td>
                                                <td>
                                                    <a href="{{ is_null($user->branch->parent_user_id) ? 'javascript:void(0)' : route('admin.users.show', $user->branch->parent_user_id) }}">
                                                        <span class="link-primary">
                                                            {{ $user->branch->name }}
                                                        </span>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endisset
                                        {{--<tr>
                                            <td class="fw-bolder p-25">الحالة الاجتماعية:</td>
                                            <td><span>متزوج</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bolder p-25">المدينة:</td>
                                            <td><span>بريدة</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bolder p-25">الوظيفة:</td>
                                            <td><span>معلم</span></td>
                                        </tr>--}}
                                        @if($user->voluntaries_sum_total > 0)
                                            <tr>
                                                <td class="fw-bolder p-25">عدد ساعات التطوع:</td>
                                                <td>
                                                    <span>{{ $user->voluntaries_sum_total }} ساعة</span>
                                                </td>
                                            </tr>
                                        @endif
                                        @isset($user->dob)
                                            <tr>
                                                <td class="fw-bolder p-25">تاريخ الميلاد:</td>
                                                <td><span>{{ optional($user->dob)->toDateString() }}</span></td>
                                            </tr>
                                        @endisset
                                        @isset($user->dod)
                                            <tr>
                                                <td class="fw-bolder p-25">تاريخ الوفاة:</td>
                                                <td><span>{{ optional($user->dod)->toDateString() }}</span></td>
                                            </tr>
                                        @endisset
                                        @can(\App\Permissions\UserPermissions::viewPhone)
                                        @isset($user->phone)
                                            <tr>
                                                <td class="fw-bolder p-25">رقم الجوال:</td>
                                                <td>
                                                    <span style="text-align: left;direction: ltr">
                                                        <a href="tel:+{{ $user->international_phone }}">{{ $user->international_phone }}</a>
                                                    </span>
                                                </td>
                                            </tr>
                                        @endisset
                                        @endcan
                                        @isset($user->national_id)
                                            <tr>
                                                <td class="fw-bolder p-25">رقم الهوية:</td>
                                                <td><span>{{ $user->national_id }}</span></td>
                                            </tr>
                                        @endisset
                                        </tbody>
                                    </table>
                                    @can('downloadExcel', $user)
                                        <div class="d-flex justify-content-center pt-2">
                                            <a href="{{ route('admin.users.family-export', $user) }}"
                                               download="{{ 'عائلة ' . $user->getName(4, true) }}.xlsx"
                                               class="btn btn-primary w-100">
                                                <i class="fa fa-file-excel"></i>
                                                تصدير العائلة
                                            </a>
                                        </div>
                                    @endif
                                    @can('create', \App\Models\FamilyGraphShare::class)
                                        <div class="d-flex justify-content-center pt-2">
                                            <a href="javascript:void(0)"
                                               onclick="formModel('{{ route('admin.family-graph-shares.create', ['ref' => 'users-view', 'user_id' => $user->id]) }}', 'إضافة رابط مشاركة جديد', 'إضافة', () => {});"
                                               class="btn btn-info w-100">
                                                <i class="fa fa-share-nodes"></i>
                                                مشاركة شجرة العائلة
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            @if($user->children->count() > 0)
                                <div class="card">
                                    <div class="card-body profile-suggestion">
                                        <h2 class="mb-2 fw-bolder">الأبناء</h2>
                                        @foreach($user->children as $child)
                                            <div class="d-flex justify-content-start align-items-center mb-1">
                                                <div class="avatar me-1">
                                                    <x-user-avatar :url="$child->profile_photo_url" width="40"/>
                                                </div>
                                                <div class="profile-user-info">
                                                    <h6 class="mb-0">
                                                        @can('view', $child)
                                                            <a href="{{ route('admin.users.show', $child) }}">
                                                                {{ $child->name }}
                                                            </a>
                                                        @else
                                                            {{ $child->name }}
                                                        @endcan
                                                    </h6>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                            @if($user->groups->count() > 0)
                                <div class="card ">
                                    <div class="card-body">
                                        <h2 class="fw-bolder">اللجان المشارك فيها</h2>
                                        @foreach($user->groups as $group)
                                            <div class="d-flex justify-content-start align-items-center mt-2">
                                                <div class="avatar me-75">
                                                    @include('avatar', ['class' => 'avatar img', 'width' => 40])
                                                </div>
                                                <div class="profile-user-info">
                                                    <h6 class="mb-0 link-primary">
                                                        @can('view', $group)
                                                            <a href="{{ route('admin.groups.show', $group) }}">
                                                                {{ $group->group->name }}
                                                            </a>
                                                        @else
                                                            {{ $group->group->name }}
                                                        @endcan
                                                    </h6>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="col-xl-8 col-lg-8 col-md-7 ">
                            <div class="card">
                                <div class="table-responsive">
                                    <div class="card-header border-bottom p-1">
                                        <div class="head-label"><h4 class="card-header">الوثائق والأحداث</h4></div>
                                        @can('create', \App\Models\Document::class)
                                            <div class="dt-action-buttons text-end">
                                                <div class="dt-buttons d-inline-flex">
                                                    <a class="dt-button create-new btn btn-primary"
                                                       tabindex="0" target="_blank"
                                                       href="{{ route('admin.documents.create', ['for_user_id' => $user->id]) }}">
                                                        <span>
                                                            <i class="fa fa-sm fa-plus"></i>
                                                            إضافة
                                                        </span>
                                                    </a>
                                                </div>
                                            </div>
                                        @endcan
                                    </div>
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th>العنوان</th>
                                            <th class="text-nowrap">تاريخ الوثيقة/المناسبة</th>
                                            <th>الأشخاص المشار لهم</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @forelse($user->documents as $document)
                                            <tr>
                                                <td>
                                                    <div class="d-flex justify-content-left align-items-center">
                                                        <div class="avatar-wrapper">
                                                            <div class="avatar me-1">
                                                                @include('avatar', ['width' => 32])
                                                            </div>
                                                        </div>
                                                        <div class="d-flex flex-column">
                                                            @if(!empty($document->title))
                                                                <a href="#"
                                                                   @can('view', $document) class="ajax-view"
                                                                   data-url="{{ route('admin.documents.show', $document) }}" @endcan>
                                                                    <span class="text-truncate fw-bolder">
                                                                        {{ $document->title }}
                                                                    </span>
                                                                </a>
                                                                <small class="text-muted">
                                                                    @include('documents.components.icon', compact('document'))
                                                                    @include('documents.components.type', compact('document'))
                                                                </small>
                                                            @else
                                                                <a href="#"
                                                                   @can('view', $document) class="ajax-view"
                                                                   data-url="{{ route('admin.documents.show', $document) }}" @endcan>
                                                                    <span class="text-truncate fw-bolder">
                                                                        @include('documents.components.icon', compact('document'))
                                                                        @include('documents.components.type', compact('document'))
                                                                    </span>
                                                                </a>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    @include('documents.components.date', compact('document'))
                                                </td>
                                                <td>
                                                    <div class="avatar-group">
                                                        @foreach($document->users as $documentUser)
                                                            <div data-bs-toggle="tooltip" data-popup="tooltip-custom"
                                                                 data-bs-placement="top" class="avatar pull-up my-0"
                                                                 title=""
                                                                 data-bs-original-title="{{ $documentUser->getFullName(2, true) }}">
                                                                <x-user-avatar :url="$documentUser->profile_photo_url"
                                                                               width="26"/>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </td>
                                                <td>
                                                    @can('update', $document)
                                                        <a href="{{ route('admin.documents.edit', $document) }}"
                                                           class="text-body">
                                                            <i data-feather="edit"></i>
                                                        </a>
                                                    @endcan
                                                    {{--<a href="javascript:void(0);" class="text-body">
                                                        <i data-feather="trash"></i>
                                                    </a>--}}
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="4">
                                                    لا يوجد وثائق لهذا المستخدم
                                                </td>
                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card">
                                <div class="table-responsive">
                                    <div class="card-header border-bottom p-1">
                                        <div class="head-label">
                                            <h4 class="card-header">ساعات التطوع</h4>
                                        </div>
                                        @can('create', \App\Models\UserVoluntary::class)
                                            <div class="dt-action-buttons text-end">
                                                <div class="dt-buttons d-inline-flex">
                                                    <button class="dt-button create-new btn btn-primary" tabindex="0"
                                                            id="CreateUserVoluntaries">
                                                        <span>
                                                            <i class="fa fa-sm fa-plus"></i>
                                                            إضافة
                                                        </span>
                                                    </button>
                                                </div>
                                            </div>
                                        @endcan
                                    </div>

                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th>عدد الساعات</th>
                                            <th class="text-nowrap">تاريخ التطوع</th>
                                            <th>التفاصيل</th>
                                            <th aria-label="Actions">الإجراءات
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @forelse($user->voluntaries as $voluntary)
                                            <tr class="odd">
                                                <td>
                                                    <div class="d-flex justify-content-left align-items-center">
                                                        <div class="avatar-wrapper">
                                                            <div class="avatar me-1">
                                                                <i data-feather="clock"></i>
                                                            </div>
                                                        </div>
                                                        <div class="d-flex flex-column">
                                                            <span
                                                                    class="text-truncate fw-bolder">{{ $voluntary->total }}</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>{{ optional($voluntary->date)->toFormattedDateString() }}</td>
                                                <td>{{ $voluntary->caption }}</td>
                                                <td>
                                                    {{--<a data-bs-toggle="modal" class="text-body"
                                                       data-bs-target="#upgradePlanModal">
                                                        <i data-feather="edit"></i>
                                                    </a>--}}
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="4">
                                                    لا يوجد ساعات تطوع لهذا المستخدم
                                                </td>
                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane" id="userTree" aria-labelledby="userTree-tab" role="tabpanel">
                    <div class="row">
                        <div class="col-xl-5 col-lg-6 col-12">
                            <div class="row">
                                <div class="col mt-2">
                                    <div class="form-check form-check-inline">
                                        <input type="checkbox" class="form-check-input" id="autoUpdateTree"
                                               checked>
                                        <label class="form-check-label" for="autoUpdateTree">تحديث تلقائي
                                            للشجرة</label>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <button class="btn btn-primary"
                                            onclick="$.jstree.reference('#tree').refresh();document.getElementById('treeData').innerHTML = '';">
                                        تحديث
                                    </button>
                                </div>
                                <div class="col-12 w-75">
                                    <hr>
                                </div>
                                <div id="tree" class="col-12" style="font-size: 20px;"></div>
                            </div>
                        </div>
                        <div class="col-xl-7 col-lg-6 col-12" id="treeData"></div>
                        <div class="col-12 mt-4">
                            <div id="chartContainer" style="background: white;"
                                 data-users-index="{{ route('admin.users.index') }}"
                                 data-json-link="{{ route('admin.users.d3-json', $user) }}"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('vendor-style')
    <!-- vendor css files -->
    <link rel="stylesheet" href="{{ url(mix('vendors/custom/jstree/themes/default/style.css')) }}"/>
    @datepickerCSS()
@endsection
@section('page-style')
    {{-- Page Css files --}}
    <link rel="stylesheet" type="text/css" href="{{url(mix('css/base/plugins/forms/pickers/form-flat-pickr.css'))}}">
    <style>
        .jstree-anchor.trashed {
            text-decoration: line-through;
            color: #2196f3;
        }

        a.jstree-anchor {
            cursor: pointer;
        }

        .jstree-anchor.trashed button {
            display: none !important;
        }

        .jstree-anchor .drag-btn {
            display: none !important;
        }

        .jstree-icon.fas.fa-female.can-add + div > .jstree-btn-plus {
            display: initial !important;
        }

        .jstree-icon.non-family + div > .jstree-btn-plus {
            display: none !important;
        }

        /*.jstree-node .jstree-icon {
            display: none;
        }*/

        .jstree-node {
            background: none !important;
        }

        .jstree-node i.male-user {
            color: #2196f3;
        }

        .jstree-node i.female-user {
            color: #ff85dc;
        }

        .jstree-node i.male-user.non-family,
        .jstree-node i.female-user.non-family {
            color: #2e555a;
        }

        .jstree-node i.jstree-ocl-v2 {
            cursor: pointer;
            width: 20px;
            height: 32px;
            line-height: 32px;
            text-align: center;
        }

        .jstree-node.jstree-closed > i.jstree-ocl-v2:before {
            content: "\f053";
        }

        .jstree-node.jstree-open > i.jstree-ocl-v2:before {
            content: "\f078";
        }

        .jstree-node.jstree-leaf > i.jstree-ocl-v2:before {
            content: "";
        }

        .jstree-node.jstree-leaf {
            height: 32px;
        }

        .accordion-button {
            margin-left: 0 !important;
            margin-right: auto;
            height: 43px;
        }

        .selected-accordion {
            background-color: #f8f8f8;
        }

        #user_info td {
            min-width: 120px;
            padding-right: 8px;
        }

        .img-container img {
            max-width: 100%;
        }

        .cropper-view-box,
        .cropper-face {
            border-radius: 50%;
        }

        /* The css styles for `outline` do not follow `border-radius` on iOS/Safari (#979). */
        .cropper-view-box {
            outline: 0;
            box-shadow: 0 0 0 1px #39f;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css"/>
    <link rel="stylesheet" href="{{ asset('vendor/d3/index.css') }}">
    @livewireStyles
@endsection
@section('vendor-script')
    <script src="{{ url(mix('vendors/custom/jstree/jstree.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/extensions/jquery.inputmask.bundle.min.js')) }}"></script>
    @datepickerJS()
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.xyz/npm/d3-org-chart@2"></script>
    <script src="https://cdn.jsdelivr.xyz/npm/d3-flextree@2.1.2/build/d3-flextree.js"></script>
    @livewireScripts
    @livewireScriptConfig
@endsection
@section('page-script')
    <script>
        const WEB_API_USERS = '{!! signedRoute('admin.web-api.users', ['all' => true], now()->addHour()) !!}'
        const WEB_API_NON_FAMILY_USERS = '{!! signedRoute('admin.web-api.non-family-users', ['all' => true], now()->addHour())  !!}'
        const AVATAR_PLACEHOLDER_PATH = '{{ url(mix('images/portrait/small/avatar.png')) }}'
    </script>
    <script src="{{ asset('vendor/d3/index.js') }}"></script>
    <script src="{{ asset('js/pages/users/livewire-user-spouses.js') }}"></script>
    {{--<template id="ChildCreateForm">
        @include('users.tree.createUpdate', ['user' => null])
    </template>--}}
    <script>
        $(function () {
            /*$(window).resize(function () {
                var h = Math.max($(window).height() - 400, 420);
                $('#container, #data, #tree, #data .content').height(h).filter('.default').css('lineHeight', h + 'px');
            }).resize();*/
            let lastUserId,
                jsTree,
                treeInitialized = false,
                voluntariesFormModel = function (url, title, btnTitle) {
                    Swal.fire({
                        customClass: 'swal-wide',
                        showCloseButton: true,
                        confirmButtonText: btnTitle,
                        willOpen: () => {
                            Swal.showLoading();
                            $.ajax({
                                type: 'GET',
                                url: url,
                                success: function (res) {
                                    Swal.disableLoading();
                                    $(Swal.getHtmlContainer()).html(res).show();
                                    $(Swal.getTitle()).html(title).show();
                                },
                                error: function (res) {
                                    Swal.close();
                                    if (res.responseJSON.message || res.responseJSON.error)
                                        Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                }
                            });
                        },
                        showLoaderOnConfirm: true,
                        preConfirm: () => {
                            return new Promise((resolve) => {
                                $.ajax({
                                    type: 'POST',
                                    data: $('form', Swal.getHtmlContainer()).serializeArray(),
                                    url: $('form', Swal.getHtmlContainer()).data('url'),
                                    success: function (res) {
                                        $(Swal.getHtmlContainer()).html(res).show();
                                        resolve(false);
                                    },
                                    error: function (res) {
                                        if (res.responseJSON.message || res.responseJSON.error)
                                            Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                        resolve(false);
                                    }
                                });
                            });
                        },
                        didClose: () => {
                            //_table.api().ajax.reload(null, false);
                        }
                    });
                }
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
            })
            localStorage.removeItem('jstree');
            $(document).on('shown.bs.tab', 'a[data-bs-toggle="tab"]', function (event) {
                if (!treeInitialized && event.target.id === 'userTree-tab') {
                    treeInitialized = true;
                    jsTree = $('#tree').jstree({
                        'core': {
                            'data': {
                                'url': '{{ route('admin.users.tree') }}',
                                'data': function (node) {
                                    if (node.id === '#')
                                        return {'id': {{ $user->id }}, root: true};
                                    return {'id': node.id};
                                }
                            },
                            'check_callback': function (o, n, p, i, m) {
                                if (m && m.dnd && m.pos !== 'i')
                                    return false;
                                if (o === "copy_node")
                                    return false;
                                if (o === "move_node")
                                    if (this.get_node(n).parent === this.get_node(p).id || p.data.gender !== '{{ \App\Enums\Gender::Male }}')
                                        return false;
                                return true;
                            },
                            'themes': {
                                'responsive': false,
                                'variant': 'large',
                                'stripes': true
                            }
                        },
                        'types': {
                            'default': {'icon': 'folder'},
                            'file': {'valid_children': [], 'icon': 'file'}
                        },
                        'plugins': [
                            'dnd', 'state',
                        ]
                    })
                }
            })
            $('#tree')
                .on('click touchend', 'a.jstree-btn-view', function (e) {
                    e.stopPropagation();
                    e.preventDefault();
                    let elm = $(e.target).closest('.jstree-anchor'),
                        inst = $.jstree.reference(elm),
                        data = inst.get_node(elm),
                        userId = data.id;
                    if (data) {
                        let _html = '';
                        let userColumns = JSON.parse('{!! json_encode(trans('userColumns')) !!}');
                        $.each(data.data, function (k, v) {
                            if (v !== null && ['update_url', 'spouses_count'].indexOf(k) === -1) {
                                let kTitle = (k in userColumns) ? userColumns[k] : k;
                                switch (k) {
                                    case 'bio':
                                        _html += `<div class="col-12 mb-1"><label class="form-label" for="user_${k}">${kTitle}</label><textarea class="form-control" id="user_${k}" readonly>${v}</textarea></div>`;
                                        break;
                                    case 'profile_photo_url':
                                        _html += `<div class="col-auto"><a href="${v}" target="_blank"><img src="${v}" class="avatar img mb-1" width="64" /></a></div><div class="clearfix"></div>`;
                                        break;
                                    case 'family_user_id':
                                        _html += `<div class="col-md-6 col-12 mb-1"><label>${kTitle}</label><br><a href="{{ route('admin.users.index') }}/${data.id}" class="h3">${v}</a></div>`;
                                        break;
                                    case 'trashed':
                                        break;
                                    case 'spouses_url':
                                    @can('create', \App\Models\User::class)
                                        let text = 'الأزواج/الزوجات' + (data.data.spouses_count > 0 ? ` <small>(${data.data.spouses_count})</small>` : '');
                                        _html += `<div class="col-md-6 col-12 mb-1"><button class="btn btn-info jstree-form-btn" data-form-url=${v}>${text}</button></div>`;
                                        @else
                                            _html += '<div class="col-md-6 col-12 mb-1"></div>'
                                        @endcan
                                            break;
                                    default:
                                        _html += `<div class="col-md-6 col-12 mb-1"><label class="form-label" for="user_${k}">${kTitle}</label><input class="form-control" id="user_${k}" readonly value="${v}"></div>`;
                                        break;
                                }
                            }
                        })
                        $('#treeData').html(`<div class="row">${_html}</div>`);
                    } else {

                    }
                })
                .on('click touchend', 'a.jstree-btn-plus', function (e) {
                    e.stopPropagation();
                    e.preventDefault();
                    @cannot('create', \App\Models\User::class)
                    toastr.error('لا تمتلك صلاحية لإضافة المستخدمين !')
                    return;
                    @endcannot
                    let elm = $(e.target).closest('.jstree-anchor'),
                        inst = $.jstree.reference(elm),
                        eData = inst.get_node(elm),
                        userId = eData.id;
                    $.ajax({
                        type: 'get',
                        url: `{{ route('admin.users.index') }}/${userId}/children/create`,
                        success: function (res, s, e) {
                            $('#treeData').html(res).show();
                        },
                        error: function (res) {
                            if (res && res.responseJSON && (res.responseJSON.message || res.responseJSON.error))
                                toastr.error(res.responseJSON.message || res.responseJSON.error)
                            /* Swal.showValidationMessage(res && res.responseJSON && (res.responseJSON.message || res.responseJSON.error));*/
                        }
                    });
                })
                .on('click touchend', 'a.jstree-btn-edit', function (e) {
                    e.stopPropagation();
                    e.preventDefault();
                    let elm = $(e.target).closest('.jstree-anchor'),
                        inst = $.jstree.reference(elm),
                        eData = inst.get_node(elm),
                        updateUrl = eData.data.update_url;
                    if (updateUrl === null || updateUrl === undefined || updateUrl.length === 0)
                        return toastr.error('لا يمكن تعديل المستخدم !');
                    $.ajax({
                        type: 'get',
                        url: updateUrl,
                        success: function (res, s, e) {
                            $('#treeData').html(res).show();
                        },
                        error: function (res) {
                            if (res && res.responseJSON && (res.responseJSON.message || res.responseJSON.error))
                                toastr.error(res.responseJSON.message || res.responseJSON.error)
                        }
                    });
                });
            $(document.body).on('click', 'a.jstree-form-btn,button.jstree-form-btn', function (e) {
                e.stopPropagation();
                e.preventDefault();
                let url = $(e.target).data('formUrl');
                if (url !== undefined)
                    $.ajax({
                        type: 'get',
                        url: url,
                        success: function (res, s, e) {
                            $('#treeData').html(res).show();
                        },
                        error: function (res) {
                            if (res && res.responseJSON && (res.responseJSON.message || res.responseJSON.error))
                                toastr.error(res.responseJSON.message || res.responseJSON.error)
                            /* Swal.showValidationMessage(res && res.responseJSON && (res.responseJSON.message || res.responseJSON.error));*/
                        }
                    });
            })
            $('#CreateUserVoluntaries').click(function (e) {
                e.preventDefault()
                voluntariesFormModel('{{ route('admin.user-voluntaries.create', ['for_user_id' => $user->id]) }}', 'إضافة ساعات تطوع جديدة', 'إضافة');
            })
            window.addEventListener('refresh-tree', event => {
                $.jstree.reference('#tree').refresh()
                $('#NonFamilyCreateForm').remove()
            })
        })
    </script>
@endsection
