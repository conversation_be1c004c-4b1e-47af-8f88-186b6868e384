@php
    $rnd = \Illuminate\Support\Str::random();
@endphp
<div class="col-md-12" style="text-align: right">
    @if(isset($message) && !empty($message))
        <div id="{{$rnd}}_alert" class="alert alert-info col-12 mt-2 p-1">
            {{$message}}
            @isset($newShortURL)
                <br>
                <br>
                <a href="{{ $newShortURL->default_short_url }}" target="_blank">
                    {{ $newShortURL->default_short_url }}
                    <i class="fa fa-arrow-up-right-from-square"></i>
                </a>
            @endif
        </div>
    @endif
    <form class="form-horizontal row swal-submit-form" id="{{$rnd}}_form" method="POST"
          data-url="{{ isset($shortURL) ? route('admin.short-urls.update', $shortURL) : route('admin.short-urls.store') }}">
        @isset($shortURL)
            @method('PUT')

            @if(request()->isMethod('get') || $errors->isEmpty())
                <div class="form-group col-12">
                    <label class="form-label" for="{{$rnd}}_default_short_url">الرابط المختصر</label>
                    <input id="{{$rnd}}_default_short_url" name="default_short_url" type="text"
                           placeholder="الرابط المختصر"
                           autocomplete="off" style="text-align: left;direction: ltr;" disabled
                           value="{{  optional($shortURL)->default_short_url }}" class="form-control input-md">
                </div>
            @endif
        @endif
        <div class="form-group col-12">
            <label class="form-label" for="{{$rnd}}_destination_url">الرابط</label>
            <input id="{{$rnd}}_destination_url" name="destination_url" type="text" placeholder="https://altuwajri.info/"
                   autocomplete="off" style="text-align: left;direction: ltr;"
                   value="{{ (request()->isMethod('post') && $errors->isNotEmpty()) ? request('destination_url') : optional(@$shortURL)->destination_url }}"
                   class="form-control input-md @error('destination_url') is-invalid @enderror">
            @error('destination_url')
            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <div class="form-group col-md-6 col-12">
            <label class="form-label" for="{{$rnd}}_url_key">الاختصار</label>
            <input id="{{$rnd}}_url_key" name="url_key" type="text" placeholder="الاختصار" autocomplete="off"
                   value="{{ (request()->isMethod('post') && $errors->isNotEmpty()) ? request('url_key') : optional(@$shortURL)->url_key }}"
                   class="form-control input-md @error('url_key') is-invalid @enderror">
            @error('url_key')
            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <div class="form-group col-md-6 col-12">
            <label class="form-label" for="{{$rnd}}_title">الاسم</label>
            <input id="{{$rnd}}_title" name="title" type="text" placeholder="الاسم" autocomplete="off"
                   value="{{ (request()->isMethod('post') && $errors->isNotEmpty()) ? request('title') : optional(@$shortURL)->title }}"
                   class="form-control input-md @error('title') is-invalid @enderror">
            @error('title')
            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        {{--<div class="form-group col-12">
            <label class="form-label" for="{{$rnd}}_deactivateAt">تاريخ الانتهاء</label>
            <input id="{{$rnd}}_deactivateAt" name="deactivate_at" type="text" placeholder="تاريخ الانتهاء" autocomplete="off"
                   value="{{ (request()->isMethod('post') && $errors->isNotEmpty()) ? request('deactivate_at') : '' }}"
                   class="form-control input-md @error('deactivate_at') is-invalid @enderror">
            @error('deactivate_at')
            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
            @enderror
        </div>--}}

        <div class="col-md-3 col-6 mt-2">
            <div class="form-check form-check-inline">
                <input type="checkbox" class="form-check-input" id="{{ $rnd }}_trackVisits" name="track_visits"
                        @checked((request()->isMethod('post') && $errors->isNotEmpty()) ? request()->has('track_visits') : (optional(@$shortURL)->track_visits ?? true))>
                <label class="form-check-label" for="{{ $rnd }}_trackVisits">تتبع الزيارات</label>
            </div>
        </div>

        <div class="col-md-3 col-6 mt-2">
            <div class="form-check form-check-inline">
                <input type="checkbox" class="form-check-input" id="{{ $rnd }}_singleUse" name="single_use"
                        @disabled(optional(@$shortURL)->single_use)
                        @checked((request()->isMethod('post') && $errors->isNotEmpty()) ? request()->has('single_use') : optional(@$shortURL)->single_use)>
                <label class="form-check-label" for="{{ $rnd }}_singleUse">استخدام مرة واحدة</label>
            </div>
        </div>

        <input type="submit" style="display: none;">
    </form>
</div>

<script>
    $(function () {
        $('#{{$rnd}}_deactivateAt').hijriDatePicker({
            ...datepickerOptions,
            minDate: "{{ now()->addDay()->toDateString() }}"
        })
        $('#{{$rnd}}_form').submit(function (e) {
            e.preventDefault();
            swal.clickConfirm();
        });
    });
</script>
