@php
    $rnd = \Illuminate\Support\Str::random();
@endphp
<div>
    {{--<div class="w-100">
        <div class="card card-browser-states">
            <div class="card-header">
                <div><h4 class="card-title">المتصفحات</h4></div>
            </div>
            <div class="card-body">
                @foreach($browserVisits as $browserVisit)
                    <div class="browser-states">
                        <div class="d-flex">
                            <img src="{{asset('images/browser/' . strtolower($browserVisit['name']) . '.png')}}"
                                 class="rounded me-1" height="30" alt="{{ $browserVisit['name'] }}"/>
                            <h6 class="align-self-center mb-0">{{ $browserVisit['name'] }}</h6>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="fw-bold text-body-heading me-1">{{ round($browserVisit['percent'], 2) }}%</div>
                            <div class="state-chart" data-percent="{{ $browserVisit['percent'] }}"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <div class="w-100">
        <div class="card card-browser-states">
            <div class="card-header">
                <div><h4 class="card-title">النظام</h4></div>
            </div>
            <div class="card-body">
                @foreach($osVisits as $osVisit)
                    <div class="browser-states">
                        <div class="d-flex">
                            --}}{{--<img src="{{asset('images/os/' . strtolower($osVisit['name']) . '.png')}}"
                                 class="rounded me-1" height="30" alt="{{ $osVisit['name'] }}"/>--}}{{--
                            <h6 class="align-self-center mb-0">{{ $osVisit['name'] }}</h6>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="fw-bold text-body-heading me-1">{{ round($osVisit['percent'], 2) }}%</div>
                            <div class="state-chart" data-percent="{{ $osVisit['percent'] }}"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>--}}
    {{--<svg width="600" height="400" id="{{ $rnd }}_CHART"></svg>--}}
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex flex-sm-row flex-column justify-content-md-between align-items-start justify-content-start">
                <div>
                    <h4 class="card-title mb-75">الزيارات</h4>
                </div>
                {{--<div class="d-flex align-items-center flex-wrap mt-sm-0 mt-1">
                    <h5 class="fw-bolder mb-0 me-1">{{ $visits->count() }}</h5>
                </div>--}}
            </div>
            <div class="card-body">
                <div id="visits-chart"></div>
            </div>
        </div>
    </div>
    <div class="col-12">
        <div class="card">
            <div class="card-header flex-column align-items-start">
                <h4 class="card-title mb-75">المتصفحات</h4>
            </div>
            <div class="card-body">
                <div id="browsers-chart"></div>
            </div>
        </div>
    </div>
    <div class="col-12">
        <div class="card">
            <div class="card-header flex-column align-items-start">
                <h4 class="card-title mb-75">النظام</h4>
            </div>
            <div class="card-body">
                <div id="os-chart"></div>
            </div>
        </div>
    </div>
</div>
<script>
    $(function () {
        let browsersChartEl = document.querySelector('#browsers-chart'),
            osChartEl = document.querySelector('#os-chart'),
            visitsChartEl = document.querySelector('#visits-chart'),
            browsersChartConfig = {
                chart: {
                    height: 350,
                    width: '100%',
                    type: 'donut'
                },
                legend: {
                    show: true,
                    position: 'bottom',
                    formatter: function (val, opts) {
                        return val + ` (${opts.w.globals.series[opts.seriesIndex]})`
                    }
                },
                labels: @json($browserVisits->pluck('name')->values()),
                series: @json($browserVisits->pluck('value')->values()),
                fill: {
                    type: 'gradient',
                },
                dataLabels: {
                    enabled: true,
                    formatter: function (val, opt) {
                        return parseInt(val) + '%';
                    }
                },
                plotOptions: {
                    pie: {
                        donut: {
                            labels: {
                                show: true,
                                name: {
                                    fontSize: '2rem',
                                    fontFamily: 'Montserrat'
                                },
                                value: {
                                    fontSize: '1rem',
                                    fontFamily: 'Montserrat',
                                    formatter: (val) => parseInt(val)
                                },
                                total: {
                                    show: true,
                                    fontSize: '1.5rem',
                                    label: 'الإجمالي',
                                    formatter: function (w) {
                                        return '{{ $visits->count() }}';
                                    }
                                }
                            }
                        }
                    }
                },
                responsive: [
                    {
                        breakpoint: 992,
                        options: {
                            chart: {
                                height: 380
                            }
                        }
                    },
                    {
                        breakpoint: 576,
                        options: {
                            chart: {
                                height: 320
                            },
                            plotOptions: {
                                pie: {
                                    donut: {
                                        labels: {
                                            show: true,
                                            name: {
                                                fontSize: '1.5rem'
                                            },
                                            value: {
                                                fontSize: '1rem'
                                            },
                                            total: {
                                                fontSize: '1.5rem'
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                ]
            },
            osChartConfig = {
                chart: {
                    height: 350,
                    width: '100%',
                    type: 'donut'
                },
                legend: {
                    show: true,
                    position: 'bottom',
                    formatter: function (val, opts) {
                        return val + ` (${opts.w.globals.series[opts.seriesIndex]})`
                    }
                },
                labels: @json($osVisits->pluck('name')->values()),
                series: @json($osVisits->pluck('value')->values()),
                fill: {
                    type: 'gradient',
                },
                dataLabels: {
                    enabled: true,
                    formatter: function (val, opt) {
                        return parseInt(val) + '%';
                    }
                },
                plotOptions: {
                    pie: {
                        donut: {
                            labels: {
                                show: true,
                                name: {
                                    fontSize: '2rem',
                                    fontFamily: 'Montserrat'
                                },
                                value: {
                                    fontSize: '1rem',
                                    fontFamily: 'Montserrat',
                                    formatter: (val) => parseInt(val)
                                },
                                total: {
                                    show: true,
                                    fontSize: '1.5rem',
                                    label: 'الإجمالي',
                                    formatter: function (w) {
                                        return '{{ $visits->count() }}';
                                    }
                                }
                            }
                        }
                    }
                },
                responsive: [
                    {
                        breakpoint: 992,
                        options: {
                            chart: {
                                height: 380
                            }
                        }
                    },
                    {
                        breakpoint: 576,
                        options: {
                            chart: {
                                height: 320
                            },
                            plotOptions: {
                                pie: {
                                    donut: {
                                        labels: {
                                            show: true,
                                            name: {
                                                fontSize: '1.5rem'
                                            },
                                            value: {
                                                fontSize: '1rem'
                                            },
                                            total: {
                                                fontSize: '1.5rem'
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                ]
            },
            visitsChartConfig = {
                chart: {
                    height: 400,
                    type: 'line',
                    zoom: {
                        enabled: false
                    },
                    parentHeightOffset: 0,
                    toolbar: {
                        show: false
                    }
                },
                series: [
                    {
                        data: @json($dayVisits->pluck('value'))
                    }
                ],
                markers: {
                    strokeWidth: 7,
                    strokeOpacity: 1,
                    strokeColors: [window.colors.solid.white],
                    colors: [window.colors.solid.warning]
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'straight'
                },
                colors: [window.colors.solid.warning],
                grid: {
                    xaxis: {
                        lines: {
                            show: true
                        }
                    },
                    padding: {
                        top: -20
                    }
                },
                tooltip: {
                    custom: function (data) {
                        return (
                            '<div class="px-1 py-50">' +
                            '<span>' + data.series[data.seriesIndex][data.dataPointIndex] + '</span>' +
                            '</div>'
                        );
                    }
                },
                xaxis: {
                    categories: @json($dayVisits->pluck('name'))
                },
                yaxis: {
                    opposite: false
                }
            };
        if (typeof browsersChartEl !== undefined && browsersChartEl !== null) {
            let browsersChart = new ApexCharts(browsersChartEl, browsersChartConfig);
            browsersChart.render();
        }
        if (typeof osChartEl !== undefined && osChartEl !== null) {
            let osChart = new ApexCharts(osChartEl, osChartConfig);
            osChart.render();
        }
        if (typeof visitsChartEl !== undefined && visitsChartEl !== null) {
            let visitsChart = new ApexCharts(visitsChartEl, visitsChartConfig);
            visitsChart.render();
        }

        /*let $stateCharts = document.querySelectorAll('.state-chart');
        $stateCharts.forEach(($stateChart) => new ApexCharts($stateChart, {
            chart: {
                height: 30,
                width: 30,
                type: 'radialBar'
            },
            grid: {
                show: false,
                padding: {
                    left: -15,
                    right: -15,
                    top: -12,
                    bottom: -15
                }
            },
            colors: [window.colors.solid.primary],
            series: [$($stateChart).data('percent')],
            plotOptions: {
                radialBar: {
                    hollow: {
                        size: '22%'
                    },
                    track: {
                        background: '#EBEBEB'
                    },
                    dataLabels: {
                        showOn: 'always',
                        name: {
                            show: false
                        },
                        value: {
                            show: false
                        }
                    }
                }
            },
            stroke: {
                lineCap: 'round'
            }
        }).render())*/

        /*var data = @json($browserVisits);

        var svg = d3.select(document.querySelector('#{{ $rnd }}_CHART')),
            //width = svg.attr('width'),
            //height = svg.attr('height'),
            width = 600,
            height = Math.min(width, 350),
            radius = Math.min(width, height) / 2 - 1,
            g = svg.append('g').attr('transform', 'translate(' + width / 2 + ',' + height / 2 + ')');

        const color = d3.scaleOrdinal()
            .domain(data.map((d, i) => d.name))
            .range(d3.quantize(t => d3.interpolateSpectral(t * 0.8 + 0.1), data.length).reverse())


        // Generate the pie
        const pie = d3.pie()
            .sort(null)
            .value(d => d.value);

        // Generate the arcs
        var arc = d3.arc()
            .innerRadius(0)
            .outerRadius(radius);

        const labelRadius = arc.outerRadius()() * 0.8;

        // A separate arc generator for labels.
        const arcLabel = d3.arc()
            .innerRadius(labelRadius)
            .outerRadius(labelRadius);

        //Generate groups
        var arcs = g.selectAll('arc')
            .data(pie(data))
            .enter()
            .append('g')
            .attr('class', 'arc')

        //Draw arc paths
        arcs.append('path')
            .attr('fill', (d, i) => color(d.data.name))
            .attr('d', arc)
            .append('title')
            .text(d => `Test`);

        arcs.append("g")
            .attr("text-anchor", "middle")
            .selectAll()
            .data(pie(data))
            .join("text")
            .attr("transform", d => `translate(${arcLabel.centroid(d)})`)
            .call(text => text.append("tspan")
                .attr("y", "-0.4em")
                .attr("font-weight", "bold")
                .text(d => d.data.name))
            .call(text => text.filter(d => (d.endAngle - d.startAngle) > 0.25).append("tspan")
                .attr("x", 0)
                .attr("y", "0.7em")
                .attr("fill-opacity", 0.7)
                .text(d => `${d.data.percent}%`));*/
    })
</script>
