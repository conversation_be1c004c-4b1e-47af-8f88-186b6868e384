@php
    $rnd = \Illuminate\Support\Str::random();
    $oldUserId = (!request()->isMethod('get') && $errors->isNotEmpty()) ? request('user_id') : @$hallReservation->user_id;
    if(is_null($oldUserId) && request()->has('for_user_id'))
        $oldUserId = request('for_user_id');
    $oldUser = !is_null($oldUserId) ? \App\Models\User::withoutGlobalScopes(['family_users'])->find($oldUserId) : null;
    $services = \App\Models\Service::all();
    $servicesData = collect([]);
    if(!request()->isMethod('get') && $errors->isNotEmpty())
        $servicesData = collect(request('services', []))
        ->reject(fn($d) => !isset($d['enable']))
        ->map(fn($d) => Arr::only($d, ['service_id', 'notes']))
        ->values();
    elseif(isset($hallReservation))
        $servicesData = $hallReservation->services->map(fn($service) => ['service_id' => $service->id, 'notes' => $service->pivot->notes]);
@endphp
<div class="col-md-12" style="text-align: right">
    @if(isset($message) && !empty($message))
        <div id="{{$rnd}}_alert" class="alert alert-info col-12 mt-2 p-1">{{$message}}</div>
    @endif
    <form class="form-horizontal row swal-submit-form" id="{{$rnd}}_form" method="post"
          data-url="{{ isset($hallReservation) ? route('admin.hall-reservations.update', $hallReservation) : route('admin.hall-reservations.store') }}">
        @isset($hallReservation)
            @method('put')
            <div class="form-group col-lg-6 col-12">
                <label class="form-label" for="{{$rnd}}_user_name">@lang('hallReservationColumns.user_id')</label>
                <input id="{{$rnd}}_user_name" placeholder="@lang('hallReservationColumns.user_id')"
                       value="{{ @$hallReservation->user->getFullName(2) }}" disabled
                       class="form-control input-md">
            </div>
            @isset($hallReservation->user->phone)
                <div class="form-group col-lg-6 col-12">
                    <label class="form-label" for="{{$rnd}}_phone">@lang('userColumns.phone')</label>
                    <input id="{{$rnd}}_phone" placeholder="@lang('userColumns.phone')"
                           value="{{ @$hallReservation->user->phone }}" disabled
                           class="form-control input-md">
                </div>
            @endisset
        @else
            <div class="form-group col-lg-6 col-12">
                <label class="form-label" for="{{$rnd}}_user_id">المستخدم</label>
                <select class="form-control" name="user_id" id="{{$rnd}}_user_id"
                        data-minimum-results-for-search="2" data-allow-clear="true" data-placeholder="المستخدم">
                    <option value=""></option>
                    @if(isset($oldUser) && !is_null($oldUser))
                        <option selected value="{{$oldUser->id}}">{{$oldUser->identifier}}</option>
                    @endif
                </select>
                @error('user_id')
                <span class="invalid-feedback" role="alert" style="display: block;">{{ $message }}</span>
                @enderror
            </div>
        @endisset
        <div class="form-group col-lg-6 col-12 mb-1">
            <label for="{{$rnd}}_event_type" class="form-label">@lang('hallReservationColumns.event_type')</label>
            @php($event_type = $errors->isNotEmpty() ? request('event_type') : optional(@$hallReservation)->event_type)
            <select class="select2 form-select @error('event_type') is-invalid @enderror"
                    data-minimum-results-for-search="-1" data-placeholder="@lang('hallReservationColumns.event_type')"
                    name="event_type" id="{{$rnd}}_event_type">
                @foreach(\App\Enums\HallEventType::all() as $type)
                    <option value="{{ $type }}" @selected($event_type === $type)>@lang("hallReservationColumns.event_type.$type")</option>
                @endforeach
            </select>
            @error('event_type')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="form-group col-lg-6 col-12 mb-1">
            <label for="{{$rnd}}_status" class="form-label">@lang('hallReservationColumns.status')</label>
            @php($_status = $errors->isNotEmpty() ? request('status') : optional(@$hallReservation)->status)
            <select class="select2 form-select @error('status') is-invalid @enderror"
                    data-minimum-results-for-search="-1" data-placeholder="@lang('hallReservationColumns.status')"
                    name="status" id="{{$rnd}}_status">
                @foreach(\App\Enums\HallReservationStatus::all() as $status)
                    <option value="{{ $status }}" @selected($_status === $status)>@lang("hallReservationColumns.status.$status")</option>
                @endforeach
            </select>
            @error('status')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="form-group col-lg-6 col-12">
            <label class="form-label" for="{{$rnd}}_date">@lang('hallReservationColumns.date')</label>
            <input class="form-control @error('date') is-invalid @enderror" id="{{$rnd}}_date"
                   placeholder="@lang('hallReservationColumns.date')" autocomplete="off" required name="date"
                   value="{{ (!request()->isMethod('get') && $errors->isNotEmpty()) ? request('date') : optional(@$hallReservation->start_at)->format('Y-m-d') }}"/>
            @error('date')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="clearfix"></div>
        <div class="form-group col-lg-6 col-12">
            <label class="form-label" for="{{$rnd}}_time_start">@lang('hallReservationColumns.time_start')</label>
            <input class="form-control @error('time_start') is-invalid @enderror" id="{{$rnd}}_time_start"
                   placeholder="@lang('hallReservationColumns.time_start')" autocomplete="off" required
                   name="time_start"
                   value="{{ (!request()->isMethod('get') && $errors->isNotEmpty()) ? request('time_start') : (optional(@$hallReservation->start_at)->format('h:i A') ?: '12:00 PM') }}"/>
            @error('time_start')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="form-group col-lg-6 col-12">
            <label class="form-label" for="{{$rnd}}_time_end">@lang('hallReservationColumns.time_end')</label>
            <input class="form-control @error('time_end') is-invalid @enderror" id="{{$rnd}}_time_end"
                   placeholder="@lang('hallReservationColumns.time_end')" autocomplete="off" required name="time_end"
                   value="{{ (!request()->isMethod('get') && $errors->isNotEmpty()) ? request('time_end') : (optional(optional(@$hallReservation->end_at)->addSecond())->format('h:i A') ?: '12:00 PM') }}"/>
            @error('time_end')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-12 mb-1 mt-1">
            <label class="form-label" for="{{$rnd}}_details">@lang('hallReservationColumns.details')</label>
            <textarea type="text" class="form-control @error('details') is-invalid @enderror"
                      id="{{$rnd}}_details" placeholder="@lang('hallReservationColumns.details')"
                      maxlength="1024" name="details" autocomplete="off"
                      style="height: 80px;min-height: 80px;max-height: 120px;"
                      required>{{ (!request()->isMethod('get') && $errors->isNotEmpty()) ? request('details') : @$hallReservation->details }}</textarea>
            @error('details')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        @if($services->count() > 0)
            <div class="col-12">
                <table class="w-100">
                    @foreach($services as $service)
                        <tr class="pb-25">
                            <td>
                                <input type="hidden" name="services[{{ $service->id }}][service_id]"
                                       value="{{ $service->id }}">
                                <div class="form-check form-check-inline mt-50">
                                    <input type="checkbox" class="form-check-input service-checkbox"
                                           @checked($servicesData->pluck('service_id')->contains($service->id))
                                           data-service-id="{{ $service->id }}"
                                           name="services[{{ $service->id }}][enable]"
                                           id="ServiceEnable{{ $service->id }}"/>
                                    <label class="form-check-label"
                                           for="ServiceEnable{{ $service->id }}"> {{ $service->title }} </label>
                                </div>
                            </td>
                            <td>
                                <div class="form-group">
                                    <input class="form-control @error("services.$service->id.notes") is-invalid @enderror"
                                           id="ServiceNote{{ $service->id }}"
                                           @disabled(!$servicesData->pluck('service_id')->contains($service->id))
                                           name="services[{{ $service->id }}][notes]"
                                           value="{{ @$servicesData->pluck('notes', 'service_id')->toArray()[$service->id] }}"
                                           placeholder="ملاحظات" aria-label="notes"/>
                                    @error("services.$service->id.notes")
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </table>
            </div>
        @endif
        <input type="submit" style="display: none;">
    </form>
</div>

<script>
    $(function () {
        $('#{{$rnd}}_form').closest('.swal2-html-container').css('z-index', 2)
        let UserId = $('#{{$rnd}}_user_id')[0];
        $('#{{$rnd}}_date').hijriDatePicker(datepickerOptions)
        let initPickers = function () {
            $('#{{$rnd}}_time_start').flatpickr({
                enableTime: true,
                noCalendar: true,
                dateFormat: "G:i K",
                disableMobile: true,
                locale: 'ar',
            })
            $('#{{$rnd}}_time_end').flatpickr({
                enableTime: true,
                noCalendar: true,
                dateFormat: "G:i K",
                disableMobile: true,
                locale: 'ar',
            })
        }
        if (UserId) {
            $(UserId).select2({
                // the following code is used to disable x-scrollbar when click in select input and
                // take 100% width in responsive also
                theme: 'bootstrap-5',
                //dropdownAutoWidth: true,
                width: '100%',
                dropdownParent: UserId.parentElement,
                ajax: {
                    url: "{!! signedRoute('admin.web-api.users', ['with-non-family' => true], now()->addHour()) !!}",
                    dataType: 'json',
                    method: 'post',
                    delay: 250,
                    cache: true,
                    data: function (params) {
                        return {
                            q: params.term,
                            _token: '{{ csrf_token() }}',
                            page: params.page
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data,
                            pagination: {
                                more: (params.page * 15) < data.total
                            }
                        };
                    }
                },
                escapeMarkup: markup => markup,
                minimumInputLength: 1,
                templateResult: function (user) {
                    if (user.loading)
                        return user.text;
                    let markup = "<div class='select2-result-fabricsitory clearfix d-flex'>" +
                        "<div class='select2-result-fabricsitory__meta'>" +
                        "<div class='select2-result-fabricsitory__title fs-lg fw-500'>" + (user.full_name || user.name)
                        + (user.branch_name ? ` (${user.branch_name})` : '') + "</div>";
                    if (user.family_user_id)
                        markup += "<div class='select2-result-fabricsitory__description fs-xs opacity-80 mb-1'>" + user.family_user_id + "</div>";
                    markup += "</div></div>";
                    return markup;
                },
                templateSelection: function (user) {
                    return user.text ? user.text : `#${user.family_user_id} ${(user.full_name || user.name)}`;
                },
            }).on("select2:clear", function (e) {
                @if(!isset($familyCase))
                $('#familyCaseNationalId:disabled,#familyCasePhone:disabled,#familyCaseDOB:disabled,#familyCaseGender:disabled').removeAttr('disabled').val('');
                @endif
            }).on("select2:select", function (e) {
                let data = e.params.data;
                $('#familyCaseNationalId:disabled,#familyCasePhone:disabled,#familyCaseDOB:disabled,#familyCaseGender:disabled').removeAttr('disabled').val('');
                if (data.national_id)
                    $('#familyCaseNationalId').attr('disabled', 'true').val(data.national_id)
                if (data.phone)
                    $('#familyCasePhone').attr('disabled', 'true').val(data.phone)
                if (data.dob)
                    $('#familyCaseDOB').attr('disabled', 'true').val(data.dob)
                if (data.gender)
                    $('#familyCaseGender').attr('disabled', 'true').val(data.gender)
            });
            $($(UserId).data('select2').$dropdown).css('z-index', '9001')/*.css('position', 'revert')*/;
        }
        $('select.select2', '#{{$rnd}}_form').select2({theme: 'bootstrap-5'}).each(function (k, v) {
            $($(v).data('select2').$dropdown).css('z-index', '9001');
        });
        $('#{{$rnd}}_form').submit(function (e) {
            e.preventDefault();
            swal.clickConfirm();
        });
        $('#{{$rnd}}_form').on('change', '.service-checkbox', function () {
            let serviceId = $(this).data('serviceId'),
                _elm = $(`#ServiceNote${serviceId}`);
            if (this.checked)
                _elm.removeAttr('disabled');
            else {
                _elm.attr('disabled', true).val('');
            }
        });
        $('#{{$rnd}}_event_type').change(function (e) {
            if (e.target.value === '{{ \App\Enums\HallEventType::Marriage }}') {
                if ($('#{{$rnd}}_time_start')[0]._flatpickr !== undefined)
                    $('#{{$rnd}}_time_start')[0]._flatpickr.destroy();
                if ($('#{{$rnd}}_time_end')[0]._flatpickr !== undefined)
                    $('#{{$rnd}}_time_end')[0]._flatpickr.destroy();
                $('#{{$rnd}}_time_start').attr('readonly', true).val('06:30 PM');
                $('#{{$rnd}}_time_end').attr('readonly', true).val('11:59 PM');
            } else {
                initPickers()
            }
        }).trigger('change');
        setTimeout(function () {
            $('#{{$rnd}}_alert').hide(function () {
                $(this).remove();
            })
        }, 5000);
    });
</script>
