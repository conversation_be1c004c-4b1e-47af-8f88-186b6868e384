@extends('layouts.contentLayoutMaster')

@section('title', 'حجز القاعة')

@section('vendor-style')
    <!-- Vendor css files -->
    <link rel="stylesheet" href="{{ url(mix('vendors/css/calendars/fullcalendar.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/forms/select/select2.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/pickers/flatpickr/flatpickr.css')) }}">
    @datepickerCSS()
@endsection

@section('page-style')
    <!-- Page css files -->
    <link rel="stylesheet" href="{{ url(mix('css/base/plugins/forms/pickers/form-flat-pickr.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('css/base/pages/app-calendar.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('css/base/plugins/forms/form-validation.css')) }}">
@endsection

@section('content')
    <!-- Full calendar start -->
    <section>
        <div class="app-calendar overflow-hidden border">
            <div class="row g-0">
                <!-- Sidebar -->
                <div class="col app-calendar-sidebar flex-grow-0 overflow-hidden d-flex flex-column"
                     id="app-calendar-sidebar">
                    <div class="sidebar-wrapper">
                        <div class="card-body d-flex justify-content-center">
                            <button class="btn btn-primary w-100" id="NewReservation">
                                <span class="align-middle">إضافة جديد</span>
                            </button>
                        </div>
                        <div class="card-body pb-0">
                            <h5 class="section-label mb-1">
                                <span class="align-middle">@lang('hallReservationColumns.event_type')</span>
                            </h5>
                            <div class="form-check form-check-primary mb-1">
                                <input type="checkbox" class="form-check-input select-all" id="select-all" checked/>
                                <label class="form-check-label" for="select-all">الكل</label>
                            </div>
                            <div class="calendar-events-filter">
                                <div class="form-check form-check-info mb-1">
                                    <input type="checkbox" class="form-check-input input-filter"
                                           id="{{ \App\Enums\HallEventType::Meeting }}"
                                           data-value="{{ \App\Enums\HallEventType::Meeting }}" checked/>
                                    <label class="form-check-label" for="{{ \App\Enums\HallEventType::Meeting }}">
                                        @lang("hallReservationColumns.event_type." . \App\Enums\HallEventType::Meeting)
                                    </label>
                                </div>
                                <div class="form-check form-check-warning mb-1">
                                    <input type="checkbox" class="form-check-input input-filter"
                                           id="{{ \App\Enums\HallEventType::Marriage }}"
                                           data-value="{{ \App\Enums\HallEventType::Marriage }}" checked/>
                                    <label class="form-check-label" for="{{ \App\Enums\HallEventType::Marriage }}">
                                        @lang("hallReservationColumns.event_type." . \App\Enums\HallEventType::Marriage)
                                    </label>
                                </div>
                            </div>
                            <hr>
                            <h5 class="section-label mb-1">
                                <span class="align-middle">@lang('hallReservationColumns.status')</span>
                            </h5>
                            <div class="form-check form-check-secondary mb-1">
                                <input type="checkbox" class="form-check-input status-select-all" id="status-select-all"
                                       checked/>
                                <label class="form-check-label" for="status-select-all">الكل</label>
                            </div>
                            <div class="calendar-events-status-filter">
                                <div class="form-check form-check-secondary mb-1">
                                    <input type="checkbox" class="form-check-input status-filter"
                                           id="{{ \App\Enums\HallReservationStatus::Pending }}"
                                           data-value="{{ \App\Enums\HallReservationStatus::Pending }}" checked/>
                                    <label class="form-check-label"
                                           for="{{ \App\Enums\HallReservationStatus::Pending }}">
                                        @lang("hallReservationColumns.status." . \App\Enums\HallReservationStatus::Pending)
                                    </label>
                                </div>
                                <div class="form-check form-check-secondary mb-1">
                                    <input type="checkbox" class="form-check-input status-filter"
                                           id="{{ \App\Enums\HallReservationStatus::Completed }}"
                                           data-value="{{ \App\Enums\HallReservationStatus::Completed }}" checked/>
                                    <label class="form-check-label"
                                           for="{{ \App\Enums\HallReservationStatus::Completed }}">
                                        @lang("hallReservationColumns.status." . \App\Enums\HallReservationStatus::Completed)
                                    </label>
                                </div>
                                <div class="form-check form-check-secondary mb-1">
                                    <input type="checkbox" class="form-check-input status-filter"
                                           id="{{ \App\Enums\HallReservationStatus::Confirmed }}"
                                           data-value="{{ \App\Enums\HallReservationStatus::Confirmed }}" checked/>
                                    <label class="form-check-label"
                                           for="{{ \App\Enums\HallReservationStatus::Confirmed }}">
                                        @lang("hallReservationColumns.status." . \App\Enums\HallReservationStatus::Confirmed)
                                    </label>
                                </div>
                                <div class="form-check form-check-secondary mb-1">
                                    <input type="checkbox" class="form-check-input status-filter"
                                           id="{{ \App\Enums\HallReservationStatus::Cancelled }}"
                                           data-value="{{ \App\Enums\HallReservationStatus::Cancelled }}" checked/>
                                    <label class="form-check-label"
                                           for="{{ \App\Enums\HallReservationStatus::Cancelled }}">
                                        @lang("hallReservationColumns.status." . \App\Enums\HallReservationStatus::Cancelled)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /Sidebar -->

                <!-- Calendar -->
                <div class="col position-relative">
                    <div class="card shadow-none border-0 mb-0 rounded-0">
                        <div class="card-body pb-0">
                            <div id="calendar"></div>
                        </div>
                    </div>
                </div>
                <!-- /Calendar -->
            </div>
        </div>
    </section>
    <!-- Full calendar end -->
@endsection

@section('vendor-script')
    <!-- Vendor js files -->
    <script src="{{ url(mix('vendors/js/calendar/fullcalendar.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/extensions/moment.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/forms/select/select2.full.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/forms/validation/jquery.validate.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/pickers/flatpickr/flatpickr.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/pickers/flatpickr/ar.js')) }}"></script>
    @datepickerJS()
@endsection

@section('page-script')
    <!-- Page js files -->
    <script>
        $(function () {
            $(document).on('click', '.fc-sidebarToggle-button', function (e) {
                $('.app-calendar-sidebar, .body-content-overlay').addClass('show');
            });

            $(document).on('click', '.body-content-overlay', function (e) {
                $('.app-calendar-sidebar, .body-content-overlay').removeClass('show');
            });

            let direction = $('html').data('textdirection') === 'rtl' ? 'rtl' : 'ltr',
                calendarEl = document.getElementById('calendar'),
                calendarsColor = {
                    {{ \App\Enums\HallEventType::Meeting }}: 'info',
                    {{ \App\Enums\HallEventType::Marriage }}: 'warning',
                },
                newReservationBtn = $('#NewReservation'),
                selectAll = $('.select-all'),
                calEventFilter = $('.calendar-events-filter'),
                filterInput = $('.input-filter'),

                statusSelectAll = $('.status-select-all'),
                calEventStatusFilter = $('.calendar-events-status-filter'),
                statusFilterInput = $('.status-filter');

            // Modify sidebar toggler
            function modifyToggler() {
                $('.fc-sidebarToggle-button').empty().append(feather.icons['menu'].toSvg({class: 'ficon'}));
            }

            // Calendar plugins
            let calendar = new FullCalendar.Calendar(calendarEl, {
                timeZone: 'UTC',
                initialView: 'dayGridMonth',
                events: function (info, successCallback) {
                    $.ajax(
                        {
                            url: '{{ route('admin.hall-reservations.calendar') }}',
                            data: {
                                start: info.startStr,
                                end: info.endStr
                            },
                            type: 'GET',
                            success: function (result) {
                                let eventTypes = $('.calendar-events-filter input:checked').toArray().map((x) => $(x).attr('data-value'));
                                let eventStatus = $('.calendar-events-status-filter input:checked').toArray().map((x) => $(x).attr('data-value'));
                                successCallback(
                                    result.filter(event => eventTypes.includes(event.extendedProps.event_type))
                                        .filter(event => eventStatus.includes(event.extendedProps.status))
                                );
                            },
                            error: function (error) {
                                console.log(error);
                            }
                        }
                    );
                },
                locale: {
                    code: "ar",
                    week: {dow: 6, doy: 12},
                    direction: "rtl",
                    buttonText: {
                        prev: "السابق",
                        next: "التالي",
                        today: "اليوم",
                        year: "سنة",
                        month: "شهر",
                        week: "أسبوع",
                        day: "يوم",
                        list: "أجندة"
                    },
                    weekText: "أسبوع",
                    allDayText: "اليوم كله",
                    moreLinkText: "أخرى",
                    noEventsText: "لا يوجد أي أحداث لعرضها"
                },
                editable: false,
                dragScroll: true,
                dayMaxEvents: 4,
                eventResizableFromStart: true,
                customButtons: {
                    sidebarToggle: {
                        text: 'Sidebar'
                    }
                },
                headerToolbar: {
                    start: 'sidebarToggle, prev,next, title',
                    end: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
                },
                direction: direction,
                initialDate: new Date(),
                navLinks: true, // can click day/week names to navigate views
                eventClassNames: function ({event: calendarEvent}) {
                    return [
                        // Background Color
                        'bg-light-' + calendarsColor[calendarEvent._def.extendedProps.event_type]
                    ];
                },
                dateClick: function (info) {
                    //todo show create modal
                    console.log(info);
                },
                eventClick: function (info) {
                    let viewUrl = info.event._def.extendedProps.view_url;
                    if (viewUrl !== null)
                        Swal.fire({
                            customClass: 'swal-wide',
                            showCloseButton: true,
                            showCancelButton: false,
                            showConfirmButton: false,
                            willOpen: () => {
                                Swal.showLoading();
                                $.ajax({
                                    type: 'GET',
                                    url: viewUrl,
                                    success: function (res) {
                                        Swal.disableLoading();
                                        $(Swal.getHtmlContainer()).html(res).show();
                                        $(Swal.getTitle()).html('تفاصيل الحجز').show();
                                    },
                                    error: function (res) {
                                        Swal.close();
                                        if (res.responseJSON.message || res.responseJSON.error)
                                            Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                    }
                                });
                            },
                        });
                },
                datesSet: function () {
                    modifyToggler();
                },
                viewDidMount: function () {
                    modifyToggler();
                }
            });

            // Render calendar
            calendar.render();

            // Modify sidebar toggler
            modifyToggler();

            $(newReservationBtn).on('click', function () {
                formModel('{{ route('admin.hall-reservations.create') }}', 'إضافة حجز جديد', 'إضافة', () => {
                    calendar.refetchEvents();
                });
            });

            // Select all & filter functionality
            if (selectAll.length) {
                selectAll.on('change', function () {
                    var $this = $(this);

                    if ($this.prop('checked')) {
                        calEventFilter.find('input').prop('checked', true);
                    } else {
                        calEventFilter.find('input').prop('checked', false);
                    }
                    calendar.refetchEvents();
                });
            }
            if (filterInput.length) {
                filterInput.on('change', function () {
                    $('.input-filter:checked').length < calEventFilter.find('input').length
                        ? selectAll.prop('checked', false)
                        : selectAll.prop('checked', true);
                    calendar.refetchEvents();
                });
            }
            if (statusSelectAll.length) {
                statusSelectAll.on('change', function () {
                    var $this = $(this);

                    if ($this.prop('checked')) {
                        calEventStatusFilter.find('input').prop('checked', true);
                    } else {
                        calEventStatusFilter.find('input').prop('checked', false);
                    }
                    calendar.refetchEvents();
                });
            }
            if (statusFilterInput.length) {
                statusFilterInput.on('change', function () {
                    $('.status-filter:checked').length < calEventStatusFilter.find('input').length
                        ? statusSelectAll.prop('checked', false)
                        : statusSelectAll.prop('checked', true);
                    calendar.refetchEvents();
                });
            }
        })
    </script>
@endsection
