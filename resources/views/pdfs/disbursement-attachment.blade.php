<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <style>
        /* Copy necessary styles from your main template */
        body {
            direction: rtl;
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 14px;
        }

        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 60px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 30px;
        }

        .header-table td {
            padding: 10px;
            vertical-align: middle;
        }

        .logo-cell {
            width: 120px;
            text-align: right;
        }

        .logo {
            width: 120px;
            height: auto;
            max-height: 120px;
        }

        .title-cell {
            text-align: center;
        }

        .document-title {
            font-weight: bold;
            font-size: 24px;
            display: inline-block;
        }

        .date-cell {
            width: 150px;
            text-align: left;
        }

        .date-section {
            white-space: nowrap;
        }

        .date-label {
            font-weight: bold;
            display: inline-block;
            margin-left: 5px;
            font-size: 16px;
        }

        .date-value {
            display: inline-block;
            font-size: 16px;
        }

        .content {
            clear: both;
        }

        .attachment-container {
            border: 1px solid #000;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }

        .attachment-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 15px;
        }

        .attachment-content {
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .attachment-image {
            text-align: center;
            margin-top: 20px;
        }

        .attachment-image img {
            max-width: 90%;
            max-height: 500px;
        }
    </style>
</head>
<body>
<!-- Header -->
<table class="header-table">
    <tr>
        <td class="logo-cell">
            <img class="logo" src="{{ $appIconPath ?? asset('images/logo.png') }}" alt="Logo">
        </td>
        <td class="title-cell">
            <div class="document-title">المرفقات</div>
        </td>
        <td class="date-cell">
            <div class="date-section">
                <span class="date-label">التاريخ:</span>
                <span class="date-value">{{ $request_date }}</span>
            </div>
        </td>
    </tr>
</table>

<!-- Attachment content -->
<div class="content">
    <div class="attachment-container">
        <div class="attachment-title">{{ $index }}. {{ $attachment['title'] }}</div>

        <div class="attachment-content">
            {!! nl2br(e($attachment['content'])) !!}
        </div>

        @if(!empty($attachment['file']))
            <div>
                <strong>مرفق ملف:</strong> {{ basename($attachment['file']) }}
            </div>

            @if(Str::endsWith(strtolower($attachment['file']), ['.jpg', '.jpeg', '.png', '.gif']))
                <div class="attachment-image">
                    <img src="{{ Storage::disk('public')->url($attachment['file']) }}"
                         alt="{{ basename($attachment['file']) }}">
                </div>
            @elseif(!Str::endsWith(strtolower($attachment['file']), ['.pdf']))
                <div style="text-align: center; margin-top: 20px; color: #555; font-style: italic;">
                    (هذا الملف ليس بتنسيق PDF أو صورة)
                </div>
            @endif
        @endif
    </div>
</div>
</body>
</html>
