<x-filament::page>
    <div class="flex flex-col md:flex-row gap-8">
        {{-- Tree View Section --}}
        <div class="flex-grow w-full md:w-3/5 lg:w-2/3 animate-fade-in">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-4"
                 x-data="{
                    members: [], // Root members
                    searchQuery: '',
                    expandedNodes: new Set(),
                    loadingState: { global: false, children: new Set() },
                    selectedMemberId: null,

                    async init() {
                        this.loadingState.global = true;
                        const rootMembers = await $wire.getMembers(this.searchQuery); // Pass initial search if any
                        this.members = rootMembers.map(m => ({ ...m, children: undefined, depth: 0, hasChildren: m.hasChildren ?? false }));
                        this.loadingState.global = false;
                    },
                    async fetchMembers() { // Full refresh
                        this.loadingState.global = true;
                        // Use current searchQuery for refresh, or default to initial state (null/empty)
                        const rootMembersData = await $wire.getMembers(this.searchQuery);
                        this.members = rootMembersData.map(m => ({ ...m, children: undefined, depth: 0, hasChildren: m.hasChildren ?? false }));
                        this.expandedNodes.clear();
                        this.loadingState.children.clear();
                        this.loadingState.global = false;
                    },
                    async searchMembers() {
                        this.loadingState.global = true;
                        const searchResults = await $wire.search(this.searchQuery);
                        this.members = searchResults.map(m => ({ ...m, children: undefined, depth: 0, hasChildren: m.hasChildren ?? false }));
                        this.expandedNodes.clear();
                        this.loadingState.children.clear();
                        this.loadingState.global = false;
                    },
                    findMember(membersArray, memberId) {
                        for (let member of membersArray) {
                            if (member.id === memberId) return member;
                            if (member.children) {
                                const foundInChildren = this.findMember(member.children, memberId);
                                if (foundInChildren) return foundInChildren;
                            }
                        }
                        return null;
                    },
                    async toggleNode(member) {
                        const nodeId = member.id;
                        if (this.expandedNodes.has(nodeId)) {
                            this.expandedNodes.delete(nodeId);
                        } else {
                            this.expandedNodes.add(nodeId);
                            const memberToUpdate = this.findMember(this.members, nodeId);

                            if (memberToUpdate && memberToUpdate.hasChildren && memberToUpdate.children === undefined) {
                                this.loadingState.children.add(nodeId);
                                try {
                                    const childrenData = await $wire.getChildren(nodeId);
                                    memberToUpdate.children = childrenData.map(child => ({
                                        ...child,
                                        children: undefined,
                                        depth: (memberToUpdate.depth || 0) + 1,
                                        hasChildren: child.hasChildren ?? false
                                    }));
                                } catch (error) {
                                    console.error('Failed to load children for member:', nodeId, error);
                                    memberToUpdate.children = [];
                                    this.expandedNodes.delete(nodeId);
                                } finally {
                                    this.loadingState.children.delete(nodeId);
                                }
                            }
                        }
                         // Force reactivity for the Set if needed, usually not required for add/delete
                        this.expandedNodes = new Set(this.expandedNodes);
                    },
                    isExpanded(nodeId) {
                        return this.expandedNodes.has(nodeId);
                    },
                    isLoadingChildren(nodeId) {
                        return this.loadingState.children.has(nodeId);
                    },
                    async selectMemberForEdit(member) {
                        this.selectedMemberId = member.id;
                        const formattedMember = await $wire.selectUser(member.id);
                        $dispatch('edit-member', formattedMember || member);
                    },
                    async deleteMember(id) {
                        if (confirm('هل أنت متأكد أنك تريد حذف هذا العضو؟ سيتعذر التراجع عن هذا الإجراء.')) {
                            await $wire.deleteMember(id);
                            await this.fetchMembers(); // Refresh tree
                            if (this.selectedMemberId === id) {
                                this.selectedMemberId = null;
                                $dispatch('reset-form-after-delete');
                            }
                        }
                    }
                 }">
                <h2 class="text-xl font-semibold mb-4 dark:text-white">شجرة العائلة (الذكور)</h2>
                <div class="flex gap-4 items-center mb-4">
                    <div class="relative flex-1">
                        <input type="text"
                               x-model.debounce.300ms="searchQuery"
                               x-on:keyup.debounce.300ms="searchMembers()"
                               placeholder="البحث بالاسم، البريد، أو الهاتف..."
                               class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500">
                        <div x-show="loadingState.global" class="absolute right-3 top-1/2 transform -translate-y-1/2 rtl:left-3 rtl:right-auto">
                            <svg class="animate-spin h-5 w-5 text-gray-500 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="space-y-1 border dark:border-gray-700 rounded-md p-4 min-h-[200px]">
                    <template x-if="loadingState.global && members.length === 0">
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">جاري تحميل الأعضاء...</div>
                    </template>
                    <template x-if="!loadingState.global && members.length === 0">
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            {{-- Adjust message based on whether a search is active --}}
                            <span x-text="searchQuery ? 'لم يتم العثور على نتائج للبحث.' : 'لا يوجد أعضاء لعرضهم.'"></span>
                        </div>
                    </template>

                    <template x-for="member in members" :key="member.id">
                        {{-- This div is the root of each member's display including its children --}}
                        <div class="animate-fade-in">
                            {{-- Member Item Row --}}
                            <div @click.stop="toggleNode(member)"
                                 class="flex items-center gap-2 p-2 rounded-md transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                                 :class="{ 'bg-primary-50 dark:bg-primary-600/30': selectedMemberId === member.id, 'font-semibold': isExpanded(member.id) }"
                                 :style="{'margin-left': (member.depth || 0) * 20 + 'px'}">

                                <div class="w-6 text-center shrink-0">
                                    <template x-if="member.hasChildren">
                                        <button @click.stop="toggleNode(member)" type="button"
                                                class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-sm transition-colors">
                                            {{-- Using Heroicons directly as SVGs for simplicity with Alpine --}}
                                            <svg x-show="!isExpanded(member.id)" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                                            <svg x-show="isExpanded(member.id)" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                                        </button>
                                    </template>
                                </div>
                                {{-- User Icon --}}
                                <svg class="w-4 h-4 shrink-0 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" /></svg>

                                <span class="flex-grow dark:text-gray-200" x-text="member.name"></span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 shrink-0" x-text="member.dob ? new Date(member.dob).toLocaleDateString() : ''"></span>

                                {{-- Action Buttons --}}
                                <div class="flex items-center gap-1 shrink-0">
                                    <button @click.stop="$dispatch('open-form-for-child', { parentId: member.id })" class="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg" title="إضافة ابن لهذا العضو">
                                        <svg class="w-3 h-3 text-gray-600 dark:text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /></svg>
                                    </button>
                                    <button @click.stop="selectMemberForEdit(member)" class="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg" title="تعديل بيانات العضو">
                                        <svg class="w-3 h-3 text-gray-600 dark:text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" /></svg>
                                    </button>
                                    <button @click.stop="deleteMember(member.id)" class="p-1.5 hover:bg-red-200 dark:hover:bg-red-700 text-red-600 dark:text-red-400 rounded-lg" title="حذف العضو">
                                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>
                                    </button>
                                </div>
                            </div>

                            {{-- Recursive Children Display Area --}}
                            <div x-show="isExpanded(member.id)" x-collapse class="animate-fade-in-sm"
                                 :style="{'padding-left': (member.depth || 0) * 0 + 'px'}"> {{-- Indentation for children list --}}
                                <template x-if="isLoadingChildren(member.id)">
                                    <div class="p-2 text-gray-400 dark:text-gray-500 text-sm ml-5">
                                        <svg class="animate-spin inline-block h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                                        جاري تحميل الأبناء...
                                    </div>
                                </template>
                                {{-- This is where children nodes will be recursively rendered --}}
                                {{-- Alpine doesn't have a direct "recursive component" like Vue/React.
                                     Instead, we use nested x-for if the data structure supports it.
                                     The `member.children` array will contain child member objects.
                                     We re-use the same templating logic by nesting the x-for.
                                --}}
                                <template x-if="!isLoadingChildren(member.id) && member.children && member.children.length > 0">
                                    {{-- NESTED LOOP FOR CHILDREN --}}
                                    <template x-for="child in member.children" :key="child.id">
                                        <div class="animate-fade-in"> {{-- Child's wrapper --}}
                                            <div @click.stop="toggleNode(child)"
                                                 class="flex items-center gap-2 p-2 rounded-md transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                                                 :class="{ 'bg-primary-50 dark:bg-primary-600/30': selectedMemberId === child.id, 'font-semibold': isExpanded(child.id) }"
                                                 :style="{'margin-left': (child.depth || 0) * 20 + 'px'}">

                                                <div class="w-6 text-center shrink-0">
                                                    <template x-if="child.hasChildren">
                                                        <button @click.stop="toggleNode(child)" type="button"
                                                                class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-sm transition-colors">
                                                            <svg x-show="!isExpanded(child.id)" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                                                            <svg x-show="isExpanded(child.id)" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                                                        </button>
                                                    </template>
                                                </div>
                                                <svg class="w-4 h-4 shrink-0 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" /></svg>
                                                <span class="flex-grow dark:text-gray-200" x-text="child.name"></span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 shrink-0" x-text="child.dob ? new Date(child.dob).toLocaleDateString() : ''"></span>
                                                <div class="flex items-center gap-1 shrink-0">
                                                    <button @click.stop="$dispatch('open-form-for-child', { parentId: child.id })" class="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg" title="إضافة ابن لهذا العضو"><svg class="w-3 h-3 text-gray-600 dark:text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /></svg></button>
                                                    <button @click.stop="selectMemberForEdit(child)" class="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg" title="تعديل بيانات العضو"><svg class="w-3 h-3 text-gray-600 dark:text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" /></svg></button>
                                                    <button @click.stop="deleteMember(child.id)" class="p-1.5 hover:bg-red-200 dark:hover:bg-red-700 text-red-600 dark:text-red-400 rounded-lg" title="حذف العضو"><svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button>
                                                </div>
                                            </div>
                                            {{-- GRANDCHILDREN Area (and further recursion) --}}
                                            {{-- This demonstrates one more level. For infinite, you'd need a component or helper. --}}
                                            <div x-show="isExpanded(child.id)" x-collapse class="animate-fade-in-sm"
                                                 :style="{'padding-left': (child.depth || 0) * 0 + 'px'}">
                                                <template x-if="isLoadingChildren(child.id)">
                                                    <div class="p-2 text-gray-400 dark:text-gray-500 text-sm ml-5">
                                                        <svg class="animate-spin inline-block h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                                                        جاري تحميل الأحفاد...
                                                    </div>
                                                </template>
                                                <template x-if="!isLoadingChildren(child.id) && child.children && child.children.length > 0">
                                                    <template x-for="grandchild in child.children" :key="grandchild.id">
                                                        {{-- Grandchild Item Row (Simplified for brevity, add full actions if needed) --}}
                                                        <div @click.stop="toggleNode(grandchild)"
                                                             class="flex items-center gap-2 p-2 rounded-md transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                                                             :class="{ 'bg-primary-50 dark:bg-primary-600/30': selectedMemberId === grandchild.id, 'font-semibold': isExpanded(grandchild.id) }"
                                                             :style="{'margin-left': (grandchild.depth || 0) * 20 + 'px'}">
                                                            <div class="w-6 text-center shrink-0">
                                                                <template x-if="grandchild.hasChildren">
                                                                    <button @click.stop="toggleNode(grandchild)" type="button" class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-sm transition-colors">
                                                                        <svg x-show="!isExpanded(grandchild.id)" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                                                                        <svg x-show="isExpanded(grandchild.id)" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                                                                    </button>
                                                                </template>
                                                            </div>
                                                            <svg class="w-4 h-4 shrink-0 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" /></svg>
                                                            <span class="flex-grow dark:text-gray-200" x-text="grandchild.name"></span>
                                                            {{-- Add actions for grandchild if needed --}}
                                                        </div>
                                                        {{-- For great-grandchildren, you'd nest again... --}}
                                                    </template>
                                                </template>
                                                <template x-if="!isLoadingChildren(child.id) && child.children && child.children.length === 0 && child.hasChildren">
                                                    <div class="p-2 text-gray-400 dark:text-gray-500 text-sm ml-5">لا يوجد أبناء (أحفاد) لهذا العضو.</div>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                                <template x-if="!isLoadingChildren(member.id) && member.children && member.children.length === 0 && member.hasChildren">
                                    <div class="p-2 text-gray-400 dark:text-gray-500 text-sm ml-5">
                                        لا يوجد أبناء لـ <span x-text="member.name"></span>.
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        {{-- Form Section --}}
        <div class="w-full md:w-2/5 lg:w-1/3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-4 animate-fade-in"
             x-data="{
                isEditMode: false,
                formTitle: 'إضافة عضو جديد',
                formData: {}, // Initialized in init
                allMembersForDropdown: [], // For father dropdown

                get treeComponent() { // Helper to access the tree's Alpine component
                    return document.querySelector('[x-data*= &quot;members&quot;]').__x;
        },

        get availableFathers() {
        // Filter members from the main tree data for the father dropdown
        // This ensures it's up-to-date if the tree members change
        const treeAlpine = this.treeComponent;
        if (!treeAlpine || !treeAlpine.members) return [];

        // Flatten the tree to get all members for the dropdown
        let allPotentialFathers = [];
        function flattenMembers(members) {
        for (const member of members) {
        if (member.gender && member.gender.toLowerCase() === 'MALE') { // Check gender explicitly
        allPotentialFathers.push(member);
        }
        if (member.children && member.children.length > 0) {
        flattenMembers(member.children);
        }
        }
        }
        flattenMembers(treeAlpine.members);

        return allPotentialFathers.filter(m => m.id !== this.formData.id); // Exclude self
        },

        init() {
        this.resetForm();
        this.$watch('isEditMode', value => {
        this.formTitle = value ? 'تحديث العضو' : 'إضافة عضو جديد';
        });
        // Load initial members for dropdown if tree component is ready
        if (this.treeComponent && this.treeComponent.members.length > 0) {
        this.updateFathersDropdown();
        }
        // Watch for changes in treeComponent.members to update dropdown
        // This is more complex, direct call or event might be better
        // For now, dropdown updates when form resets or edits.
        },
        updateFathersDropdown() {
        //This function is implicitly handled by `availableFathers` getter now
        },
        async submitForm() {
        const treeAlpine = this.treeComponent;
        if (this.isEditMode) {
        await $wire.updateMember(this.formData.id, this.formData);
        } else {
        await $wire.addMember(this.formData);
        }
        this.resetForm(); // Reset form data
        if (treeAlpine) {
        await treeAlpine.fetchMembers(); // Refresh the tree on the left
        }
        },
        resetForm(parentId = null) {
        this.formData = {
        id: null, name: '', gender: 'MALE', father_id: parentId,
        mother_id: null, bio: '', dob: '', dod: '',
        is_dead: false, phone: '', email: ''
        };
        this.isEditMode = false;
        // this.updateFathersDropdown(); // Update fathers when form resets
        }
        }"
        @edit-member.window="
        isEditMode = true;
        formData = { ...$event.detail }; // $event.detail is the member object
        if (formData.father_id === undefined) formData.father_id = null; // Ensure father_id exists
        // this.updateFathersDropdown();
        "
        @open-form-for-child.window="
        resetForm($event.detail.parentId);
        "
        @reset-form-after-delete.window="resetForm()">

        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold dark:text-white" x-text="formTitle"></h2>
            <button x-show="isEditMode" @click="resetForm()" type="button"
                    class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <svg class="w-4 h-4 inline-block" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>
                <span class="ml-1 text-sm rtl:mr-1 rtl:ml-0">إلغاء التعديل</span>
            </button>
        </div>
        <form @submit.prevent="submitForm" class="space-y-4">
            <input type="hidden" x-model="formData.id">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم الكامل</label>
                <input type="text" x-model="formData.name" placeholder="مثال: فلان بن فلان الفلاني"
                       class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الجنس</label>
                <select x-model="formData.gender"
                        class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500" required>
                    <option value="MALE">ذكر</option>
                    <option value="FEMALE">أنثى</option> {{-- Livewire backend handles MALE/FEMALE conversion --}}
                </select>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الجوال</label>
                    <input type="tel" x-model="formData.phone" placeholder="05xxxxxxxx" dir="ltr"
                           class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني</label>
                    <input type="email" x-model="formData.email" placeholder="<EMAIL>" dir="ltr"
                           class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الأب</label>
                <select x-model="formData.father_id"
                        class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="">-- اختر الأب (إن وجد) --</option>
                    {{-- Iterating over a computed property 'availableFathers' --}}
                    <template x-for="father in availableFathers" :key="father.id">
                        <option :value="father.id" x-text="father.name"></option>
                    </template>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نبذة</label>
                <textarea x-model="formData.bio" placeholder="معلومات إضافية..." rows="3"
                          class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500"></textarea>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ الميلاد</label>
                    <input type="date" x-model="formData.dob"
                           class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div x-show="formData.is_dead">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ الوفاة</label>
                    <input type="date" x-model="formData.dod"
                           class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white px-4 py-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
            </div>
            <label class="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                <input type="checkbox" x-model="formData.is_dead" class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-primary-600 focus:ring-primary-500 dark:focus:ring-offset-gray-800">
                <span>متوفى</span>
            </label>
            <div class="flex gap-4 pt-2">
                <button type="submit"
                        class="flex-1 bg-primary-600 text-white px-6 py-2.5 rounded-lg hover:bg-primary-700 transition-colors focus:outline-none focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-800">
                    <span x-text="isEditMode ? 'تحديث بيانات العضو' : 'إضافة عضو جديد'"></span>
                </button>
                <button type="button" @click="resetForm()"
                        class="px-6 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700">
                    إلغاء
                </button>
            </div>
        </form>
    </div>
    </div>

    {{-- No need for the external findMemberInTree script tag if using Alpine's internal method --}}
</x-filament::page>
