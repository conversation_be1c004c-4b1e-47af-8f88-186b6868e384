<div class="space-y-4">
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <!-- Total Items -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $total }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">إجمالي العناصر</div>
        </div>

        <!-- Assigned Items -->
        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $assigned }}</div>
            <div class="text-sm text-green-600 dark:text-green-400">مُعيَّن لمستخدمين</div>
            @if($total > 0)
                <div class="text-xs text-green-500">{{ round(($assigned / $total) * 100, 1) }}%</div>
            @endif
        </div>

        <!-- Anonymous Items -->
        <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $anonymous }}</div>
            <div class="text-sm text-yellow-600 dark:text-yellow-400">معاملات مجهولة</div>
            @if($total > 0)
                <div class="text-xs text-yellow-500">{{ round(($anonymous / $total) * 100, 1) }}%</div>
            @endif
        </div>

        <!-- Unassigned Items -->
        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $unassigned }}</div>
            <div class="text-sm text-red-600 dark:text-red-400">غير مُعيَّن</div>
            @if($total > 0)
                <div class="text-xs text-red-500">{{ round(($unassigned / $total) * 100, 1) }}%</div>
            @endif
        </div>
    </div>

    @if($withErrors > 0)
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <div class="text-sm font-medium text-red-800 dark:text-red-200">
                        يوجد {{ $withErrors }} عنصر يحتوي على أخطاء
                    </div>
                    <div class="text-xs text-red-600 dark:text-red-400">
                        العناصر التي تحتوي على أخطاء لا يمكن تأكيدها
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Progress Bar -->
    <div class="space-y-2">
        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
            <span>تقدم التعيين</span>
            <span>{{ $total > 0 ? round((($assigned + $anonymous) / $total) * 100, 1) : 0 }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-300" 
                 style="width: {{ $total > 0 ? (($assigned + $anonymous) / $total) * 100 : 0 }}%"></div>
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ $assigned + $anonymous }} من {{ $total }} عنصر تم تعيينه
        </div>
    </div>

    @if($unassigned > 0)
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <div class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        نصيحة: استخدم الإجراءات المجمعة لتعيين عدة عناصر بسرعة
                    </div>
                    <div class="text-xs text-blue-600 dark:text-blue-400">
                        حدد العناصر واستخدم "تعيين مستخدم للمحدد" أو "تحديد كمجهول"
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
