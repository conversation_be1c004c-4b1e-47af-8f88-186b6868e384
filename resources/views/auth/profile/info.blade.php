@extends('auth.profile.layout', ['activeTab' => 'info'])

@section('title', 'الصفحة الشخصية')

@section('ProfileContent')
    <div class="card">
        <div class="card-header border-bottom">
            <h4 class="card-title">الصفحة الشخصية</h4>
        </div>
        <div class="card-body py-2 my-25">
            <div class="d-flex">
                <x-user-avatar :user="$user" :width="100" :class="['avatar me-25 border-secondary']" />
                <!-- upload and reset button -->
                {{--<div class="d-flex align-items-end mt-75 ms-1">
                    <div>
                        <label for="account-upload" class="btn btn-sm btn-primary mb-75 me-75">Upload</label>
                        <input type="file" id="account-upload" hidden accept="image/*"/>
                        <button type="button" id="account-reset" class="btn btn-sm btn-outline-secondary mb-75">
                            Reset
                        </button>
                        <p class="mb-0">Allowed file types: png, jpg, jpeg.</p>
                    </div>
                </div>--}}
                <!--/ upload and reset button -->
            </div>

            <form class="validate-form mt-2 pt-50">
                <div class="row">
                    <div class="col-sm-6 col-12 mb-1">
                        <label class="form-label" for="userFamilyId">@lang('userColumns.family_user_id')</label>
                        <input type="text" class="form-control" id="userFamilyId"
                               placeholder="@lang('userColumns.family_user_id')" disabled readonly
                               value="{{ $user->family_user_id }}"/>
                    </div>

                    <div class="col-sm-6 col-12 mb-1">
                        <label class="form-label" for="userPhone">@lang('userColumns.phone')</label>
                        <input type="text" class="form-control" id="userPhone"
                               placeholder="@lang('userColumns.phone')" disabled readonly
                               value="{{ $user->phone }}"/>
                    </div>

                    <div class="col-sm-6 col-12 mb-1">
                        <label class="form-label" for="name">@lang('userColumns.name')</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name"
                               placeholder="@lang('userColumns.name')" required name="name"
                               value="{{ $errors->isNotEmpty() ? old('name') : $user->name }}"/>
                        @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-sm-6 col-12 mb-1">
                        <label class="form-label" for="userEmail">@lang('userColumns.email')</label>
                        <input type="text" class="form-control @error('email') is-invalid @enderror" id="userEmail"
                               placeholder="@lang('userColumns.email')" autocomplete="user-email"
                               name="email" value="{{ $errors->isNotEmpty() ? old('email') : @$user->email }}"/>
                        @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        {{--<button type="submit" class="btn btn-primary mt-1 me-1">Save changes</button>--}}
                        {{--<button type="reset" class="btn btn-outline-secondary mt-1">Discard</button>--}}
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
