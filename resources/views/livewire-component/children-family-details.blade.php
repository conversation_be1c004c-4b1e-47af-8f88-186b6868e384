<div class="col-12">
    <h3>الأبناء</h3>
</div>
@foreach($children as $child)
    <div class="col-12" wire:key="child-{{ $child->id }}">
        <div class="row">
            <div class="col-lg-4 col-12 mb-1">
                @if($loop->index === 0)
                    <label for="" class="form-label">الاسم</label>
                @endif
                <div class="row">
                    <div class="col-auto p-0 m-0">
                        <div class="mt-50 me-0 form-check form-check-inline" style="float: right;">
                            <input type="checkbox" class="form-check-input"
                                   @disabled($child->is_user_dead)
                                   wire:model="familyData.child-{{ $child->id }}.is_active">
                        </div>
                    </div>
                    <div class="col p-0 m-0">
                        <input type="text" class="form-control" disabled readonly
                               value="{{ $child->full_name }}"/>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-12 mb-1">
                @if($loop->index === 0)
                    <label for="" class="form-label">الجنس</label>
                @endif
                <input type="text" class="form-control" disabled readonly
                       value="@lang("userColumns.gender.{$child->gender}")"/>
            </div>
            <div class="col-lg-2 col-12 mb-1">
                @if($loop->index === 0)
                    <label data-required class="form-label">تاريخ الميلاد</label>
                @endif
                <input type="text" id="DOBChild{{ $child->id }}"
                       class="form-control user-dob @error("familyData.child-{$child->id}.dob") is-invalid @enderror"
                       wire:model="familyData.child-{{ $child->id }}.dob"
                    @disabled(!empty($child->dob))/>
                @error("familyData.child-{$child->id}.dob")
                <div class=" invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>
            @if(empty($child->dob))
                <script>
                    /*$(function () {
                        let _DOBChild = $('#DOBChild{{ $child->id }}');
                        if (_DOBChild.data('HijriDatePicker') === undefined)
                            _DOBChild.hijriDatePicker(datepickerOptions).on('dp.change', function (e) {
                                //@formatter:off
                                if ($(e.target).attr('wire:model.live') !== undefined)
                                        @this.set($(e.target).attr('wire:model.live'), e.target.value, true);
                                else if ($(e.target).attr('wire:model') !== undefined)
                                        @this.set($(e.target).attr('wire:model'), e.target.value, false);
                                //@formatter:on
                            })
                    })*/
                </script>
            @endif
            <div class="col-lg-2 col-6 mb-1">
                @if($loop->index === 0)
                    <label data-required>
                        @lang('userColumns.educational_status')
                    </label>
                @endif
                <div wire:ignore>
                    <select data-placeholder="@lang('userColumns.educational_status')"
                            class="form-control wire-select2 @error("familyData.child-{$child->id}.educational_status") is-invalid @enderror"
                            data-minimum-results-for-search="-1" data-allow-clear="true"
                            @disabled(!empty($child->educational_status))
                            wire:model="familyData.child-{{ $child->id }}.educational_status">
                        <option value=""></option>
                        @foreach(all_educational_status() as $i)
                            <option value="{{ $i }}" @selected($child->educational_status === $i)>{{ $i }}</option>
                        @endforeach
                    </select>
                </div>
                @error("familyData.child-{$child->id}.educational_status")
                <div class=" invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-lg-2 col-6 mb-1">
                @if($loop->index === 0)
                    <label data-required>@lang('userColumns.health_status')</label>
                @endif
                <div wire:ignore>
                    <select data-placeholder="@lang('userColumns.health_status')"
                            class="form-control wire-select2 @error("familyData.child-{$child->id}.health_status") is-invalid @enderror"
                            data-minimum-results-for-search="-1" data-allow-clear="true"
                            @disabled(!empty($child->health_status))
                            wire:model="familyData.child-{{ $child->id }}.health_status">
                        <option value=""></option>
                        @foreach(all_health_status() as $i)
                            <option value="{{ $i }}" @selected($child->health_status === $i)>{{ $i }}</option>
                        @endforeach
                    </select>
                </div>
                @error("familyData.child-{$child->id}.health_status")
                <div class=" invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>
@endforeach
