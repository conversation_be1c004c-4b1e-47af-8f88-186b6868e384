@extends('layouts.contentLayoutMaster')

@section('title', 'إعدادات البرنامج')

@section('vendor-style')
    <!-- vendor css files -->
    <link rel="stylesheet" href="{{ url(mix('vendors/css/forms/select/select2.min.css')) }}">
    <style>
        .ce-block__content {
            max-width: 95%;
            background: #f9f9f970;
            margin: 3px;
        }
    </style>
@endsection

@section('content')
    <div class="card">
        <div class="card-body">
            @if(session()->has('message'))
                <div class="alert alert-success" role="alert">
                    {{--<h4 class="alert-heading">Success</h4>--}}
                    <div class="alert-body">
                        <h4>{{ session('message') }}</h4>
                    </div>
                </div>
            @endif
            <form method="post" enctype="multipart/form-data" action="{{ route('admin.app-setting.store') }}">
                @csrf
                <div class="row">
                    <div class="col-xl-6 col-md-12 mb-1">
                        <label for="AppSettingZakatType">طريقة احتساب الالتزامات (الزكاة)</label>
                        @php($zakat_type = $errors->isNotEmpty() ? old('zakat_type') : $generalSettings->zakatType)
                        <select class="select2 form-select @error('zakat_type') is-invalid @enderror"
                                data-minimum-results-for-search="-1" data-allow-clear="true"
                                data-placeholder="طريقة احتساب الالتزامات (الزكاة)"
                                @disabled(!user()->hasPermissionTo(\App\Permissions\GeneralPermissions::SETTING_ZAKAT))
                                name="zakat_type" id="AppSettingZakatType">
                            <option value=""></option>
                            <option value="PERCENTAGE" @selected($zakat_type === 'PERCENTAGE')>نسبة مئوية</option>
                            <option value="STATIC" @selected($zakat_type === 'STATIC')>مبلغ ثابت</option>
                        </select>
                        @error('zakat_type')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-xl-6 col-md-12 mb-1">
                        <label for="AppSettingZakatValue">قيمة الالتزامات من الدخل (الزكاة)</label>
                        <input type="text" class="form-control @error('zakat_value') is-invalid @enderror"
                               name="zakat_value" id="AppSettingZakatValue"
                               @disabled(!user()->hasPermissionTo(\App\Permissions\GeneralPermissions::SETTING_ZAKAT))
                               placeholder="نسبة الالتزامات من الدخل (الزكاة)" required
                               value="{{ $errors->isNotEmpty() ? old('zakat_value') : $generalSettings->zakatValue }}"/>
                        @error('zakat_value')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-xl-6 col-md-12 mb-1">
                        <label for="AppSettingDaftraTreasuryId">دفترة (خزينة التبرعات)</label>
                        <input type="text" class="form-control @error('daftra_treasury_id') is-invalid @enderror"
                               name="daftra_treasury_id" id="AppSettingDaftraTreasuryId"
                               @disabled(!user()->hasPermissionTo(\App\Permissions\GeneralPermissions::GENERAL))
                               placeholder="دفترة (خزينة التبرعات)" required
                               value="{{ $errors->isNotEmpty() ? old('daftra_treasury_id') : $featureSettings->daftraTreasuryId }}"/>
                        @error('daftra_treasury_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    {{--<div class="col-xl-6 col-md-12 mb-1">
                        <div class="form-check form-check-inline mt-2">
                            <input class="form-check-input" name="waha_enabled" type="checkbox"
                                   id="waha_enabled" value="1"
                                   @checked($errors->isNotEmpty() ? !empty(old('waha_enabled')) : ($featureSettings->wahaEnabled === true))/>
                            <label class="form-check-label" for="waha_enabled">تفعيل الواتساب</label>
                        </div>
                    </div>--}}
                </div>
                <hr>
                <button class="btn btn-primary mt-2" type="submit">تعديل</button>
            </form>
        </div>
    </div>
@endsection

@section('vendor-script')
    <!-- vendor files -->
@endsection

@section('page-script')
    <script>
        $(function () {
            $('select.select2').select2({theme: 'bootstrap-5'})
        })
    </script>
@endsection
