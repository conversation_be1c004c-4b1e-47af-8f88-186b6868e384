@php
    $rnd = \Illuminate\Support\Str::random();
    $oldParentUserId = (!request()->isMethod('get') && $errors->isNotEmpty()) ? request('parent_user_id') : @$branch->parent_user_id;
    $oldParentUser = !is_null($oldParentUserId) ? \App\Models\User::find($oldParentUserId) : null;
@endphp
<div class="col-md-12" style="text-align: right">
    @if(isset($message) && !empty($message))
        <div id="{{$rnd}}_alert" class="alert alert-info col-12 mt-2 p-1">{{$message}}</div>
    @endif
    <form class="form-horizontal row swal-submit-form" id="{{$rnd}}_form" method="{{ isset($branch) ? 'PUT' : 'POST' }}"
          data-url="{{ isset($branch) ? route('admin.branches.update', $branch) : route('admin.branches.store') }}">
        <div class="form-group col-12">
            <label class="form-label" for="{{$rnd}}_name">اسم الفرع</label>
            <input id="{{$rnd}}_name" name="name" type="text" placeholder="اسم الفرع" autocomplete="off"
                   value="{{ (request()->isMethod('post') && $errors->isNotEmpty()) ? request('name') : @$branch->name }}"
                   class="form-control input-md @error('name') is-invalid @enderror">
            @error('name')
            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <div class="form-group col-12">
            <label class="form-label" for="{{$rnd}}_city_id">المدينة</label>
            <select class="select2 form-control" name="city_id" id="{{$rnd}}_city_id"
                    data-minimum-results-for-search="-1" data-allow-clear="true" data-placeholder="المدينة">
                <option value=""></option>
                @foreach(\App\Models\City::all() as $city)
                    <option
                        @if(intval((request()->isMethod('post') && $errors->isNotEmpty()) ? request('city_id') : @$branch->city_id) === $city->id) selected
                        @endif
                        value="{{$city->id}}">{{$city->title_ar}}</option>
                @endforeach
            </select>
            @error('city_id')
            <span class="invalid-feedback" role="alert" style="display: block;"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <div class="form-group col-12">
            <label class="form-label" for="{{$rnd}}_parent_id">الفرع الأب</label>
            <select class="select2 form-control" name="parent_id" id="{{$rnd}}_parent_id"
                    data-minimum-results-for-search="-1" data-allow-clear="true" data-placeholder="الفرع الأب">
                <option value=""></option>
                @foreach($branches as $parentBranch)
                    <option
                        @if(intval((request()->isMethod('post') && $errors->isNotEmpty()) ? request('parent_id') : @$branch->parent_id) === $parentBranch->id) selected
                        @endif
                        value="{{$parentBranch->id}}">{{$parentBranch->name}}</option>
                @endforeach
            </select>
            @error('parent_id')
            <span class="invalid-feedback" role="alert" style="display: block;"><strong>{{ $message }}</strong></span>
            @enderror
        </div>

        <div class="form-group col-12">
            <label class="form-label" for="{{$rnd}}_parent_user_id">الجد للفرع</label>
            <select class="form-control" name="parent_user_id" id="{{$rnd}}_parent_user_id"
                    data-minimum-results-for-search="2" data-allow-clear="true" data-placeholder="الجد للفرع">
                <option value=""></option>
                @if(isset($oldParentUser) && !is_null($oldParentUser))
                    <option selected value="{{$oldParentUser->id}}">{{$oldParentUser->full_name}} #{{$oldParentUser->family_user_id}}</option>
                @endif
            </select>
            @error('parent_user_id')
            <span class="invalid-feedback" role="alert"
                  style="display: block;"><strong>{{ $message }}</strong></span>
            @enderror
        </div>
        <input type="submit" style="display: none;">
    </form>
</div>

<script>
    $(function () {
        let UserId = $('#{{$rnd}}_parent_user_id')[0];
        if (UserId) {
            $(UserId).select2({
                theme: 'bootstrap-5',
                width: '100%',
                dropdownParent: UserId.parentElement,
                ajax: {
                    url: "{!! signedRoute('admin.web-api.users', [], now()->addHour()) !!}",
                    dataType: 'json',
                    method: 'post',
                    delay: 250,
                    cache: true,
                    data: function (params) {
                        return {
                            q: params.term,
                            _token: '{{ csrf_token() }}',
                            page: params.page
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data,
                            pagination: {
                                more: (params.page * 15) < data.total
                            }
                        };
                    }
                },
                escapeMarkup: markup => markup,
                minimumInputLength: 1,
                templateResult: function (user) {
                    if (user.loading)
                        return user.text;
                    let markup = "<div class='select2-result-fabricsitory clearfix d-flex'>" +
                        "<div class='select2-result-fabricsitory__meta'>" +
                        "<div class='select2-result-fabricsitory__title fs-lg fw-500'>" + (user.full_name || user.name)
                        + (user.branch_name ? ` (${user.branch_name})` : '') + "</div>";
                    if (user.family_user_id)
                        markup += "<div class='select2-result-fabricsitory__description fs-xs opacity-80 mb-1'>" + user.family_user_id + "</div>";
                    markup += "</div></div>";
                    return markup;
                },
                templateSelection: function (user) {
                    return user.text ? user.text : `#${user.family_user_id} ${(user.full_name || user.name)}`;
                },
            });
            $($(UserId).data('select2').$dropdown).css('z-index', '9001')/*.css('position', 'revert')*/;
        }
        $('select.select2', '#{{$rnd}}_form').select2({theme: 'bootstrap-5'}).each(function (k, v) {
            $($(v).data('select2').$dropdown).css('z-index', '9001');
        });
        $('#{{$rnd}}_form').submit(function (e) {
            e.preventDefault();
            swal.clickConfirm();
        });
        setTimeout(function () {
            $('#{{$rnd}}_alert').hide(function () {
                $(this).remove();
            })
        }, 5000);
    });
</script>
