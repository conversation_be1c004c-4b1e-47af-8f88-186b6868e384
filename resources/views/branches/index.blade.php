@extends('layouts.contentLayoutMaster')

@section('title', 'قائمة الفروع')

@section('content')
    <div class="card">
        <div class="card-body">
            @if(session()->has('message'))
                <div class="alert alert-success" role="alert">
                    <div class="alert-body">
                        <h4>{{ session('message') }}</h4>
                    </div>
                </div>
            @endif
            <table class="datatables-ajax table">
                <thead>
                <tr>
                    <th>@lang('branchColumns.id')</th>
                    <th>@lang('branchColumns.name')</th>
                    <th>@lang('branchColumns.parent_user_name')</th>
                    <th>@lang('branchColumns.ambassadors_count')</th>
                    <th>@lang('branchColumns.users_count')</th>
                    <th>@lang('branchColumns.male_users_count')</th>
                    <th>@lang('branchColumns.female_users_count')</th>
                    <th>@lang('branchColumns.updated_at')</th>
                    <th>#</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
@endsection

@section('vendor-style')
    {{-- vendor css files --}}
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/dataTables.bootstrap5.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/responsive.bootstrap5.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/buttons.bootstrap5.min.css')) }}">
    {{--<link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/rowGroup.bootstrap5.min.css')) }}">--}}
@endsection

@section('vendor-script')
    {{-- vendor files --}}
    <script src="{{ url(mix('vendors/js/tables/datatable/jquery.dataTables.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.bootstrap5.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.responsive.min.js')) }}"></script>

    <script src="{{ url(mix('vendors/js/tables/datatable/datatables.buttons.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/buttons.bootstrap5.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.rowGroup.min.js')) }}"></script>

    {{--<script src="{{ asset('vendors/js/tables/datatable/custom.js') }}"></script>--}}
    {{--<script src="{{ asset('vendors/js/bootstrap-confirmation/bootstrap-confirmation.js') }}"></script>--}}
@endsection

@section('page-script')
    {{-- Page js files --}}
    {{--<script src="{{ url(mix('js/scripts/tables/table-datatables-advanced.js')) }}"></script>--}}
    <script>
        let errorsJoin = function (data) {
            let errors = [];
            for (var key in data)
                if (data.hasOwnProperty(key))
                    errors = errors.concat(data[key])
            return errors.join('<br>')
        }
        let dt = $('table.datatables-ajax').dataTable({
            dom: '<"card-header border-bottom p-1"<"head-label"><"dt-action-buttons text-right"B>><"d-flex justify-content-between align-items-center mx-0 row"<"col-sm-12 col-md-auto"l><"col-sm-12 col-md-auto"f>>t<"d-flex justify-content-between mx-0 row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
            language: {
                "url": '{{ url(mix('vendors/js/tables/datatable/Arabic.json')) }}'
            },
            ajax: '{{ route('admin.branches.index') }}',
            search: {smart: true},
            processing: true,
            serverSide: true,
            columns: [
                {data: 'id', searchable: true},
                {data: 'name', searchable: false, orderable: false},
                {
                    data: null, searchable: false, orderable: false,
                    render: function (data, type, row) {
                        if (row['parent_user'] != null)
                            return `<a href="{{ route('admin.users.index') }}/${row['parent_user']['id']}">${row['parent_user']['name']}</a>`;
                        return '';
                    }
                },
                {data: 'ambassadors_count', searchable: false, orderable: false},
                {data: 'users_count', searchable: false, orderable: false},
                {data: 'male_users_count', searchable: false, orderable: false},
                {data: 'female_users_count', searchable: false, orderable: false},
                {data: 'updated_at', searchable: false, orderable: false},
                {
                    data: null, searchable: false, orderable: false, render: function (data, type, row) {
                        let _btns = [];
                        if (row.ambassadors_url)
                            _btns.push(`<a href="${row.ambassadors_url}"><i class="far fa-2x fa-clipboard-list"></i></a>`);
                        if (row.notify_url)
                            _btns.push(`<a href="javascript:void(0)" class="notify-branch" data-url="${row.notify_url}"><i class="far fa-2x fa-paper-plane"></i></a>`);
                        if (row.update_url)
                            _btns.push(`<a href="javascript:void(0)" class="update-branch" data-url="${row.update_url}"><i class="far fa-2x fa-edit"></i></a>`);
                        if (row.delete_url)
                            _btns.push(`<a href="javascript:void(0)" class="delete-branch" data-delete-url="${row.delete_url}"><i class="far fa-2x fa-trash-alt"></i></a>`);
                        return _btns.join('&nbsp;&nbsp;')
                    }
                },
            ],
            buttons: [
                //ReloadButton,
                    @can('create', \App\Models\Branch::class)
                {
                    text: '<i class="fa fa-plus"></i>&nbsp;' + 'إضافة جديد',
                    className: 'btn btn-outline-primary waves-effect reload-btn',
                    action: function (e, dt, node, config) {
                        Swal.fire({
                            customClass: 'swal-wide',
                            showCloseButton: true,
                            confirmButtonText: 'حفظ',
                            willOpen: () => {
                                Swal.showLoading();
                                $.ajax({
                                    type: 'GET',
                                    url: '{{SignedRoute('admin.branches.create')}}',
                                    success: function (res) {
                                        Swal.disableLoading();
                                        $(Swal.getHtmlContainer()).html(res).show();
                                        $(Swal.getTitle()).html('إضافة فرع جديد').show();
                                    },
                                    error: function (res) {
                                        Swal.close();
                                        if (res.responseJSON.message || res.responseJSON.error)
                                            Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                    }
                                });
                            },
                            showLoaderOnConfirm: true,
                            preConfirm: () => {
                                return new Promise((resolve) => {
                                    $.ajax({
                                        type: 'POST',
                                        data: $('form', Swal.getHtmlContainer()).serializeArray(),
                                        url: $('form', Swal.getHtmlContainer()).data('url'),
                                        success: function (res) {
                                            $(Swal.getHtmlContainer()).html(res).show();
                                            resolve(false);
                                        },
                                        error: function (res) {
                                            if (res.responseJSON.message || res.responseJSON.error)
                                                Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                            resolve(false);
                                        }
                                    });
                                });
                            },
                            didClose: () => {
                                dt.ajax.reload(null, false);
                            }
                        });
                    }
                }
                @endcan
            ],
            drawCallback: function (oSettings) {
                $('a.update-branch', oSettings.nTable).click(async function (e) {
                    e.preventDefault();
                    let $btn = $(this);
                    await Swal.fire({
                        customClass: 'swal-wide',
                        showCloseButton: true,
                        confirmButtonText: 'حفظ',
                        willOpen: () => {
                            Swal.showLoading();
                            $.ajax({
                                type: 'GET',
                                url: $btn.data('url'),
                                success: function (res) {
                                    Swal.disableLoading();
                                    $(Swal.getHtmlContainer()).html(res).show();
                                    $(Swal.getTitle()).html('تعديل فرع').show();
                                },
                                error: function (res) {
                                    Swal.close();
                                    if (res.responseJSON.message || res.responseJSON.error)
                                        Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                }
                            });
                        },
                        showLoaderOnConfirm: true,
                        preConfirm: () => {
                            return new Promise((resolve) => {
                                $.ajax({
                                    type: $('form', Swal.getHtmlContainer()).attr('method'),
                                    data: $('form', Swal.getHtmlContainer()).serializeArray(),
                                    url: $('form', Swal.getHtmlContainer()).data('url'),
                                    success: function (res) {
                                        $(Swal.getHtmlContainer()).html(res).show();
                                        resolve(false);
                                    },
                                    error: function (res) {
                                        if (res.responseJSON.message || res.responseJSON.error)
                                            Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                        resolve(false);
                                    }
                                });
                            });
                        },
                        didClose: () => {
                            dt.api().ajax.reload(null, false);
                        }
                    });
                });
                $('a.notify-branch', oSettings.nTable).click(async function (e) {
                    e.preventDefault();
                    let $btn = $(this);
                    await Swal.fire({
                        customClass: 'swal-wide',
                        showCloseButton: true,
                        confirmButtonText: 'حفظ',
                        willOpen: () => {
                            Swal.showLoading();
                            $.ajax({
                                type: 'GET',
                                url: $btn.data('url'),
                                success: function (res) {
                                    Swal.disableLoading();
                                    $(Swal.getHtmlContainer()).html(res).show();
                                    $(Swal.getTitle()).html('إرسال رسالة').show();
                                },
                                error: function (res) {
                                    Swal.close();
                                    if (res.responseJSON.message || res.responseJSON.error)
                                        Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                }
                            });
                        },
                        showLoaderOnConfirm: true,
                        preConfirm: () => {
                            return new Promise((resolve) => {
                                $.ajax({
                                    type: $('form', Swal.getHtmlContainer()).attr('method'),
                                    data: $('form', Swal.getHtmlContainer()).serializeArray(),
                                    url: $('form', Swal.getHtmlContainer()).data('url'),
                                    success: function (res) {
                                        $(Swal.getHtmlContainer()).html(res).show();
                                        resolve(false);
                                    },
                                    error: function (res) {
                                        if (res.responseJSON.message || res.responseJSON.error)
                                            Swal.showValidationMessage(res.responseJSON.message || res.responseJSON.error);
                                        resolve(false);
                                    }
                                });
                            });
                        },
                        didClose: () => {
                            dt.api().ajax.reload(null, false);
                        }
                    });
                });
                $('a.delete-branch', oSettings.nTable).each(function () {
                    $(this).confirmation({
                        rootSelector: 'a.delete-case',
                        title: 'تأكيد حذف الفرع ؟',
                        popout: true,
                        singleton: true,
                        btnOkLabel: 'نعم',
                        btnCancelLabel: 'لا',
                        btnOkClass: 'btn btn-sm btn-danger',
                        btnCancelClass: 'btn btn-sm btn-light',
                        onConfirm: (event) => {
                            $.ajax({
                                type: 'DELETE',
                                url: $(this).data('deleteUrl'),
                                success: function (res) {
                                    dt.api().ajax.reload(null, false);
                                },
                                error: function (res) {
                                    if (res.responseJSON.message || res.responseJSON.error)
                                        toastr.error(res.responseJSON.message || res.responseJSON.error);
                                }
                            });
                        }
                    });
                })
            }
        });
        $('div.head-label').html('<h2 class="mb-0">الفروع</h2>');
    </script>
@endsection
