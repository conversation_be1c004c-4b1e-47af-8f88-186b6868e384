@php
    $rnd = \Illuminate\Support\Str::random();
@endphp
<div class="col-12 mb-25 sponsor-item">
    <div class="row">
        <input type="hidden" name="sponsors[{{ $rnd }}][type]" value="COMPANY">
        <div class="col-lg-3 col-md-3 col-sm-6">
            <select class="form-control" disabled readonly="">
                <option>شركة</option>
            </select>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6">
            <select class="form-control sponsor-company-id"
                    @error("sponsors.$k.company_id")
                    @else
                        readonly=""
                    @enderror
                    name="sponsors[{{ $rnd }}][company_id]">
                @isset($sponsorCompany)
                    <option value="{{ $sponsorCompany->id }}" selected>{{ $sponsorCompany->title }}</option>
                @endisset
            </select>
            @error("sponsors.$k.company_id")
            <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-lg-3 col-md-2 col-sm-4">
            <input type="text" class="form-control" value="{{ $support_amount ?: 0 }}"
                   name="sponsors[{{ $rnd }}][support_amount]" placeholder="قيمة الدعم">
        </div>
        <div class="form-group col-lg-2 col-md-2 col-sm-4">
            <div class="form-check form-check-inline mt-2">
                <input class="form-check-input" name="sponsors[{{ $rnd }}][confidential]" type="checkbox"
                       id="{{$rnd}}_confidential" @checked(@$confidential) value="1"/>
                <label class="form-check-label" for="{{$rnd}}_confidential">إخفاء الاسم</label>
            </div>
        </div>
        <div class="col-lg-1 col-md-2 col-sm-4">
            <button class="btn btn-danger sponsor-delete" type="button">
                <i class="fas fa-times"></i>
                &nbsp;
                حذف
            </button>
        </div>
    </div>
</div>
@unset($rnd)
