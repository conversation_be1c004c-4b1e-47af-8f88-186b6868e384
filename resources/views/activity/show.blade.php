@extends('layouts.contentLayoutMaster')
@section('title', $activity->title)
@section('content')
    @if(session()->has('message'))
        <div class="alert alert-success" role="alert">
            <div class="alert-body">
                <h4>{{ session('message') }}</h4>
            </div>
        </div>
    @endif
    @if(session()->has('errorMessage'))
        <div class="alert alert-danger" role="alert">
            <div class="alert-body">
                <h4>{!! nl2br(session('errorMessage')) !!}</h4>
            </div>
        </div>
    @endif

    <div class="card">
        <div class="card-header">
            <h4 class="card-title">بيانات البرنامج</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12 mb-1">
                    <label>@lang('activityColumns.title')</label>
                    <input type="text" class="form-control" disabled readonly value="{{ $activity->title }}"/>
                </div>

                <div class="col-md-4 col-4 mb-1">
                    <label>@lang('activityColumns.support_amount')</label>
                    <input type="text" class="form-control" disabled readonly
                           value="{{ number_format($activity->support_amount) }}"/>
                </div>
                @php($sponsors_support_amount = $activity->sponsors->sum('support_amount'))
                <div class="col-md-4 col-8 mb-1">
                    <label>@lang('activityColumns.sponsors_support_amount')</label>
                    <input type="text" class="form-control" disabled readonly
                           value="{{ number_format($sponsors_support_amount) }}"/>
                </div>

                <div class="col-md-4 col-12 mb-1">
                    <label>المتبقي</label>
                    <input type="text" class="form-control" disabled readonly
                           value="{{ $sponsors_support_amount > $activity->support_amount ? 0 : number_format($activity->support_amount - $sponsors_support_amount) }}"/>
                </div>

                <div class="col-12 mb-1">
                    <label>@lang('activityColumns.description')</label>
                    <textarea type="text" class="form-control" disabled readonly
                              style="height: 80px;min-height: 80px;max-height: 120px;">{{ $activity->description }}</textarea>
                </div>

                <div class="col-md-3 col-6 mb-1">
                    <label>@lang('activityColumns.age_from')</label>
                    <input type="text" class="form-control" disabled readonly value="{{ $activity->age_from }}"/>
                </div>

                <div class="col-md-3 col-6 mb-1">
                    <label>@lang('activityColumns.age_to')</label>
                    <input type="text" class="form-control" disabled readonly value="{{ $activity->age_to }}"/>
                </div>

                <div class="col-md-3 col-6 mb-1">
                    <label>@lang('activityColumns.start_at')</label>
                    <input type="text" class="form-control" style="direction: ltr;text-align: left;" disabled readonly
                           value="{{ optional($activity->start_at)->format('Y-m-d') }}"/>
                </div>

                <div class="col-md-3 col-6 mb-1">
                    <label for="activityEndAt">@lang('activityColumns.end_at')</label>
                    <input type="text" class="form-control" style="direction: ltr;text-align: left;" disabled readonly
                           value="{{ optional($activity->end_at)->format('Y-m-d') }}"/>
                </div>

                <div class="col-12 mb-1">
                    <label for="activityCover">@lang('activityColumns.cover')</label>
                    @if(!is_null(@$activity->cover_path))
                        <a href="{{ $activity->cover_url }}" target="_blank">
                            <img src="{{ $activity->cover_thumb_url }}" width="100" class="mt-25 border-secondary">
                        </a>
                    @endif
                </div>
            </div>
            @if($activity->sponsors->isNotEmpty())
                <div class="row">
                    <div class="col-12">
                        <hr>
                    </div>
                    <div class="col-12">
                        <h2>الرعاة :</h2>
                    </div>
                    <div class="col-12 mb-2">
                        <div class="row" id="SponsorList">
                            @foreach($activity->sponsors as $k => $sponsor)
                                @if($sponsor->sponsor_type === \App\Models\Company::class)
                                    <div class="col-12 mb-25">
                                        <div class="row">
                                            <div class="col-lg-3 col-md-4 col-5">
                                                <select class="form-control" disabled readonly>
                                                    <option>شركة</option>
                                                </select>
                                            </div>
                                            <div class="col-lg-5 col-md-8 col-7">
                                                <select class="form-control sponsor-company-id" disabled readonly>
                                                    @isset($sponsor->sponsor)
                                                        <option value="{{ $sponsor->sponsor->id }}"
                                                                selected>{{ $sponsor->confidential ? Str::repeat('*', 10) : $sponsor->sponsor->title }}</option>
                                                    @endisset
                                                </select>
                                            </div>
                                            {{--<div class="col-lg-3 col-md-4 col-sm-2">
                                                <input class="form-control" value="{{ $sponsor->support_amount }}"
                                                       disabled readonly/>
                                            </div>--}}
                                        </div>
                                    </div>
                                @elseif($sponsor->sponsor_type === \App\Models\User::class)
                                    <div class="col-12 mb-25">
                                        <div class="row">
                                            <div class="col-lg-3 col-md-4 col-5">
                                                <select class="form-control" disabled readonly>
                                                    <option>مستخدم</option>
                                                </select>
                                            </div>
                                            <div class="col-lg-5 col-md-8 col-7">
                                                <select class="form-control sponsor-company-id" disabled readonly>
                                                    @isset($sponsor->sponsor)
                                                        <option value="{{ $sponsor->sponsor->id }}"
                                                                selected>{{ $sponsor->confidential ? Str::repeat('*', 10) : $sponsor->sponsor->identifier }}</option>
                                                    @endisset
                                                </select>
                                            </div>
                                            {{--<div class="col-lg-3 col-md-4 col-sm-2">
                                                <input class="form-control" value="{{ $sponsor->support_amount }}"
                                                       disabled readonly/>
                                            </div>--}}
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                            {{--<div class="col-12 mb-25">
                                <div class="row">
                                    <div class="col-lg-6 col-md-8 col-sm-10">
                                        <input class="form-control bg-white" readonly disabled
                                               value="@lang('activityColumns.sponsors_support_amount')"/>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-2">
                                        <input class="form-control bg-white" readonly disabled
                                               value="{{ $activity->sponsors->sum('support_amount') }}"/>
                                    </div>
                                </div>
                            </div>--}}
                        </div>
                    </div>
                </div>
            @endif
            <div class="row">
                <div class="col-12">
                    <hr>
                </div>
                <div class="col-12">
                    <h2>المشاركين :</h2>
                </div>
                <div class="col-xl-10 col-lg-12 col-12 mb-2">
                    <div class="row" id="MemberList">
                        @foreach($activity->members as $k => $member)
                            <div class="col-12 mb-25 member-item">
                                <div class="row">
                                    <div class="mt-1 col-auto" style="width: 7%">
                                        <span>{{ $loop->iteration }}.</span>
                                    </div>
                                    <div class="col-xl col-lg col-md col-sm-8">
                                        <a href="{{ can('view', $member->user) ? route('admin.users.show', $member->user) : '#' }}"
                                           class="btn btn-outline-secondary form-control w-100 text-start" readonly>
                                            {{ $member->user->identifier }}
                                        </a>
                                    </div>
                                    <div class="col-xl-2 col-lg-2 col-md-2 col-sm-3 mt-sm-0 mt-25">
                                        <select class="form-control" disabled readonly>
                                            <option value="{{ $member->role->id }}"
                                                    selected>{{ $member->role->title }}</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-md-3 mt-md-0 mt-25">
                                        @php($phone = null)
                                        @if(!is_null($member->user->phone))
                                            @php($phone = $member->user->phone)
                                        @elseif(!is_null($member->user->father?->phone))
                                            @php($phone = 'الأب: ' . $member->user->father->phone)
                                        @elseif(!is_null($member->user->mother?->phone))
                                            @php($phone = 'الأم: ' . $member->user->mother->phone)
                                        @endif
                                        <input type="text" class="form-control" value="{{ $phone }}" readonly>
                                    </div>
                                    <hr class="col-12 d-md-none mt-1">
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="row mt-4">
                        <form action="{{route('admin.import-activity-members')}}" method="POST"
                              enctype="multipart/form-data">
                            @csrf
                            <div class="col-md-12 form-group">
                                <label>إستيراد من إكسل</label>
                                <input type="file" name="members_file" class="form-control">
                                @error('members_file')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <input type="hidden" name="activity_id" value="{{$activity->id}}">
                            <button type="submit" class="btn btn-outline-primary mt-1">إستيراد</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
