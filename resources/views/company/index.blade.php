@extends('layouts.contentLayoutMaster')

@section('title', __('sidebar.companies'))

@section('content')
    <div class="card">
        <div class="card-body">
            @if(session()->has('message'))
                <div class="alert alert-success" role="alert">
                    <div class="alert-body">
                        <h4>{{ session('message') }}</h4>
                    </div>
                </div>
            @endif
            <table class="datatables-ajax table">
                <thead>
                <tr>
                    <th>@lang('companyColumns.id')</th>
                    <th class="width-20-per select-search"
                        data-options="{{ json_encode($categories->map->only(['id', 'title'])) }}"
                        style="max-width: 200px;">
                        @lang('category')
                    </th>
                    <th>@lang('companyColumns.visible')</th>
                    <th>@lang('companyColumns.title')</th>
                    <th>@lang('companyColumns.sponsored_count')</th>
                    <th>@lang('companyColumns.created_at')</th>
                    <th class="width-10-per no-search" style="max-width: 150px;">#</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
@endsection

@section('vendor-style')
    {{-- vendor css files --}}
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/dataTables.bootstrap5.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/responsive.bootstrap5.min.css')) }}">
    <link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/buttons.bootstrap5.min.css')) }}">
    {{--<link rel="stylesheet" href="{{ url(mix('vendors/css/tables/datatable/rowGroup.bootstrap5.min.css')) }}">--}}
@endsection

@section('page-style')
    {{-- Page Css files --}}
@endsection

@section('vendor-script')
    {{-- vendor files --}}
    <script src="{{ url(mix('vendors/js/tables/datatable/jquery.dataTables.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.bootstrap5.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.responsive.min.js')) }}"></script>

    <script src="{{ url(mix('vendors/js/tables/datatable/datatables.buttons.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/buttons.bootstrap5.min.js')) }}"></script>
    <script src="{{ url(mix('vendors/js/tables/datatable/dataTables.rowGroup.min.js')) }}"></script>
@endsection

@section('page-script')
    {{-- Page js files --}}
    <script>
        let dt = $('table.datatables-ajax').dataTable({
            dom: '<"card-header border-bottom p-1"<"head-label"><"dt-action-buttons text-right"B>><"d-flex justify-content-between align-items-center mx-0 row"<"col-sm-12 col-md-auto"l><"col-sm-12 col-md-auto"f>>t<"d-flex justify-content-between mx-0 row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
            language: {
                "url": '{{ url(mix('vendors/js/tables/datatable/Arabic.json')) }}'
            },
            ajax: '{{ route('admin.companies.index') }}',
            search: {smart: true},
            processing: true,
            serverSide: true,
            columns: [
                {data: 'id', searchable: true},
                {data: 'category.id', searchable: true, orderable: false},
                {data: 'visible', searchable: false, orderable: false},
                {
                    data: 'title', searchable: true, orderable: false,
                    render: function (data, type, row) {
                        console.log(row);
                        let _html = `${data}`
                        if (row['thumb_url'] !== null)
                            _html = `<img src="${row['thumb_url']}" style="width: 48px;height: 48px;"/>&nbsp;${_html}`
                        return _html
                    }
                },
                {data: 'sponsored_count', searchable: false, orderable: false},
                {data: 'created_at', searchable: false, orderable: false},
                {
                    data: null, searchable: false, orderable: false, render: function (data, type, row) {
                        let _btns = [];
                        if (row.update_url)
                            _btns.push(`<a href="${row.update_url}"><i class="far fa-2x fa-edit"></i></a>`);
                        if (row.delete_url)
                            _btns.push(`<a href="javascript:void(0)" class="delete-btn" data-delete-url="${row.delete_url}"><i class="far fa-2x fa-trash-alt"></i></a>`);
                        return _btns.join('&nbsp;&nbsp;')
                    }
                },
            ],
            buttons: [
                    @can('create', \App\Models\Company::class)
                {
                    text: '<i class="fa fa-plus"></i>&nbsp;' + 'إضافة جديد',
                    className: 'btn btn-outline-primary waves-effect reload-btn',
                    action: function (e, dt, node, config) {
                        window.location = '{{ route('admin.companies.create') }}';
                    }
                }
                @endcan
            ],
            drawCallback: function (oSettings) {
                $('a.delete-btn', oSettings.nTable).each(function () {
                    $(this).confirmation({
                        rootSelector: 'a.delete-btn',
                        title: 'تأكيد حذف الشركة ؟',

                        popout: true,
                        singleton: true,
                        btnOkLabel: 'نعم',
                        btnCancelLabel: 'لا',
                        btnOkClass: 'btn btn-sm btn-danger',
                        btnCancelClass: 'btn btn-sm btn-light',

                        onConfirm: (event) => {
                            $.ajax({
                                type: 'DELETE',
                                url: $(this).data('deleteUrl'),
                                success: function (res) {
                                    $('table.datatables-ajax').DataTable().ajax.reload(null, false);
                                },
                                error: function (res) {
                                    if (res.responseJSON.message || res.responseJSON.error)
                                        toastr.error(res.responseJSON.message || res.responseJSON.error);
                                }
                            });
                        }
                    });
                });
            }
        });
        $('div.head-label').html('<h2 class="mb-0">ألقاب العائلة</h2>');
    </script>
@endsection
