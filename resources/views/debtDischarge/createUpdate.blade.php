@extends('layouts.contentLayoutMaster')

@section('title', (isset($debtDischarge) ? "تعديل تفريج كربة" : 'إضافة تفريج كربة جديدة'))

@section('content')
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">بيانات الحالة</h4>
        </div>
        <div class="card-body">
            {{--@dd($errors)--}}
            @if(session()->has('message'))
                <div class="alert alert-success" role="alert">
                    {{--<h4 class="alert-heading">Success</h4>--}}
                    <div class="alert-body">
                        <h4>{{ session('message') }}</h4>
                    </div>
                </div>
            @endif
            <form method="post" enctype="multipart/form-data" novalidate id="debtDischargeCreateUpdateForm"
                  action="{{isset($debtDischarge) ? route('admin.debt-discharges.update', $debtDischarge): route('admin.debt-discharges.store')}}">
                @csrf
                @isset($debtDischarge)
                    @method('put')
                @endisset
                <div class="row">
                    @if(isset($debtDischarge) && $debtDischarge->case_id)
                        <div class="col-xl-4 col-lg-6 col-12 mb-1">
                            <label for="debtDischargeCaseId">@lang('debtDischargeColumns.case_id')</label>
                            <input type="text" class="form-control" id="debtDischargeCaseId"
                                   placeholder="@lang('debtDischargeColumns.case_id')" disabled readonly
                                   value="{{ $debtDischarge->case_id }}"/>
                        </div>
                    @endif

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        @php
                            $oldBeneficiaryUserId = ($errors->isNotEmpty() ? old('beneficiary_user_id') : @$debtDischarge->beneficiary_user_id);
                            if(isset($oldBeneficiaryUserId) && !empty($oldBeneficiaryUserId))
                                $oldBeneficiaryUser = \App\Models\User::query()->find($oldBeneficiaryUserId);
                        @endphp
                        <div class="form-group @error('beneficiary_user_id') is-invalid @enderror">
                            <label for="debtDischargeBeneficiaryUserId"
                                   data-required>@lang('debtDischargeColumns.name')</label>
                            <select class="form-control @error('beneficiary_user_id') is-invalid @enderror"
                                    id="debtDischargeBeneficiaryUserId" name="beneficiary_user_id"
                                    data-allow-clear="true"
                                    data-placeholder="@lang('debtDischargeColumns.name')">
                                <option value=""></option>
                                @isset($oldBeneficiaryUser)
                                    <option value="{{$oldBeneficiaryUser->id}}"
                                            selected>{{ $oldBeneficiaryUser->identifier }}</option>
                                @endisset
                            </select>
                            @error('beneficiary_user_id')
                            <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        @php
                            $oldUserId = ($errors->isNotEmpty() ? old('responsible_user_id') : @$debtDischarge->responsible_user_id);
                            if(isset($oldUserId) && !empty($oldUserId))
                                $oldUser = \App\Models\User::query()->find($oldUserId);
                        @endphp
                        <div class="form-group @error('responsible_user_id') is-invalid @enderror">
                            <label
                                for="debtDischargeResponsibleUserId">@lang('debtDischargeColumns.responsible_user_id')</label>
                            <select class="form-control @error('responsible_user_id') is-invalid @enderror"
                                    id="debtDischargeResponsibleUserId"
                                    data-placeholder="@lang('debtDischargeColumns.responsible_user_id')"
                                    name="responsible_user_id" data-allow-clear="true">
                                <option value=""></option>
                                @isset($oldUser)
                                    <option value="{{$oldUser->id}}" selected>{{ $oldUser->identifier }}</option>
                                @endisset
                            </select>
                            @error('responsible_user_id')
                            <div class="invalid-feedback" style="display: block;">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="debtDischargeHealthStatus"
                               data-required>@lang('debtDischargeColumns.health_status')</label>
                        <input type="text" class="form-control @error('health_status') is-invalid @enderror"
                               id="debtDischargeHealthStatus" placeholder="@lang('debtDischargeColumns.health_status')"
                               autocomplete="debt-discharge-health_status" name="health_status" required
                               value="{{ $errors->isNotEmpty() ? old('health_status') : @$debtDischarge->health_status }}"/>
                        @error('health_status')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="debtDischargeFamilyMembers"
                               data-required>@lang('debtDischargeColumns.family_members')</label>
                        <input type="text" class="form-control @error('family_members') is-invalid @enderror"
                               id="debtDischargeFamilyMembers"
                               placeholder="@lang('debtDischargeColumns.family_members')"
                               autocomplete="debt-discharge-family_members" name="family_members" required
                               value="{{ $errors->isNotEmpty() ? old('family_members') : @$debtDischarge->family_members }}"/>
                        @error('family_members')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1">
                        <label for="debtDischargeNajizId">@lang('debtDischargeColumns.najiz_id')</label>
                        <input type="text" class="form-control @error('najiz_id') is-invalid @enderror"
                               id="debtDischargeNajizId"
                               placeholder="@lang('debtDischargeColumns.najiz_id')"
                               autocomplete="debt-discharge-najiz_id" required
                               name="najiz_id"
                               value="{{ $errors->isNotEmpty() ? old('najiz_id') : @$debtDischarge->najiz_id }}"/>
                        @error('najiz_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-xl-4 col-lg-6 col-12 mb-1 mt-2">
                        <div class="form-check form-check-inline">
                            <input type="checkbox" class="form-check-input"
                                   id="debtDischargeEhsanExists" name="ehsan_exists"
                                   @if((old('ehsan_exists') && $errors->isNotEmpty()) || @$debtDischarge->ehsan_exists) checked @endif>
                            <label class="form-check-label"
                                   for="debtDischargeEhsanExists">@lang('debtDischargeColumns.ehsan_exists')</label>
                        </div>
                    </div>

                    <div class="col-12 mt-1 mb-2">
                        <hr>
                    </div>

                    <div class="col-12 invoices-repeater">
                        <h3>الفواتير</h3>
                        <div data-repeater-list="invoices">
                            @forelse(($errors->isEmpty() ? (@$debtDischarge->invoices ?? []) : old('invoices', [])) as $i => $invoice)
                                <div class="row" data-repeater-item>
                                    <div class="col-xl-3 col-lg-6 col-12 mb-1">
                                        <label
                                            for="debtDischargeCreditorName">@lang('debtDischargeColumns.creditor_name')</label>
                                        <input type="text"
                                               class="form-control @error("invoices.$i.creditor_name") is-invalid @enderror"
                                               id="debtDischargeCreditorName"
                                               placeholder="@lang('debtDischargeColumns.creditor_name')"
                                               autocomplete="debt-discharge-creditor_name" required name="creditor_name"
                                               value="{{ $invoice['creditor_name'] }}"/>
                                        @error("invoices.$i.creditor_name")
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-xl-2 col-lg-6 col-12 mb-1">
                                        <label
                                            for="debtDischargeCreditorPhone">@lang('debtDischargeColumns.creditor_phone')</label>
                                        <input type="text"
                                               class="form-control @error("invoices.$i.creditor_phone") is-invalid @enderror"
                                               id="debtDischargeCreditorPhone"
                                               placeholder="@lang('debtDischargeColumns.creditor_phone')"
                                               autocomplete="debt-discharge-creditor_phone" required
                                               name="creditor_phone"
                                               value="{{ $invoice['creditor_phone'] }}"/>
                                        @error("invoices.$i.creditor_phone")
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-xl-2 col-lg-6 col-12 mb-1">
                                        <label
                                            for="debtDischargeInvoiceId">@lang('debtDischargeColumns.invoice_id')</label>
                                        <input type="text"
                                               class="form-control @error("invoices.$i.invoice_id") is-invalid @enderror"
                                               id="debtDischargeInvoiceId"
                                               placeholder="@lang('debtDischargeColumns.invoice_id')"
                                               autocomplete="debt-discharge-invoice_id" required
                                               name="invoice_id"
                                               value="{{ $invoice['invoice_id'] }}"/>
                                        @error("invoices.$i.invoice_id")
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-xl-2 col-lg-6 col-12 mb-1">
                                        <label for="debtDischargeAmount"
                                               data-required>@lang('debtDischargeColumns.amount')</label>
                                        <input type="text"
                                               class="form-control @error("invoices.$i.amount") is-invalid @enderror"
                                               id="debtDischargeAmount"
                                               placeholder="@lang('debtDischargeColumns.amount')"
                                               autocomplete="debt-discharge-amount" required name="amount"
                                               value="{{ $invoice['amount'] }}"/>
                                        @error("invoices.$i.amount")
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-xl-2 col-lg-6 col-12 mb-1">
                                        <label for="debtDischargeDueAt"
                                               data-required>@lang('debtDischargeColumns.due_at')</label>
                                        <input type="text"
                                               class="form-control due-at-input @error("invoices.$i.due_at") is-invalid @enderror"
                                               id="debtDischargeDueAt"
                                               placeholder="@lang('debtDischargeColumns.due_at')"
                                               autocomplete="debt-discharge-due_at" required name="due_at"
                                               value="{{ $invoice['due_at'] }}"/>
                                        @error("invoices.$i.due_at")
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col">
                                        <button class="btn btn-outline-danger text-nowrap px-1 mt-2"
                                                data-repeater-delete type="button">
                                            <i data-feather="x" class="me-25"></i>
                                            <span>حذف</span>
                                        </button>
                                    </div>
                                    <div class="col-12">
                                        <hr>
                                    </div>
                                </div>
                            @empty
                                <div class="row" data-repeater-item>
                                    <div class="col-xl-3 col-lg-6 col-12 mb-1">
                                        <label
                                            for="debtDischargeCreditorName">@lang('debtDischargeColumns.creditor_name')</label>
                                        <input type="text" class="form-control" id="debtDischargeCreditorName"
                                               placeholder="@lang('debtDischargeColumns.creditor_name')"
                                               autocomplete="debt-discharge-creditor_name" required
                                               name="creditor_name" value=""/>
                                    </div>

                                    <div class="col-xl-2 col-lg-6 col-12 mb-1">
                                        <label
                                            for="debtDischargeCreditorPhone">@lang('debtDischargeColumns.creditor_phone')</label>
                                        <input type="text" class="form-control" id="debtDischargeCreditorPhone"
                                               placeholder="@lang('debtDischargeColumns.creditor_phone')"
                                               autocomplete="debt-discharge-creditor_phone" required
                                               name="creditor_phone" value=""/>
                                    </div>

                                    <div class="col-xl-2 col-lg-6 col-12 mb-1">
                                        <label
                                            for="debtDischargeInvoiceId">@lang('debtDischargeColumns.invoice_id')</label>
                                        <input type="text" class="form-control" id="debtDischargeInvoiceId"
                                               placeholder="@lang('debtDischargeColumns.invoice_id')"
                                               autocomplete="debt-discharge-invoice_id" required
                                               name="invoice_id" value=""/>
                                    </div>

                                    <div class="col-xl-2 col-lg-6 col-12 mb-1">
                                        <label for="debtDischargeAmount"
                                               data-required>@lang('debtDischargeColumns.amount')</label>
                                        <input type="text" class="form-control" id="debtDischargeAmount"
                                               placeholder="@lang('debtDischargeColumns.amount')"
                                               autocomplete="debt-discharge-amount" required
                                               name="amount" value=""/>
                                    </div>

                                    <div class="col-xl-2 col-lg-6 col-12 mb-1">
                                        <label for="debtDischargeDueAt"
                                               data-required>@lang('debtDischargeColumns.due_at')</label>
                                        <input type="text" class="form-control due-at-input" id="debtDischargeDueAt"
                                               placeholder="@lang('debtDischargeColumns.due_at')"
                                               autocomplete="debt-discharge-due_at" required name="due_at" value=""/>
                                    </div>
                                    <div class="col">
                                        <button class="btn btn-outline-danger text-nowrap px-1 mt-2"
                                                data-repeater-delete type="button">
                                            <i data-feather="x" class="me-25"></i>
                                            <span>حذف</span>
                                        </button>
                                    </div>
                                    <div class="col-12">
                                        <hr>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                        <div class="row">
                            <div class="col-12 mt-1">
                                <button class="btn btn-icon btn-primary" type="button" data-repeater-create>
                                    <i data-feather="plus" class="me-25"></i>
                                    <span>إضافة فاتورة جديد</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 mt-1 mb-2">
                        <hr>
                    </div>

                    @if(isset($debtDischarge) && $debtDischarge->attachments()->count() > 0)
                        <div class="col-12 mt-2 mb-2">
                            <div class="row">
                                @foreach(['national_id', 'invoice'] as $type)
                                    @isset($debtDischarge->{$type.'_attachment'})
                                        <div class="col-auto">
                                            <h3>@lang('attachments.types.'.$type)</h3>
                                            <a href="{{ $debtDischarge->{$type.'_attachment'}->url }}" target="_blank">
                                                <img
                                                    src="{{ $debtDischarge->{$type.'_attachment'}->file_type === 'pdf' ? url(mix('images/pdf.png')) : $debtDischarge->{$type.'_attachment'}->thumb_url }}"
                                                    @if($debtDischarge->{$type.'_attachment'}->file_type === 'pdf') height="50"
                                                    @endif
                                                    alt="{{$type}}_thumb">
                                            </a>
                                        </div>
                                    @endisset
                                @endforeach
                                @foreach($debtDischarge->custom_attachments as $attachment)
                                    <div class="col-auto">
                                        <h3>{{$attachment->title}}</h3>
                                        <a href="{{ $attachment->url }}" target="_blank">
                                            <img
                                                src="{{ $attachment->file_type === 'pdf' ? url(mix('images/pdf.png')) : $attachment->thumb_url }}"
                                                @if($attachment->file_type === 'pdf') height="50" @endif
                                                alt="{{$attachment->title}}">
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <div class="col-lg-6 col-12 mb-1">
                        <div class="form-group">
                            <label for="debtDischargeNationalIdImage" @if(!isset($debtDischarge)) data-required @endif>صورة
                                الهوية</label>
                            <div class="@error('national_id_image') is-invalid @enderror">
                                <input type="file"
                                       class="form-control @error('national_id_image') is-invalid @enderror"
                                       id="debtDischargeNationalIdImage"
                                       name="national_id_image"/>
                                <label class="form-label" for="debtDischargeNationalIdImage">أرفق الهوية</label>
                            </div>
                            @error('national_id_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-lg-6"></div>

                    <div class="col-lg-6 col-12 mb-1">
                        <div class="form-group">
                            <label for="debtDischargeInvoiceImage" @if(!isset($debtDischarge)) data-required @endif>صك
                                المطالبة</label>
                            <div class="@error('invoice_image') is-invalid @enderror">
                                <input type="file" id="debtDischargeInvoiceImage" name="invoice_image"
                                       class="form-control @error('invoice_image') is-invalid @enderror"/>
                                <label class="form-label" for="debtDischargeInvoiceImage">أرفق صك
                                    المطالبة</label>
                            </div>
                            @error('invoice_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-lg-6"></div>
                </div>

                <div class="col-12 attachments-repeater">
                    <h3>الشواهد</h3>
                    <div data-repeater-list="attachments">
                        <div class="row" data-repeater-item>
                            <div class="col-4">
                                <input type="text" class="form-control" placeholder="العنوان" name="title"
                                       id="{{ \Illuminate\Support\Str::random() }}"/>
                            </div>
                            <div class="col-6">
                                <div class="mb-1">
                                    <input type="file" class="form-control"
                                           id="{{ \Illuminate\Support\Str::random() }}" name="file"/>
                                    <label class="form-label" for="{{ \Illuminate\Support\Str::random() }}">
                                        إرفاق الشاهد
                                    </label>
                                </div>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-danger text-nowrap px-1" data-repeater-delete
                                        type="button">
                                    <i data-feather="x" class="me-25"></i>
                                    <span>حذف</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    @error('attachments')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                    @error('attachments.*')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                    <div class="row">
                        <div class="col-12 mt-1">
                            <button class="btn btn-icon btn-primary" type="button" data-repeater-create>
                                <i data-feather="plus" class="me-25"></i>
                                <span>إضافة جديد</span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="float-end">
                    <button class="btn btn-primary mt-4" type="submit">
                        @isset($debtDischarge)
                            تعديل
                        @else
                            إضافة
                        @endisset
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('vendor-style')
    <!-- vendor css files -->
@endsection

@section('vendor-script')
    <!-- vendor files -->
    <script src="{{ url(mix('vendors/js/forms/repeater/jquery.repeater.min.js')) }}"></script>
@endsection

@section('page-script')
    <script>
        $(function () {
            $('.attachments-repeater').repeater({
                isFirstItemUndeletable: true,
                show: function () {
                    $(this).slideDown()
                    fileId = `attachment_file_${Date.now()}`
                    $('.form-control', this)[0].id = fileId
                    $('.form-label', this)[0].attributes.for.value = fileId
                    id = `attachment_title_${Date.now()}`
                    $('input[type=text]', this)[0].id = id
                    if (feather)
                        feather.replace({width: 14, height: 14})
                },
                hide: function (deleteElement) {
                    if (confirm('تأكيد حذف الشواهد ؟'))
                        $(this).slideUp(deleteElement)
                }
            });
            $('.invoices-repeater').repeater({
                isFirstItemUndeletable: true,
                show: function () {
                    $(this).slideDown()
                    if (feather)
                        feather.replace({width: 14, height: 14})
                },
                hide: function (deleteElement) {
                    if (confirm('تأكيد حذف الفاتورة ؟'))
                        $(this).slideUp(deleteElement)
                }
            });
            let debtDischargeResponsibleUserId = $('#debtDischargeResponsibleUserId')[0];
            $(debtDischargeResponsibleUserId).select2({
                // the following code is used to disable x-scrollbar when click in select input and
                // take 100% width in responsive also
                theme: 'bootstrap-5',
                //dropdownAutoWidth: true,
                width: '100%',
                dropdownParent: debtDischargeResponsibleUserId.parentElement,
                ajax: {
                    url: "{{ route('admin.web-api.users') }}",
                    dataType: 'json',
                    method: 'post',
                    delay: 250,
                    cache: true,
                    data: function (params) {
                        return {
                            q: params.term,
                            _token: '{{ csrf_token() }}',
                            page: params.page
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data,
                            pagination: {
                                more: (params.page * 15) < data.total
                            }
                        };
                    }
                },
                escapeMarkup: markup => markup,
                minimumInputLength: 1,
                templateResult: function (user) {
                    if (user.loading)
                        return user.text;
                    let markup = "<div class='select2-result-fabricsitory clearfix d-flex'>" +
                        "<div class='select2-result-fabricsitory__meta'>" +
                        "<div class='select2-result-fabricsitory__title fs-lg fw-500'>" + (user.full_name || user.name) + "</div>";
                    if (user.family_user_id)
                        markup += "<div class='select2-result-fabricsitory__description fs-xs opacity-80 mb-1'>" + user.family_user_id + "</div>";
                    markup += "</div></div>";
                    return markup;
                },
                templateSelection: function (user) {
                    return user.text ? user.text : `#${user.family_user_id} ${(user.full_name || user.name)}`;
                },
            });

            let debtDischargeBeneficiaryUserId = $('#debtDischargeBeneficiaryUserId')[0];
            if (debtDischargeBeneficiaryUserId)
                $(debtDischargeBeneficiaryUserId).select2({
                    // the following code is used to disable x-scrollbar when click in select input and
                    // take 100% width in responsive also
                    theme: 'bootstrap-5',
                    //dropdownAutoWidth: true,
                    width: '100%',
                    dropdownParent: debtDischargeBeneficiaryUserId.parentElement,
                    ajax: {
                        url: "{!! signedRoute('admin.web-api.users', ['all' => true], now()->addHour()) !!}",
                        dataType: 'json',
                        method: 'post',
                        delay: 250,
                        cache: true,
                        data: function (params) {
                            return {
                                q: params.term,
                                _token: '{{ csrf_token() }}',
                                page: params.page
                            };
                        },
                        processResults: function (data, params) {
                            params.page = params.page || 1;
                            return {
                                results: data.data,
                                pagination: {
                                    more: (params.page * 15) < data.total
                                }
                            };
                        }
                    },
                    escapeMarkup: markup => markup,
                    minimumInputLength: 1,
                    templateResult: function (user) {
                        if (user.loading)
                            return user.text;
                        let markup = "<div class='select2-result-fabricsitory clearfix d-flex'>" +
                            "<div class='select2-result-fabricsitory__meta'>" +
                            "<div class='select2-result-fabricsitory__title fs-lg fw-500'>" + (user.full_name || user.name)
                            + (user.branch_name ? ` (${user.branch_name})` : '') + "</div>";
                        if (user.family_user_id)
                            markup += "<div class='select2-result-fabricsitory__description fs-xs opacity-80 mb-1'>" + user.family_user_id + "</div>";
                        markup += "</div></div>";
                        return markup;
                    },
                    templateSelection: function (user) {
                        return user.text ? user.text : `#${user.family_user_id} ${(user.full_name || user.name)}`;
                    },
                });
        })
    </script>
@endsection
