<?php

namespace App\Providers;

use Log;
use Closure;
use Exception;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\Vite;
use App\Filament\Auth\LoginResponse;
use Illuminate\Support\Facades\Blade;
use App\Filament\Auth\LogoutResponse;
use Illuminate\Support\ServiceProvider;
use Filament\Tables\Columns\ImageColumn;
use Filament\Support\Facades\FilamentView;
use Filament\Infolists\Components\ImageEntry;
use Filament\Http\Responses\Auth\LoginResponse as BaseLoginResponse;
use Filament\Http\Responses\Auth\LogoutResponse as BaseLogoutResponse;


class FilamentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->app->bind(BaseLoginResponse::class, LoginResponse::class);
        $this->app->bind(BaseLogoutResponse::class, LogoutResponse::class);

        FilamentView::registerRenderHook(
            PanelsRenderHook::HEAD_END,
            fn(): string => Blade::render('{{ Vite::useHotFile(public_path(\'filament/hot\'))->useBuildDirectory(\'filament\')->withEntryPoints([\'resources/js/app.js\'])}}'),
        );
        FilamentView::registerRenderHook(
            PanelsRenderHook::HEAD_END,
            fn(): string => "\n" . view('components.env-js')->render(),
        );
        foreach ([ImageEntry::class, ImageColumn::class] as $class) {

            $class::macro('withFallbackAvatar', function (string $fallbackUrl = null, string $brokenImageUrl = null, array|Closure $customAttributes = []) {
                $defaultFallback = static::getAssetUrl('resources/img/avatar.png');
                $defaultBrokenImage = static::getAssetUrl('resources/img/broken.png');

                $fallbackUrl = $fallbackUrl ?? $defaultFallback;
                $brokenImageUrl = $brokenImageUrl ?? $defaultBrokenImage;

                return $this
                    ->defaultImageUrl($fallbackUrl)
                    ->extraImgAttributes(static::getImageAttributes($brokenImageUrl, $customAttributes));
            });

            $class::macro('withFallback', function (string $fallbackUrl = null, array|Closure $customAttributes = []) {
                $fallbackUrl = $fallbackUrl ?? static::getAssetUrl('resources/img/avatar.png');

                return $this
                    ->defaultImageUrl($fallbackUrl)
                    ->extraImgAttributes(static::getImageAttributes($fallbackUrl, $customAttributes));
            });

            $class::macro('withFallbackFromStorage', function (string $disk = 'public', string $path = 'images/avatar.png', array|Closure $customAttributes = []) {
                $fallbackUrl = Storage::disk($disk)->url($path);

                return $this
                    ->defaultImageUrl($fallbackUrl)
                    ->extraImgAttributes(static::getImageAttributes($fallbackUrl, $customAttributes));
            });

            $class::macro('withCustomFallback', function (callable $fallbackResolver) {
                return $this->getStateUsing(function ($record) use ($fallbackResolver) {
                    $originalUrl = $this->getState();

                    if (empty($originalUrl) || !static::isImageAccessible($originalUrl)) {
                        return $fallbackResolver($record, $originalUrl);
                    }

                    return $originalUrl;
                });
            });

            // Helper methods
            $class::macro('getAssetUrl', function (string $path) {
                try {
                    return Vite::useHotFile(public_path('filament/hot'))
                        ->useBuildDirectory('filament')
                        ->asset($path);
                } catch (Exception $e) {
                    // Fallback to public path if Vite fails
                    return asset('filament/' . ltrim($path, 'resources/'));
                }
            });

            $class::macro('getImageAttributes', function (string $fallbackUrl, array|Closure $customAttributes = []) {
                $defaultAttributes = [
                    'onerror' => "
                if (!this.hasAttribute('data-fallback-applied')) {
                    this.src = '{$fallbackUrl}';
                    this.setAttribute('data-fallback-applied', 'true');
                    this.classList.add('image-fallback');
                }
            ",
                    'loading' => 'lazy',
                    'onload' => "
                this.style.opacity = '1';
                this.classList.remove('loading-error');
                this.classList.add('image-loaded');
            ",
                    'style' => 'opacity: 0.8; transition: opacity 0.3s ease;',
                    'data-original-src' => '', // Will be set by JavaScript
                ];


                return function () use ($defaultAttributes, $customAttributes) {
                    return array_merge($defaultAttributes, self::evaluate($customAttributes));
                };
            });

            $class::macro('isImageAccessible', function (string $url, int $timeout = 5) {
                if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
                    return false;
                }

                try {
                    $context = stream_context_create([
                        'http' => [
                            'method' => 'HEAD',
                            'timeout' => $timeout,
                            'user_agent' => 'Laravel Image Checker/1.0',
                        ],
                    ]);

                    $headers = @get_headers($url, true, $context);

                    if (!$headers) {
                        return false;
                    }

                    $statusCode = intval(substr($headers[0], 9, 3));
                    return $statusCode >= 200 && $statusCode < 400;

                } catch (Exception $e) {
                    Log::debug('Image accessibility check failed', [
                        'url' => $url,
                        'error' => $e->getMessage(),
                    ]);
                    return false;
                }
            });

            // Advanced retry mechanism
            $class::macro('withRetryFallback', function (string $fallbackUrl = null, int $maxRetries = 2, int $retryDelay = 1000) {
                $fallbackUrl = $fallbackUrl ?? static::getAssetUrl('resources/img/avatar.png');

                return $this
                    ->defaultImageUrl($fallbackUrl)
                    ->extraImgAttributes([
                        'onerror' => "
                    if (typeof this.retryCount === 'undefined') {
                        this.retryCount = 0;
                        this.originalSrc = this.src;
                    }

                    if (this.retryCount < {$maxRetries}) {
                        this.retryCount++;
                        this.classList.add('image-retrying');

                        setTimeout(() => {
                            const separator = this.originalSrc.includes('?') ? '&' : '?';
                            this.src = this.originalSrc + separator + 'retry=' + this.retryCount + '&t=' + Date.now();
                        }, {$retryDelay});
                    } else {
                        this.src = '{$fallbackUrl}';
                        this.classList.remove('image-retrying');
                        this.classList.add('image-fallback-final');
                        this.onerror = null;
                    }
                ",
                        'onload' => "
                    this.style.opacity = '1';
                    this.classList.remove('loading-error', 'image-retrying');
                    this.classList.add('image-loaded');
                ",
                        'loading' => 'lazy',
                        'style' => 'opacity: 0.8; transition: opacity 0.3s ease;',
                    ]);
            });

            // Placeholder while loading
            $class::macro('withPlaceholder', function (string $placeholderUrl = null, string $fallbackUrl = null) {
                $placeholderUrl = $placeholderUrl ?? 'data:image/svg+xml;base64,' . base64_encode(
                    '<svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                <rect width="40" height="40" fill="#f3f4f6"/>
                <text x="20" y="25" text-anchor="middle" fill="#9ca3af" font-size="12">...</text>
            </svg>'
                );

                $fallbackUrl = $fallbackUrl ?? static::getAssetUrl('resources/img/avatar.png');

                return $this
                    ->defaultImageUrl($placeholderUrl)
                    ->extraImgAttributes([
                        'data-src' => '', // Will be set by the component
                        'onload' => "
                    if (this.src !== this.dataset.src && this.dataset.src) {
                        this.src = this.dataset.src;
                    } else {
                        this.style.opacity = '1';
                        this.classList.add('image-loaded');
                    }
                ",
                        'onerror' => "
                    if (this.src !== '{$fallbackUrl}') {
                        this.src = '{$fallbackUrl}';
                    } else {
                        this.classList.add('image-fallback');
                    }
                ",
                        'loading' => 'lazy',
                        'style' => 'opacity: 0.8; transition: opacity 0.3s ease;',
                    ]);
            });
        }

    }
}
