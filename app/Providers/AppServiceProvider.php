<?php

namespace App\Providers;

use Cache;
use Exception;
use App\Models\User;
use ArPHP\I18N\Arabic;
use Livewire\Livewire;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use App\Policies\RolePolicy;
use Illuminate\Http\Request;
use App\Settings\AppSettings;
use App\Jobs\PrepareCsvExport;
use Laravel\Passport\Passport;
use App\Services\AvatarService;
use App\Policies\UserViewPolicy;
use Filament\Infolists\Infolist;
use Laravel\Pulse\Facades\Pulse;
use Filament\Actions\DeleteAction;
use Illuminate\Support\HtmlString;
use Spatie\Permission\Models\Role;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Facades\Gate;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Route;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Validator;
use App\Rules\JsonMetadataValidation;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;
use Illuminate\Cache\RateLimiting\Limit;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\RestoreAction;
use Stancl\Tenancy\Database\Models\Domain;
use Illuminate\Support\Facades\RateLimiter;
use Archilex\AdvancedTables\Models\UserView;
use Filament\Infolists\Components\TextEntry;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\DateTimePicker;
use Filament\Tables\Actions\ForceDeleteAction;
use Illuminate\Http\Resources\Json\JsonResource;
use Filament\Support\Services\RelationshipJoiner;
use Illuminate\Routing\PendingResourceRegistration;
use Wnx\LaravelBackupRestore\Actions\ImportDumpAction;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Livewire\Features\SupportFileUploads\FilePreviewController;
use Filament\Actions\Exports\Jobs\PrepareCsvExport as BasePrepareCsvExport;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
        /*if ($this->app->runningInConsole())
            config()->set('app.locale', 'en');*/
        Str::macro('ucwords', fn($value) => Str::title($value));
        Model::unguard();
        $this->app->singleton(AvatarService::class, function ($app) {
            return new AvatarService($app->make(AppSettings::class));
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register custom validation rules
        Validator::extend('json_metadata_validation', function ($attribute, $value, $parameters, $validator) {
            $rule = new JsonMetadataValidation();
            $failed = false;
            $rule->validate($attribute, $value, function ($message) use (&$failed, $validator, $attribute) {
                $failed = true;
                $validator->errors()->add($attribute, $message);
            });
            return !$failed;
        });

        $this->app->bind(BasePrepareCsvExport::class, PrepareCsvExport::class);
        $this->app->alias(
            \App\Support\Services\RelationshipJoiner::class,
            RelationshipJoiner::class,
        );
        Infolist::$defaultNumberLocale = 'en-US';

        TextEntry::macro('hiddenEmpty', function () {
            return $this->hidden(fn($state) => empty($state));
        });
        TextEntry::macro('riyal', function ($decimal = 0) {
            return $this
                ->formatStateUsing(fn($state) => new HtmlString(number_format($state ? $state : 0, $decimal) . ' <span class="icon-saudi_riyal"></span>'));
        });
        TextColumn::macro('riyal', function ($decimal = 0) {
            return $this
                ->formatStateUsing(fn($state) => new HtmlString(number_format($state ? $state : 0, $decimal) . ' <span class="icon-saudi_riyal"></span>'));
        });

        TextEntry::macro('sensitive', function () {
            return $this
                ->when(!isShowSensitiveData(), fn($entry) => $entry->url(null)->formatStateUsing(fn() => '********'));
        });
        TextColumn::macro('sensitive', function () {

            return $this
                ->when(!isShowSensitiveData(), fn($entry) => $entry->url(null)->formatStateUsing(fn() => '********'));
        });

        ViewAction::configureUsing(function ($action) {
            $action->hiddenLabel()
                ->iconSize('md');
        });
        EditAction::configureUsing(function ($action) {
            $action->hiddenLabel()
                ->iconSize('md');
        });
        \Filament\Tables\Actions\DeleteAction::configureUsing(function ($action) {
            $action->hiddenLabel()
                ->iconSize('md')
                ->requiresConfirmation();
        });
        RestoreAction::configureUsing(function ($action) {
            $action->hiddenLabel()
                ->iconSize('md')
                ->requiresConfirmation();
        });
        ForceDeleteAction::configureUsing(function ($action) {
            $action->hiddenLabel()
                ->iconSize('md')
                ->requiresConfirmation();
        });
        FileUpload::configureUsing(function ($action) {
            $action
                ->imageEditor()
                ->fetchFileInformation(false);
        });
        DeleteAction::configureUsing(function ($action) {
            $action->requiresConfirmation();
        });
        DatePicker::configureUsing(function (DatePicker $datePicker): void {
            $datePicker
                ->suffixAction(
                    Action::make('clear')
                        ->icon('heroicon-o-x-mark')
                        //->color('secondary')
                        ->size('sm')
                        ->action(fn($component) => $component->state(null))
                )
                ->native(false);
        });
        DateTimePicker::configureUsing(function (DateTimePicker $dateTimePicker): void {
            $dateTimePicker
                ->suffixAction(
                    Action::make('clear')
                        ->icon('heroicon-o-x-mark')
                        //->color('secondary')
                        ->size('sm')
                        ->action(fn($component) => $component->state(null))
                )
                ->native(false);
        });
        Select::configureUsing(function (Select $select): void {
            $select->native(false);
        });
        SelectFilter::configureUsing(function (SelectFilter $select): void {
            $select->native(false);
        });
        Table::configureUsing(function (Table $table): void {
            $table->paginated([10, 50, 100, 200])
                ->defaultPaginationPageOption(10);
        });


        Route::bind('spouse', function ($id) {
            return User::withoutGlobalScopes(['family_users'])->where('id', $id)->firstOrFail();
        });
        JsonResource::withoutWrapping();
        try {
            $cache = Cache::driver('central_redis');
            $apiDomains = $cache->get('api_domains');
            if (empty($apiDomains)) {
                $apiDomains = Domain::query()->where('type', 'API')->pluck('domain')->toArray();
                $cache->set('api_domains', $apiDomains, 60 * 24 * 7);
            }
        } catch (Exception $e) {
            $apiDomains = [];
        }

        //Route::pattern('api_domain', '(api)\.([a-z0-9.]+)');
        Route::pattern('api_domain', '(' . implode('|', array_map(fn($domain) => str_replace('.', '\\.', $domain), $apiDomains)) . ')');
        Route::pattern('central_domain', '(' . implode('|', array_map(fn($domain) => str_replace('.', '\\.', $domain), config('tenancy.central_domains'))) . ')');

        Livewire::setUpdateRoute(function ($handle) {
            return Route::post('/livewire/update', $handle)
                ->middleware(
                    'web',
                    'universal',
                    InitializeTenancyByDomain::class, // or whatever tenancy middleware you use
                );
        });
        FilePreviewController::$middleware = ['web', 'universal', InitializeTenancyByDomain::class];

        Pulse::user(fn($user) => [
            'name' => $user->name,
            'extra' => $user->family_user_id,
            'avatar' => $user->profile_photo_url,
        ]);
        /*Gate::define('viewPulse', function (User $user) {
            return $this->app->environment('local') || $user->is_system_admin === true;
        });*/

        UrlGenerator::macro('prefixedSignedRoute', function ($name, $parameters = [], $expiration = null, $absolute = true, $url = null) {
            $signedUrl = $this->signedRoute($name, $parameters, $expiration, $absolute);
            if ($url) {
                $signedUrl = str_replace(url('/'), trim($url, '/'), $signedUrl);
            }
            return $signedUrl;
        });

        Passport::ignoreRoutes();
        Passport::tokensExpireIn(now()->addDays(90));
        Passport::refreshTokensExpireIn(now()->addDays(120));
        Passport::personalAccessTokensExpireIn(now()->addDays(180));
        Passport::tokensCan([
            'portal' => 'Access to Portal',
        ]);
        Gate::policy(Role::class, RolePolicy::class);
        Gate::policy(UserView::class, UserViewPolicy::class);


        PendingResourceRegistration::macro('withRestore', function () {
            $this->options['only'] = array_merge($this->options['only'] ?? [], ['restore', 'permanentlyDelete']);
            $this->options['trashed'] = array_merge($this->options['trashed'] ?? [], ['restore', 'permanentlyDelete']);
            return $this;
        });

        foreach (
            [
                \Staudenmeir\LaravelAdjacencyList\Eloquent\Builder::class,
                \Illuminate\Database\Query\Builder::class,
                Builder::class,
            ] as $builder
        ) {
            call_user_func([$builder, 'macro'], 'driver', function () {
                /** @noinspection PhpUndefinedMethodInspection */
                return $this->getConnection()->getConfig('driver');
            });
            call_user_func([$builder, 'macro'], 'whereSplit', function ($column, $value, $delimiter = ' ', $boolean = 'and') {
                /** @noinspection PhpUndefinedMethodInspection */
                match ($this->driver()) {
                    'mariadb', 'mysql' => $this->{$boolean === 'or' ? 'orWhereRaw' : 'whereRaw'}("SUBSTRING_INDEX(`$column`, '$delimiter', 1) = ?", [$value]),
                    'pgsql' => $this->{$boolean === 'or' ? 'orWhereRaw' : 'whereRaw'}("split_part($column, '$delimiter', 1) = ?", [$value]),
                    //'sqlite' => ,
                };
            });
        }

        RateLimiter::for('api', function (Request $request) {
            return [
                Limit::perMinute(strtoupper(config('app.env')) === 'LOCAL' ? 300 : 60)
                    ->by($request->user() ? "user:{$request->user()->id}" : "ip:{$request->ip()}"),
            ];
        });

        Gate::define('viewLogViewer', function (?User $user) {
            return $this->app->environment('local') || $user?->is_system_admin === true;
        });

        Model::preventSilentlyDiscardingAttributes(!$this->app->isProduction());
        Model::preventAccessingMissingAttributes(!$this->app->isProduction());

        //\App::alias(ConfirmsPasswords::class, \App\Livewire\ConfirmsPasswords::class);

        $this->app->singleton(Arabic::class, function () {
            return new Arabic();
        });

        $this->app->alias(
            \App\LaravelBackupRestore\Actions\ImportDumpAction::class,
            ImportDumpAction::class
        );

        $this->validators();
    }

    public function validators(): void
    {
        Validator::extend('words_gt', function ($attribute, $value, $params, $validator) {
            try {
                $count = intval($params[0]);
                if (!($count > 0))
                    $count = 1;

                $validator->addReplacer('words_gt', function ($message, $attribute, $rule, $parameters) {
                    return str_replace([':count'], $parameters, $message);
                });

                return count(explode(' ', $value)) > $count;
            } catch (Exception $e) {
                return false;
            }
        });

        Validator::extend('words_lte', function ($attribute, $value, $params, $validator) {
            try {
                $count = intval($params[0]);
                if (!($count > 0))
                    $count = 1;

                $validator->addReplacer('words_lte', function ($message, $attribute, $rule, $parameters) {
                    return str_replace([':count'], $parameters, $message);
                });

                return count(explode(' ', $value)) <= $count;
            } catch (Exception $e) {
                return false;
            }
        });

        Validator::extend('words_eq', function ($attribute, $value, $params, $validator) {
            try {
                $count = intval($params[0]);
                if (!($count > 0))
                    $count = 1;

                $validator->addReplacer('words_eq', function ($message, $attribute, $rule, $parameters) {
                    return str_replace([':count'], $parameters, $message);
                });

                return count(explode(' ', $value)) === $count;
            } catch (Exception $e) {
                return false;
            }
        });
    }

    private function setCustomResolverForPgsql()
    {
        /*Connection::resolverFor('pgsql', function ($connection, $database, $prefix, $config) {
            return new PostgresConnection($connection, $database, $prefix, $config);
        });*/
        /*FilamentAsset::register([
            AlpineComponent::make('file-upload', __DIR__ . '/../dist/components/file-upload.js'),
        ], 'filament/forms');*/
    }
}
