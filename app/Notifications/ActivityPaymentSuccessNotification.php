<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use App\Models\ProductTransaction;
use App\Broadcasting\TaqnyatChannel;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Notifications\Traits\DefaultMessageTrait;

class ActivityPaymentSuccessNotification extends Notification implements ShouldQueue
{
    use Queueable, DefaultMessageTrait;

    /**
     * @var string
     */
    public static $defaultMessage = (
        'شكراً لتسجيل اهتمامك في ({product_name})'
        . "\r\n" .
        '{url}'
    );

    /**
     * @var array
     */
    public static $parameters = [
        '{product_name}' => 'اسم البرنامج',
        '{url}' => 'رابط الفاتورة',
    ];

    /**
     * Create a new notification instance.
     *
     * @param string $otp
     */
    public function __construct(protected ProductTransaction $productTransaction)
    {
        $this->queue = 'default';
    }

    /**
     * Get the sender_id.
     *
     * @return string
     */
    public function TaqnyatSender()
    {
        return app(\App\Settings\ServicesSettings::class)->taqnyatSender;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return [TaqnyatChannel::class];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'content' => self::getDefaultMessage(),
            'data' => [
                'product_name' => $this->productTransaction->product->title,
                'url' => $this->productTransaction->sms_url,
            ],
        ];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return string
     */
    public function toTaqnyat($notifiable)
    {
        return str_replace([
            '{product_name}',
            '{url}',
        ], [
            $this->productTransaction->product->title,
            $this->productTransaction->sms_url,
        ], self::getDefaultMessage());
    }
}
