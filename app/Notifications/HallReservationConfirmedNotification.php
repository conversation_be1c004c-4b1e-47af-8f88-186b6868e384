<?php

namespace App\Notifications;

use App\Models\HelpForm;
use Illuminate\Bus\Queueable;
use App\Models\HallReservation;
use App\Broadcasting\TaqnyatChannel;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Notifications\Traits\DefaultMessageTrait;
use Illuminate\Notifications\Messages\MailMessage;

class HallReservationConfirmedNotification extends Notification implements ShouldQueue
{
    use Queueable, DefaultMessageTrait;

    /**
     * @var string
     */
    public static $defaultMessage = (
    'تم تأكيد حجزكم لقاعة الصندوق في يوم {date_name} الموافق {date}'
    );

    /**
     * @var array
     */
    public static $parameters = [
        '{user_name}' => 'اسم المستخدم',
        '{date}' => 'التاريخ',
        '{date_name}' => 'اليوم',
    ];

    private HelpForm $helpForm;

    /**
     * Create a new notification instance.
     *
     * @param HallReservation $hallReservation
     */
    public function __construct(private readonly HallReservation $hallReservation)
    {
        $this->queue = 'default';
    }

    /**
     * Get the sender_id.
     *
     * @return string
     */
    public function TaqnyatSender()
    {
        return app(\App\Settings\ServicesSettings::class)->taqnyatSender;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return [TaqnyatChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)->line(nl2br($this->getMessage($notifiable)));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'content' => $this->getMessage($notifiable),
        ];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return string
     */
    public function toTaqnyat($notifiable)
    {
        return $this->getMessage($notifiable);
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return string
     */
    public function getMessage($notifiable)
    {
        return str_replace([
            '{user_name}',
            '{date}',
            '{date_name}',
        ], [
            $notifiable->getName(1),
            $this->hallReservation->start_at->locale('ar_SA')->translatedFormat('M j, Y'),
            $this->hallReservation->start_at->locale('ar_SA')->dayName,
        ], self::getDefaultMessage());
    }
}

