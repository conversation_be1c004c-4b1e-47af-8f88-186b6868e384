<?php

namespace App\Notifications;

use App\Services\GiftTransaction;
use App\Services\GiftNotificationService;
use App\Broadcasting\TaqnyatChannel;
use App\Broadcasting\WahaChannel;
use App\Notifications\Traits\DefaultMessageTrait;
use App\Settings\GeneralSettings;
use App\Settings\AppSettings;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GiftConfirmationNotification extends Notification implements ShouldQueue
{
    use Queueable, DefaultMessageTrait;

    /**
     * Default SMS/WhatsApp message template
     */
    public static $defaultMessage = (
        '{greeting} {giver_name}' . "\r\n" .
        'تم بنجاح إهداء عضوية ({package_name}) إلى {recipient_name}' . "\r\n" .
        'قيمة الهدية: {amount}' . "\r\n" .
        'شكراً لكرمك وسخائك'
    );

    /**
     * Available parameters for message customization
     */
    public static $parameters = [
        '{greeting}' => 'التحية (ابن العم / ابنة العم)',
        '{giver_name}' => 'اسم المهدي',
        '{recipient_name}' => 'اسم المستلم',
        '{package_name}' => 'اسم الباقة',
        '{amount}' => 'قيمة الهدية',
        '{transaction_uuid}' => 'رقم المعاملة',
    ];

    private GiftTransaction $giftTransaction;
    private array $giftDetails;

    /**
     * Create a new notification instance.
     */
    public function __construct(GiftTransaction $giftTransaction)
    {
        $this->giftTransaction = $giftTransaction;
        $this->giftDetails = app(GiftNotificationService::class)->getGiftDetails($giftTransaction);
        $this->queue = 'default';
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return app(GiftNotificationService::class)->getNotificationChannels($notifiable);
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $giftDetails = $this->giftDetails;
        $familyName = app(GeneralSettings::class)->familyNameAr;

        return (new MailMessage)
            ->subject("✅ تأكيد إهداء العضوية إلى {$giftDetails['recipient_name']}")
            ->greeting("السلام عليكم {$giftDetails['giver_name']}")
            ->line("تم بنجاح إهداء عضوية **{$giftDetails['package_name']}** إلى {$giftDetails['recipient_name']}")
            ->line("تفاصيل الهدية:")
            ->line("• المستفيد: {$giftDetails['recipient_name']}")
            ->line("• نوع العضوية: {$giftDetails['package_name']}")
            ->line("• قيمة الهدية: {$giftDetails['amount']} ريال")
            ->line("• رقم المعاملة: {$giftDetails['transaction_uuid']}")
            ->when(!empty($giftDetails['gift_message']), function ($mail) use ($giftDetails) {
                return $mail->line("• رسالتك: {$giftDetails['gift_message']}");
            })
            ->line("تم إرسال إشعار للمستفيد بالهدية وسيتمكن من الاستفادة من العضوية فوراً.")
            ->line("شكراً لكرمك وسخائك في دعم أعضاء {$familyName}")
            ->salutation("مع أطيب التحيات،\r\n{$familyName}");
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toTaqnyat($notifiable): string
    {
        return $this->formatMessage($notifiable);
    }

    /**
     * Get the WhatsApp representation of the notification.
     */
    public function toWaha($notifiable): array
    {
        return [
            'type' => 'text',
            'text' => $this->formatMessage($notifiable),
        ];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable): array
    {
        return [
            'type' => 'gift_confirmation',
            'title' => "✅ تم إهداء العضوية إلى {$this->giftDetails['recipient_name']}",
            'message' => "تم بنجاح إهداء عضوية {$this->giftDetails['package_name']}",
            'data' => [
                'transaction_uuid' => $this->giftDetails['transaction_uuid'],
                'recipient_name' => $this->giftDetails['recipient_name'],
                'package_name' => $this->giftDetails['package_name'],
                'amount' => $this->giftDetails['amount'],
                'gift_message' => $this->giftDetails['gift_message'],
                'gift_type' => $this->giftDetails['gift_type'],
            ],
            'action_url' => app(AppSettings::class)->appUrl . '/transactions',
        ];
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return $this->toDatabase($notifiable);
    }

    /**
     * Format message with user-specific data
     */
    private function formatMessage($notifiable): string
    {
        $greeting = $this->getGreeting($notifiable);
        $giftDetails = $this->giftDetails;

        return str_replace([
            '{greeting}',
            '{giver_name}',
            '{recipient_name}',
            '{package_name}',
            '{amount}',
            '{transaction_uuid}',
        ], [
            $greeting,
            $giftDetails['giver_name'],
            $giftDetails['recipient_name'],
            $giftDetails['package_name'],
            number_format($giftDetails['amount']) . ' ريال',
            $giftDetails['transaction_uuid'],
        ], self::getDefaultMessage());
    }

    /**
     * Get appropriate greeting based on user gender and age
     */
    private function getGreeting($notifiable): string
    {
        if ($notifiable->gender === \App\Enums\Gender::Female) {
            return 'ابنة العم';
        } else {
            // Check age for male users
            if (!empty($notifiable->dob) && $notifiable->dob->diffInYears() >= 55) {
                return 'العم';
            }
            return 'ابن العم';
        }
    }

    /**
     * Get sender for SMS notifications
     */
    public function TaqnyatSender(): string
    {
        return app(\App\Settings\ServicesSettings::class)->taqnyatSender;
    }

    /**
     * Get WhatsApp channel
     */
    public function wahaChannel()
    {
        return app(AppSettings::class)->wahaSender;
    }
}
