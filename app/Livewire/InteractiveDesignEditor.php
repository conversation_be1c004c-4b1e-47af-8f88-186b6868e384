<?php

namespace App\Livewire;

use Illuminate\Foundation\Exceptions\Renderer\Exception;
use Livewire\Component;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class InteractiveDesignEditor extends Component
{

    // Component state
    public $fields = [];
    public $imageUrl = '';
    public $canvasConfig = [];
    public $selectedFieldId = null;
    public $syncInProgress = false;
    
    // Avatar settings
    public $avatarSettings = [
        'enabled' => false,
        'x' => 0,
        'y' => 0,
        'width' => 100,
        'height' => 100,
        'shape' => 'circle',
        'borderColor' => '#000000',
        'borderWidth' => 2
    ];

    // Model configuration
    public $modelType = 'card';
    public $modelId;
    public $modelClass;
    public $textFieldsRelation = 'text_fields';

    // Form state for field editing
    public $editingField = null;

    // Parent component reference
    public $parentStatePath;

    // Events
    protected $listeners = [
        'fieldsSaved' => 'refreshFields',
        'imageUploaded' => 'handleImageUpload',
        'refreshEditor' => 'refreshFromDatabase',
        'image-updated' => 'updateImage'
    ];

    // Store fabric JSON - we'll manage serialization carefully
    public $fabricJson = null; // Changed to public so it can be accessed
    
    public function mount($fields = [], $imageUrl = '', $canvasConfig = [],
                         $modelId = null, $modelClass = null, $modelType = 'card',
                         $textFieldsRelation = 'text_fields', $parentStatePath = null)
    {
        $this->fields = $this->formatFields($fields);
        $this->modelId = $modelId;
        $this->modelClass = $modelClass;
        $this->modelType = $modelType;
        $this->textFieldsRelation = $textFieldsRelation;
        $this->parentStatePath = $parentStatePath;

        // Load from database if model exists
        if ($this->modelId && $this->modelClass) {
            $this->refreshFromDatabase();
            
            // Load the image and canvas dimensions from the model
            try {
                $model = $this->modelClass::find($this->modelId);
                if ($model && $model->exists) {
                    // Load canvas dimensions from the card model
                    if ($this->modelType === 'card') {
                        $this->canvasConfig = array_merge([
                            'width' => 800,
                            'height' => 600,
                        ], $canvasConfig, [
                            'width' => $model->canvas_width ?? 800,
                            'height' => $model->canvas_height ?? 1200,
                        ]);
                    } else {
                        // For non-card models, use default dimensions
                        $this->canvasConfig = array_merge([
                            'width' => 800,
                            'height' => 600,
                        ], $canvasConfig);
                    }
                    
                    // Load image
                    if (!empty($model->image_path)) {
                        // For multi-tenant applications, use tenant_asset if available
                        if (function_exists('tenant') && tenant()) {
                            $this->imageUrl = tenant_asset($model->image_path);
                        } else {
                            // Check if image_path already contains 'storage/'
                            if (str_starts_with($model->image_path, 'storage/')) {
                                $this->imageUrl = asset($model->image_path);
                            } else {
                                $this->imageUrl = asset('storage/' . $model->image_path);
                            }
                        }
                    }
                    
                    // Load avatar settings if model is a card
                    if ($this->modelType === 'card') {
                        $this->avatarSettings = [
                            'enabled' => $model->avatar_enabled ?? false,
                            'x' => $model->avatar_x ?? 0,
                            'y' => $model->avatar_y ?? 0,
                            'width' => $model->avatar_width ?? 100,
                            'height' => $model->avatar_height ?? 100,
                            'shape' => 'circle', // Default shape
                            'borderColor' => '#000000',
                            'borderWidth' => 2
                        ];
                        
                    }
                }
            } catch (\Exception $e) {
                logger()->error('Failed to load model data: ' . $e->getMessage());
                // Fall back to default canvas config if loading fails
                $this->canvasConfig = array_merge([
                    'width' => 800,
                    'height' => 600,
                    'gridSize' => 20,
                    'showGrid' => false,
                    'snapToGrid' => false
                ], $canvasConfig);
            }
        } else {
            // No model, use passed canvas config or defaults
            $this->canvasConfig = array_merge([
                'width' => 800,
                'height' => 600,
            ], $canvasConfig);
            
            if ($imageUrl) {
                $this->imageUrl = $imageUrl;
            }
        }
    }

    public function render()
    {
        return view('livewire.interactive-design-editor', [
            'fabricJson' => $this->fabricJson
        ]);
    }

    /**
     * Get the text fields table name based on model type
     */
    protected function getTextFieldsTable(): string
    {
        return match($this->modelType) {
            'card' => 'card_text_fields',
            'certificate' => 'certificate_text_fields',
            'invitation' => 'invitation_text_fields',
            default => $this->modelType . '_text_fields'
        };
    }

    /**
     * Get the parent table name based on model type
     */
    protected function getParentTable(): string
    {
        return match($this->modelType) {
            'card' => 'cards',
            'certificate' => 'certificates',
            'invitation' => 'invitations',
            default => $this->modelType . 's'
        };
    }

    /**
     * Get the foreign key column name
     */
    protected function getForeignKeyColumn(): string
    {
        return match($this->modelType) {
            'card' => 'card_id',
            'certificate' => 'certificate_id',
            'invitation' => 'invitation_id',
            default => $this->modelType . '_id'
        };
    }

    /**
     * Refresh fields from database - now loads from fabric_json
     */
    public function refreshFromDatabase()
    {
        if (!$this->modelId || !$this->modelClass) {
            return;
        }

        try {
            $model = $this->modelClass::find($this->modelId);
            if ($model && $model->exists) {
                // Load from fabric_json if available
                if ($model->fabric_json && isset($model->fabric_json['objects'])) {
                    $this->fabricJson = $model->fabric_json; // Store it
                    $this->fields = $this->extractFieldsFromFabricJson($model->fabric_json);
                    
                    // Also dispatch the fabric JSON for the editor to load directly
                    $this->dispatch('load-fabric-json', $model->fabric_json);
                } else {
                    // Fallback to text_fields relationship for backward compatibility
                    $textFields = $model->{$this->textFieldsRelation};
                    $this->fields = $this->formatFields($textFields->map(function($field) {
                        return [
                            'id' => $field->id,
                            'variable_name' => $field->variable_name,
                            'label' => $field->label,
                            'placeholder' => $field->placeholder,
                            'text_x' => $field->text_x,
                            'text_y' => $field->text_y,
                            'text_font' => $field->text_font,
                            'text_size' => $field->text_size,
                            'text_color' => $field->text_color,
                            'text_align' => $field->text_align,
                            'text_width' => $field->text_width,
                            'text_bold' => $field->text_bold,
                            'line_height' => (float)($field->line_height ?: 1.16),
                            'order' => $field->order,
                        ];
                    })->toArray());
                }

                $this->dispatch('fields-reloaded', $this->fields);
            }
        } catch (\Exception $e) {
            // Handle error gracefully
            logger()->error('Failed to refresh fields from database: ' . $e->getMessage());
        }
    }
    
    /**
     * Extract fields from fabric JSON
     */
    protected function extractFieldsFromFabricJson($fabricJson)
    {
        $fields = [];
        $order = 0;
        
        if (!isset($fabricJson['objects'])) {
            return $fields;
        }
        
        foreach ($fabricJson['objects'] as $object) {
            if ($object['type'] === 'textbox' && isset($object['fieldData'])) {
                $fieldData = $object['fieldData'];
                
                // Use fabric object positions, converting from display to design coordinates
                $editorConfig = $fabricJson['editorConfig'] ?? null;
                $scaleFactor = 1;
                
                if ($editorConfig) {
                    $scaleFactor = $editorConfig['designWidth'] / $editorConfig['displayWidth'];
                }
                
                $fields[] = [
                    'id' => $fieldData['id'] ?? $object['id'] ?? Str::uuid()->toString(),
                    'variable_name' => $fieldData['variable_name'] ?? '',
                    'label' => $fieldData['label'] ?? '',
                    'placeholder' => $fieldData['placeholder'] ?? $object['text'] ?? '',
                    'text_x' => isset($object['left']) ? round($object['left'] * $scaleFactor) : ($fieldData['text_x'] ?? 0),
                    'text_y' => isset($object['top']) ? round($object['top'] * $scaleFactor) : ($fieldData['text_y'] ?? 0),
                    'text_font' => $object['fontFamily'] ?? $fieldData['text_font'] ?? 'Arial',
                    'text_size' => isset($object['fontSize']) ? round($object['fontSize'] * $scaleFactor) : ($fieldData['text_size'] ?? 16),
                    'text_color' => $object['fill'] ?? $fieldData['text_color'] ?? '#000000',
                    'text_align' => $object['textAlign'] ?? $fieldData['text_align'] ?? 'left',
                    'text_width' => isset($object['width']) ? round($object['width'] * $scaleFactor) : ($fieldData['text_width'] ?? 200),
                    'text_bold' => ($object['fontWeight'] ?? 'normal') === 'bold' || ($fieldData['text_bold'] ?? false),
                    'line_height' => (float)($fieldData['line_height'] ?? 1.16),
                    'order' => $fieldData['order'] ?? $order++,
                ];
            }
        }
        
        return $fields;
    }

    /**
     * Format fields for display
     */
    protected function formatFields($fields)
    {
        return collect($fields)->map(function ($field) {
            return [
                'id' => $field['id'] ?? Str::uuid()->toString(),
                'variable_name' => $field['variable_name'] ?? '',
                'label' => $field['label'] ?? '',
                'placeholder' => $field['placeholder'] ?? '',
                'text_x' => (int)($field['text_x'] ?? 100),
                'text_y' => (int)($field['text_y'] ?? 100),
                'text_font' => $field['text_font'] ?? 'Arial',
                'text_size' => (int)($field['text_size'] ?? 16),
                'text_color' => $field['text_color'] ?? '#000000',
                'text_align' => $field['text_align'] ?? 'right',
                'text_bold' => (bool)($field['text_bold'] ?? false),
                'text_width' => (int)($field['text_width'] ?? 200),
                'line_height' => (float)($field['line_height'] ?? 1.16),
                'order' => (int)($field['order'] ?? 0),
            ];
        })->toArray();
    }

    /**
     * Update field position from canvas drag event
     */
    public function updateFieldPosition($fieldId, $x, $y)
    {
        $fieldIndex = collect($this->fields)->search(function ($field) use ($fieldId) {
            return $field['id'] == $fieldId;
        });

        if ($fieldIndex !== false) {
            $this->fields[$fieldIndex]['text_x'] = (int)$x;
            $this->fields[$fieldIndex]['text_y'] = (int)$y;

            // Don't auto-save - only save when form is submitted
            // $this->saveFieldToDatabase($this->fields[$fieldIndex]);

            // Emit event to notify parent component
            $this->dispatch('fieldPositionUpdated', $fieldId, $x, $y);
            $this->syncWithParent();
        }
    }

    /**
     * Update field properties
     */
    public function updateFieldProperties($fieldId, $properties)
    {
        $fieldIndex = collect($this->fields)->search(function ($field) use ($fieldId) {
            return $field['id'] == $fieldId;
        });

        if ($fieldIndex !== false) {
            foreach ($properties as $key => $value) {
                if (array_key_exists($key, $this->fields[$fieldIndex])) {
                    $this->fields[$fieldIndex][$key] = $value;
                }
            }

            // Don't auto-save - only save when form is submitted
            // $this->saveFieldToDatabase($this->fields[$fieldIndex]);

            // Update canvas display
            $this->dispatch('field-updated', $this->fields[$fieldIndex]);
            $this->syncWithParent();
        }
    }

    /**
     * Update a single field property
     */
    public function updateField($fieldId, $property, $value)
    {
        $fieldIndex = collect($this->fields)->search(function ($field) use ($fieldId) {
            return $field['id'] == $fieldId;
        });

        if ($fieldIndex !== false && array_key_exists($property, $this->fields[$fieldIndex])) {
            $this->fields[$fieldIndex][$property] = $value;
            $this->dispatch('field-updated', $this->fields[$fieldIndex]);
            $this->syncWithParent();
        }
    }

    /**
     * Add a new text field
     */
    public function addTextField($label, $placeholder = null)
    {
        $newField = [
            'id' => Str::uuid()->toString(), // Use UUID for temporary ID
            'variable_name' => 'field_' . time(),
            'label' => $label ?? 'حقل جديد',
            'placeholder' => $placeholder ?? $label ?? 'نص تجريبي',
            'text_x' => 100,
            'text_y' => 100 + (count($this->fields) * 30),
            'text_font' => 'Arial',
            'text_size' => 16,
            'text_color' => '#000000',
            'text_align' => 'right',
            'text_bold' => false,
            'text_width' => 200,
            'line_height' => 1.16,
            'order' => count($this->fields),
        ];

        // Don't auto-save - field will be saved when form is submitted
        $this->fields[] = $newField;

        // Notify canvas to add the field
        $this->dispatch('field-added', $newField);
        $this->syncWithParent();
    }

    /**
     * Remove a field
     */
    public function removeField($fieldId)
    {
        // Don't delete from database - just remove from local state
        // The deletion will happen when the form is submitted
        $this->fields = collect($this->fields)->reject(function ($field) use ($fieldId) {
            return $field['id'] == $fieldId;
        })->values()->toArray();

        // Notify canvas to remove the field
        $this->dispatch('field-removed', $fieldId);
        $this->syncWithParent();
    }

    /**
     * Select a field for editing
     */
    public function selectField($fieldId)
    {
        $this->selectedFieldId = $fieldId;
        $this->editingField = collect($this->fields)->firstWhere('id', $fieldId);
    }

    /**
     * Deselect field
     */
    public function deselectField()
    {
        $this->selectedFieldId = null;
        $this->editingField = null;
    }

    /**
     * Update single property of editing field
     */
    public function updateEditingField($property, $value)
    {
        if ($this->editingField && $this->selectedFieldId) {
            $this->editingField[$property] = $value;
            $this->updateFieldProperties($this->selectedFieldId, [$property => $value]);
        }
    }

    /**
     * Save field to database
     */
    protected function saveFieldToDatabase($fieldData)
    {
        if (!$this->modelId || !$this->modelClass) {
            return null;
        }

        try {
            $model = $this->modelClass::find($this->modelId);
            if (!$model || !$model->exists) {
                return null;
            }

            $attributes = [
                $this->getForeignKeyColumn() => $model->id,
                'label' => $fieldData['label'],
                'placeholder' => $fieldData['placeholder'],
                'text_x' => $fieldData['text_x'],
                'text_y' => $fieldData['text_y'],
                'text_font' => $fieldData['text_font'],
                'text_size' => $fieldData['text_size'],
                'text_color' => $fieldData['text_color'],
                'text_align' => $fieldData['text_align'],
                'text_width' => $fieldData['text_width'],
                'text_bold' => $fieldData['text_bold'],
                'line_height' => (float)$fieldData['line_height'],
                'order' => $fieldData['order'],
            ];

            if ($fieldData['id'] && is_numeric($fieldData['id'])) {
                // Update existing field
                DB::table($this->getTextFieldsTable())
                    ->where('id', $fieldData['id'])
                    ->update($attributes);

                return ['id' => $fieldData['id']];
            } else {
                // Create new field
                $id = DB::table($this->getTextFieldsTable())
                    ->insertGetId($attributes);

                return ['id' => $id];
            }

        } catch (\Exception $e) {
            logger()->error('Failed to save field to database: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Sync with parent Filament component
     */
    protected function syncWithParent()
    {
        // Skip parent sync for now to avoid DB errors
        // The data is already being saved to the database via the relationship
        // when saveFieldToDatabase is called
        return;
    }

    /**
     * Update image from Filament FileUpload
     */
    public function updateImage($url)
    {
        // If just a path is provided, convert to full URL
        if ($url && !filter_var($url, FILTER_VALIDATE_URL)) {
            if (function_exists('tenant') && tenant()) {
                $this->imageUrl = tenant_asset($url);
            } else {
                $this->imageUrl = asset('storage/' . $url);
            }
        } else {
            $this->imageUrl = $url;
        }
        
        // Notify canvas to update background
        $this->dispatch('image-changed', $this->imageUrl);
    }

    /**
     * Add field at specific position (called from JavaScript)
     */
    public function addFieldAtPosition($x, $y, $label = null)
    {
        $newField = [
            'id' => Str::uuid()->toString(), // Use UUID for temporary ID
            'variable_name' => 'field_' . time(),
            'label' => $label ?? 'حقل جديد',
            'placeholder' => $label ?? 'نص تجريبي',
            'text_x' => (int)$x,
            'text_y' => (int)$y,
            'text_font' => 'Arial',
            'text_size' => 16,
            'text_color' => '#000000',
            'text_align' => 'right',
            'text_bold' => false,
            'text_width' => 200,
            'line_height' => 1.16,
            'order' => count($this->fields),
        ];

        // Don't auto-save - field will be saved when form is submitted
        $this->fields[] = $newField;

        // Notify canvas to add the field
        $this->dispatch('field-added', $newField);
        $this->syncWithParent();
    }


    /**
     * Get all fields data for saving
     */
    public function getFieldsData()
    {
        return collect($this->fields)->map(function ($field) {
            return [
                'label' => $field['label'],
                'variable_name' => $field['variable_name'],
                'placeholder' => $field['placeholder'],
                'text_x' => $field['text_x'],
                'text_y' => $field['text_y'],
                'text_font' => $field['text_font'],
                'text_size' => $field['text_size'],
                'text_color' => $field['text_color'],
                'text_align' => $field['text_align'],
                'text_bold' => $field['text_bold'],
                'text_width' => $field['text_width'],
                'line_height' => $field['line_height'],
                'order' => $field['order'],
            ];
        })->toArray();
    }

    /**
     * Update component configuration
     */
    public function updateConfiguration($config)
    {
        if (isset($config['variables'])) {
            $this->variables = array_merge($this->variables, $config['variables']);
        }
        if (isset($config['canvasConfig'])) {
            $this->canvasConfig = array_merge($this->canvasConfig, $config['canvasConfig']);
        }
        if (isset($config['imageUrl'])) {
            $this->imageUrl = $config['imageUrl'];
            $this->dispatch('image-changed', $this->imageUrl);
        }

        // Reload fields in canvas
        $this->dispatch('fields-reloaded', $this->fields);
    }

    /**
     * Sync fields data to the parent repeater
     * NOTE: This is deprecated - we now use fabric_json as the single source of truth
     */
    public function syncFieldsToRepeater($fieldsData)
    {
        // This method is no longer needed since we're using fabric_json
        // Just log and return success
        logger()->info('syncFieldsToRepeater called but skipped - using fabric_json instead', [
            'fieldsCount' => count($fieldsData)
        ]);
        
        // Don't dispatch any events to avoid potential infinite loops
        // The fabric_json is already being saved via updateFabricJSON
        
        return;
        
        /*
        // Original code commented out - keeping for reference
        // Set sync flag to prevent rendering
        $this->syncInProgress = true;

        logger()->info('syncFieldsToRepeater called with data:', ['fieldsData' => $fieldsData]);

        if (!$this->modelId || !$this->modelClass) {
            logger()->warning('syncFieldsToRepeater: Missing modelId or modelClass', [
                'modelId' => $this->modelId,
                'modelClass' => $this->modelClass
            ]);
            $this->syncInProgress = false;
            return;
        }

        try {
            $model = $this->modelClass::find($this->modelId);
            if (!$model || !$model->exists) {
                logger()->warning('syncFieldsToRepeater: Model not found', ['modelId' => $this->modelId]);
                return;
            }

            logger()->info('syncFieldsToRepeater: Found model', ['modelId' => $model->id]);

            // Delete existing fields
            $deleted = DB::table($this->getTextFieldsTable())
                ->where($this->getForeignKeyColumn(), $model->id)
                ->delete();

            logger()->info('syncFieldsToRepeater: Deleted existing fields', ['count' => $deleted]);

            // Insert updated fields
            $insertedCount = 0;
            foreach ($fieldsData as $index => $field) {
                $attributes = [
                    $this->getForeignKeyColumn() => $model->id,
                    'label' => $field['label'] ?? '',
                    'placeholder' => $field['placeholder'] ?? '',
                    'text_x' => (int)($field['text_x'] ?? 0),
                    'text_y' => (int)($field['text_y'] ?? 0),
                    'text_font' => $field['text_font'] ?? 'Arial',
                    'text_size' => (int)($field['text_size'] ?? 16),
                    'text_color' => $field['text_color'] ?? '#000000',
                    'text_align' => $field['text_align'] ?? 'right',
                    'text_width' => (int)($field['text_width'] ?? 200),
                    'text_bold' => (bool)($field['text_bold'] ?? false),
                    'line_height' => (float)($field['line_height'] ?? 1.16),
                    'order' => $index,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                logger()->info('syncFieldsToRepeater: Inserting field', ['index' => $index, 'attributes' => $attributes]);

                DB::table($this->getTextFieldsTable())->insert($attributes);
                $insertedCount++;
            }

            logger()->info('syncFieldsToRepeater: Inserted fields', ['count' => $insertedCount]);

            // DON'T update local fields to prevent re-render
            // $this->fields = $fieldsData;

            // Dispatch sync completion event
            $this->dispatch('fields-sync-completed', [
                'success' => true,
                'count' => $insertedCount
            ]);

            // Show success message
            $this->dispatch('notify', [
                'title' => 'تم الحفظ',
                'message' => "تم حفظ {$insertedCount} حقل بنجاح",
                'type' => 'success'
            ]);

        } catch (Exception $e) {
            logger()->error('Failed to sync fields to repeater: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            // Dispatch sync completion event
            $this->dispatch('fields-sync-completed', [
                'success' => false,
                'error' => $e->getMessage()
            ]);

            $this->dispatch('notify', [
                'title' => 'خطأ',
                'message' => 'فشل في حفظ التغييرات: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        } finally {
            // Always reset sync flag
            $this->syncInProgress = false;
        }
        */
    }

    /**
     * Update avatar settings from canvas
     */
    public function updateAvatarSettings($settings)
    {
        $this->avatarSettings = array_merge($this->avatarSettings, $settings);
        
        // Save to database if model exists
        if ($this->modelId && $this->modelClass && $this->modelType === 'card') {
            try {
                $model = $this->modelClass::find($this->modelId);
                if ($model && $model->exists) {
                    $model->update([
                        'avatar_x' => $this->avatarSettings['x'],
                        'avatar_y' => $this->avatarSettings['y'],
                        'avatar_width' => $this->avatarSettings['width'],
                        'avatar_height' => $this->avatarSettings['height']
                    ]);
                }
            } catch (\Exception $e) {
                logger()->error('Failed to update avatar settings: ' . $e->getMessage());
            }
        }
    }

    /**
     * Save the Fabric.js canvas JSON data
     * This method is now deprecated - we let Filament handle the save
     */
    public function saveFabricDesign($fabricJSON)
    {
        // Just update the fabric JSON in memory
        $this->fabricJson = $fabricJSON;
        
        // Dispatch event to parent Filament component
        $this->dispatch('fabricJsonUpdated', ['fabricJson' => $fabricJSON]);
        
        logger()->info('Fabric design ready for save', [
            'modelType' => $this->modelType,
            'modelId' => $this->modelId,
            'objectCount' => isset($fabricJSON['objects']) ? count($fabricJSON['objects']) : 0
        ]);
        
        $this->dispatch('notify', [
            'title' => __('Success'),
            'message' => __('Design updated successfully'),
            'type' => 'success'
        ]);
    }
    
    /**
     * Update the Fabric.js canvas JSON data without saving to database
     * This is called when the canvas is updated in the editor
     */
    public function updateFabricJSON($fabricJSON)
    {
        // Sanitize fabric JSON to remove admin avatar sources before storing
        $sanitizedFabricJSON = $this->sanitizeFabricJSON($fabricJSON);
        
        // Store the sanitized fabric JSON in memory (protected property)
        $this->fabricJson = $sanitizedFabricJSON;
        
        // Store sanitized version in session for access during save
        session(['fabric_json_' . $this->modelType . '_' . $this->modelId => $sanitizedFabricJSON]);
        
        // Dispatch event to parent Filament component
        // The parent component will handle saving to database
        $this->dispatch('fabricJsonUpdated', ['fabricJson' => $sanitizedFabricJSON]);
        
        logger()->info('Fabric JSON updated and sanitized', [
            'modelType' => $this->modelType,
            'modelId' => $this->modelId,
            'objectCount' => isset($sanitizedFabricJSON['objects']) ? count($sanitizedFabricJSON['objects']) : 0
        ]);
    }
    
    /**
     * Sanitize fabric JSON to remove admin avatar image sources
     * This ensures clean templates for end users
     */
    private function sanitizeFabricJSON($fabricJSON)
    {
        if (!is_array($fabricJSON) || !isset($fabricJSON['objects'])) {
            return $fabricJSON;
        }
        
        $sanitized = $fabricJSON;
        
        // Remove admin avatar sources from all avatar objects
        foreach ($sanitized['objects'] as &$object) {
            if (isset($object['isAvatar']) && $object['isAvatar'] === true) {
                // Remove the src to ensure clean template
                unset($object['src']);
            }
        }
        
        return $sanitized;
    }
}
