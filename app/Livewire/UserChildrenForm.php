<?php

namespace App\Livewire;

use App\Models\NonFamilyUser;
use App\Models\User;
use Livewire\Component;

class UserChildrenForm extends Component
{
    public $fatherId;
    public $motherId;
    public bool $spouseSelected = false;
    private NonFamilyUser|null|User $father = null;
    private NonFamilyUser|null|User $mother = null;

    public function mount($father = null, $mother = null)
    {
        $this->fatherId = $father;
        $this->motherId = $mother;
        $this->load_father();
        $this->load_mother();
        if (is_null($this->mother) && $this->father instanceof User && $this->father->wives->count() === 0)
            $this->spouseSelected = true;
        if (is_null($this->father) && $this->mother instanceof User && $this->mother->husbands->count() === 0)
            $this->spouseSelected = true;
    }

    public function load_father()
    {
        if (is_null($this->father) && !is_null($this->fatherId))
            $this->father = User::withoutGlobalScopes(['family_users'])->findOrFail($this->fatherId);
        return $this->father;
    }

    public function load_mother()
    {
        if (is_null($this->mother) && !is_null($this->motherId))
            $this->mother = User::withoutGlobalScopes(['family_users'])->findOrFail($this->motherId);
        return $this->mother;
    }

    public function render()
    {
        return view('livewire.user-children-form', ['father' => $this->father, 'mother' => $this->mother]);
    }

    public function selectFather($fatherId)
    {
        $this->load_mother();
        $this->fatherId = $fatherId;
        $this->load_father();
        $this->spouseSelected = !is_null($this->father);
    }

    public function selectMother($motherId)
    {
        $this->load_father();
        $this->motherId = $motherId;
        $this->load_mother();
        $this->spouseSelected = !is_null($this->mother);
    }
}
