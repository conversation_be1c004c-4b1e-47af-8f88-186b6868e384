<?php

namespace App\Livewire;

use App\Enums\CaseStatus;
use App\Models\HelpForm;
use App\Models\HelpFormComment;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class HelpFormCommentsComponent extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public HelpForm $record;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        if (!auth()->user()->can('comment', $this->record)) {
            return $form;
        }

        if (config('filament-comments.editor') === 'markdown') {
            $editor = Forms\Components\MarkdownEditor::make('comment')
                ->hiddenLabel()
                ->required()
                ->placeholder(__('filament-comments::filament-comments.comments.placeholder'))
                ->toolbarButtons(config('filament-comments.toolbar_buttons'));
        } else {
            $editor = Forms\Components\RichEditor::make('comment')
                ->hiddenLabel()
                ->required()
                ->placeholder(__('filament-comments::filament-comments.comments.placeholder'))
                ->extraInputAttributes(['style' => 'min-height: 6rem'])
                ->toolbarButtons(config('filament-comments.toolbar_buttons'));
        }

        return $form
            ->schema([
                $editor,
                Forms\Components\ToggleButtons::make('status')
                    ->label(__('helpFormColumns.status'))
                    ->options([
                        'PENDING' => 'تحت الدراسة',
                        'APPROVED' => 'مقبول',
                        'DECLINED' => 'مرفوض',
                    ])
                    ->colors([
                        'APPROVED' => 'success',
                        'DECLINED' => 'danger',
                        'PENDING' => 'info',
                    ])
                    ->icons([
                        'APPROVED' => 'heroicon-o-check-circle',
                        'DECLINED' => 'heroicon-o-x-circle',
                        'PENDING' => 'heroicon-o-clock',
                    ])
                    ->visible(fn(): bool => auth()->user()->can('status', $this->record))
                    ->inline()
                    ->reactive()
                    ->nullable()
                    ->in(['APPROVED', 'DECLINED', 'PENDING'])
                    ->default(fn() => is_null($this->record->status) ? 'PENDING' : null)
                    ->disableOptionWhen(function (string $value): bool {
                        return match ($value) {
                            'APPROVED' => $this->record->status === 'APPROVED',
                            'DECLINED' => $this->record->status === 'DECLINED',
                            'PENDING' => !is_null($this->record->status),
                            default => false,
                        };
                    }),
                Forms\Components\TextInput::make('helping_value')
                    ->label(__('helpFormColumns.helping_value'))
                    ->numeric()
                    ->minValue(1)
                    ->requiredIf('status', CaseStatus::Approved)
                    ->visible(fn($get): bool => $get('status') === 'APPROVED' && auth()->user()->can('status', $this->record)),
            ])
            ->statePath('data');
    }

    public function create(): void
    {
        if (!auth()->user()->can('comment', $this->record)) {
            return;
        }

        $this->form->validate();

        $data = $this->form->getState();
        $data['status'] = $data['status'] ?? 'PENDING';

        if ($data['status'] === 'PENDING')
            $data['status'] = null;

        $this->record->comments()->create([
            'content' => $data['comment'],
            'status' => $data['status'],
            'user_id' => auth()->id(),
        ]);

        if ($data['status'] === 'APPROVED') {
            $this->record->update([
                'status' => $data['status'],
                'helping_value' => $data['helping_value'],
                'closed_at' => now(),
            ]);
        } elseif ($data['status'] === 'DECLINED') {
            $this->record->update([
                'status' => $data['status'],
            ]);
        }
        $this->record->refresh();

        Notification::make()
            ->title(__('filament-comments::filament-comments.notifications.created'))
            ->success()
            ->send();

        $this->form->fill();
    }

    public function delete(int $id): void
    {
        $comment = HelpFormComment::find($id);

        if (!$comment) {
            return;
        }

        if (!auth()->user()->can('delete', $comment)) {
            return;
        }

        $comment->delete();

        Notification::make()
            ->title(__('filament-comments::filament-comments.notifications.deleted'))
            ->success()
            ->send();
    }

    public function render(): View
    {
        $comments = $this->record->comments()
            ->with(['user.father.father.father'])
            ->latest('created_at')
            ->get();

        return view('livewire.help-form-comments-component', ['comments' => $comments]);
    }
}
