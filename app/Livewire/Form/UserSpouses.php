<?php

namespace App\Livewire\Form;

use App\Enums\Gender;
use App\Enums\MarriageStatus;
use App\Livewire\Form\Spouses\FamilyUserSelect;
use App\Livewire\Form\Spouses\NonFamilyUserSelect;
use App\Models\HusbandWife;
use App\Models\NonFamilyUser;
use App\Models\User;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\Attributes\On;

class UserSpouses extends Component
{
    public User $user;
    public bool $showAddFamilySpouse = false, $showAddNonFamilySpouse = false;
    public string|null $errorMessage = null;
    public $user_id, $non_family_user_id;

    public function mount(User $user)
    {
        if ($user->gender === Gender::Male)
            $user->load(['wives' => ['wife']]);
        else
            $user->load(['husbands' => ['husband']]);
        $this->user = $user;
    }

    public function boot()
    {
        $this->errorMessage = null;
        $this->withValidatorCallback = function (\Illuminate\Validation\Validator $validator) {
            if ($this->showAddFamilySpouse)
                $this->dispatch('set-errors', $validator->errors()->toArray())->to(FamilyUserSelect::class);
            if ($this->showAddNonFamilySpouse)
                $this->dispatch('set-errors', $validator->errors()->toArray())->to(NonFamilyUserSelect::class);
        };
    }

    public function render()
    {
        return view('livewire.form.user-spouses');
    }

    public function rules(): array
    {
        if ($this->showAddFamilySpouse)
            return [
                'user_id' => [
                    'required',
                    //Rule::notIn($this->user->wives->filter(fn($i) => $i->wife_type === User::class)->pluck('wife_id')->values()),
                    Rule::when($this->user->gender === Gender::Male, [
                        Rule::exists(User::class, 'id')->where('gender', Gender::Female),
                        function ($attribute, $value, $fail) {
                            if ($this->user->wives()->where(['wife_id' => $value])->count() > 0)
                                $fail('الزوجة على ذمة نفس الزوج .');
                            elseif (HusbandWife::query()->where(['wife_id' => $value, 'status' => MarriageStatus::Active])->count() > 0)
                                $fail('الزوجة على ذمة زوج آخر .');
                        },
                    ]),
                    Rule::when($this->user->gender === Gender::Female, [
                        Rule::exists(User::class, 'id')->where('gender', Gender::Male),
                        function ($attribute, $value, $fail) {
                            if ($this->user->husbands()->where(['husband_id' => $value])->count() > 0)
                                $fail('الزوج على ذمة نفس الزوجة .');
                            elseif (HusbandWife::query()->where(['husband_id' => $value, 'status' => MarriageStatus::Active])->count() >= 4)
                                $fail('الزوج على ذمته 4 زوجات أخرىات .');
                        },
                    ]),
                ]
            ];
        if ($this->showAddNonFamilySpouse)
            return [
                'non_family_user_id' => [
                    'required',
                    //Rule::notIn($this->user->wives->filter(fn($i) => $i->wife_type === User::class)->pluck('wife_id')->values()),
                    Rule::when($this->user->gender === Gender::Male, [
                        Rule::exists(NonFamilyUser::class, 'id')->where('gender', Gender::Female),
                        function ($attribute, $value, $fail) {
                            if ($this->user->wives()->where(['wife_id' => $value])->count() > 0)
                                $fail('الزوجة على ذمة نفس الزوج .');
                            elseif (HusbandWife::query()->where(['wife_id' => $value, 'status' => MarriageStatus::Active])->count() > 0)
                                $fail('الزوجة على ذمة زوج آخر .');
                        },
                    ]),
                    Rule::when($this->user->gender === Gender::Female, [
                        Rule::exists(NonFamilyUser::class, 'id')->where('gender', Gender::Male),
                        function ($attribute, $value, $fail) {
                            if ($this->user->husbands()->where(['husband_id' => $value])->count() > 0)
                                $fail('الزوج على ذمة نفس الزوجة .');
                            elseif (HusbandWife::query()->where(['husband_id' => $value, 'status' => MarriageStatus::Active])->count() >= 4)
                                $fail('الزوج على ذمته 4 زوجات أخرىات .');
                        },
                    ]),
                ]
            ];
        return [];
    }

    public function AddFamilySpouse()
    {
        if ($this->user->gender === Gender::Male) {
            if ($this->user->wives()->where('status', MarriageStatus::Active)->count() >= 4)
                return $this->wives_limit_error();
        } else {
            if ($this->user->husbands()->where('status', MarriageStatus::Active)->count() >= 1)
                return $this->husbands_limit_error();
        }
        $this->showAddFamilySpouse = true;
        $this->showAddNonFamilySpouse = false;
    }

    public function AddNonFamilySpouse()
    {
        if ($this->user->gender === Gender::Male) {
            if ($this->user->wives()->where('status', MarriageStatus::Active)->count() >= 4)
                return $this->wives_limit_error();
        } else {
            if ($this->user->husbands()->where('status', MarriageStatus::Active)->count() >= 1)
                return $this->husbands_limit_error();
        }
        $this->showAddNonFamilySpouse = true;
        $this->showAddFamilySpouse = false;
    }

    private function wives_limit_error()
    {
        $this->showAddFamilySpouse = false;
        $this->showAddNonFamilySpouse = false;
        return $this->errorMessage = 'يوجد 4 زوجات على ذمة الزوج !';
    }

    private function husbands_limit_error()
    {
        $this->showAddFamilySpouse = false;
        $this->showAddNonFamilySpouse = false;
        return $this->errorMessage = 'يوجد زوج آخر على ذمة الزوجة!';
    }

    #[On('family-spouse-create')]
    public function AddFamilySpouseConfirm()
    {
        if (!$this->showAddFamilySpouse)
            return;
        $this->validate();
        if ($this->user->gender === Gender::Male) {
            if ($this->user->wives()->where('status', MarriageStatus::Active)->count() >= 4)
                return $this->wives_limit_error();
            $this->validate();
            $this->user->wives()->create([
                'wife_id' => $this->user_id,
                'status' => MarriageStatus::Active,
            ]);
        } else {
            if ($this->user->husbands()->where('status', MarriageStatus::Active)->count() >= 1)
                return $this->husbands_limit_error();
            $this->validate();
            $this->user->husbands()->create([
                'husband_id' => $this->user_id,
                'status' => MarriageStatus::Active,
            ]);
        }
        $this->user_id = null;
        $this->showAddFamilySpouse = false;
        $this->user->refresh();
    }

    #[On('non-family-spouse-create')]
    public function AddNonFamilySpouseConfirm()
    {
        if (!$this->showAddNonFamilySpouse)
            return;
        $this->validate();
        /*$this->dispatch('form-error', [
            'message' => trans('validation.required', ['attribute' => 'test']),
            'key'     => "non_family_user_id",
        ])->to(NonFamilyUserSelect::class);*/
        if ($this->user->gender === Gender::Male) {
            if ($this->user->wives()->where('status', MarriageStatus::Active)->count() >= 4)
                return $this->wives_limit_error();
            $this->validate();
            $this->user->wives()->create([
                'wife_id' => $this->non_family_user_id,
                'status' => MarriageStatus::Active,
            ]);
        } else {
            if ($this->user->husbands()->where('status', MarriageStatus::Active)->count() >= 1)
                return $this->husbands_limit_error();
            $this->validate();
            $this->user->husbands()->create([
                'husband_id' => $this->non_family_user_id,
                'status' => MarriageStatus::Active,
            ]);
        }
        $this->non_family_user_id = null;
        $this->showAddNonFamilySpouse = false;
        $this->user->refresh();
    }

    #[On('delete-spouse')]
    public function _deleteSpouse(HusbandWife $spouse)
    {
        $spouse->delete();
        //$this->user->wives = null;
        $this->user->unsetRelation('wives');
        $this->user->load(['wives']);
    }

    #[On('cancel-add-form')]
    public function CancelAddForm()
    {
        $this->showAddFamilySpouse = false;
        $this->showAddNonFamilySpouse = false;
    }

    public function hydrate()
    {
        $this->dispatch('test');
    }
}
