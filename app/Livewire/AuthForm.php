<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\UserOTP;
use App\Notifications\OTPMailNotification;
use App\Notifications\OTPNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider;
use Livewire\Component;

/**
 * @property ?User $user
 */
class AuthForm extends Component
{
    public $phone;
    public $otp;
    public $showMethods = false;
    public $isDemo = false;
    public $selectedMethod = null;
    public $otp_uuid = null;
    public $user_id = null;
    public bool $remember = false;
    public $allowedMethods = [
        'whatsapp' => true,
        'sms' => true,
        'email' => false,
        '2fa' => false,
    ];

    public function mount()
    {
        if (app()->environment(['local']))
            $this->phone = env('LOGIN_PHONE', '');
    }

    public function resetForm()
    {
        $this->phone = null;
        $this->init();
    }

    public function init()
    {
        $this->otp_uuid = $this->selectedMethod = $this->user_id = $this->otp = null;
        $this->showMethods = false;
        $this->allowedMethods['email'] = false;
        $this->allowedMethods['2fa'] = false;
    }

    public function returnToMethods()
    {
        $this->selectedMethod = null;
        $this->showMethods = true;
    }

    public function showAvailableMethods()
    {
        $user = null;
        $this->init();
        $this->validate([
            'phone' => [
                'required', Rule::phone()->country(['AUTO'])->mobile(),
                function ($attribute, $val, $fail) use (&$user) {
                    $phone = phone_format($val, ['SA', 'AUTO'], false);
                    $user = User::query()
                        ->whereHas('roles')
                        ->where(compact('phone'))->first();
                    if (is_null($user))
                        $fail('لا يوجد مستخدم مسجل بهذا الرقم !');
                },
            ],
        ]);
        if (!is_null($user)) {
            $this->user_id = encrypt($user->id);
            $this->allowedMethods['sms'] = !empty($this->user->phone) && is_sa_phone($this->user->phone);
            $this->allowedMethods['whatsapp'] = !empty($this->user->phone) && is_waha_working();
            $this->allowedMethods['email'] = !empty($this->user->email);
            $this->allowedMethods['2fa'] = $this->user->hasEnabledTwoFactorAuthentication();
            $methods = $this->onlyMethods();
            if (count($methods) === 1)
                $this->selectMethod(array_keys($methods)[0]);
            else if (\Arr::has($methods, '2fa'))
                $this->selectMethod('2fa');
            else
                $this->showMethods = true;
        }
    }

    public function onlyMethods()
    {
        return array_filter($this->allowedMethods, fn($val) => $val === true);
    }

    public function selectMethod($method)
    {
        if ($this->selectedMethod !== null)
            return;
        if (in_array($method, array_keys($this->onlyMethods()))) {
            if (in_array($method, ['email', 'sms', 'whatsapp'])) {
                $otp = rand(1009, 9999);
                if (!app()->environment(['production']) && $method === 'sms')
                    return;
                $userOTP = new UserOTP([
                    'method' => $method,
                    'code' => $otp,
                    'expired_at' => now()->addMinutes(config('otp.timeout') ?? 3),
                ]);
                $this->user->otp()->save($userOTP);
                $this->otp_uuid = $userOTP->uuid;
                switch ($method) {
                    case 'email':
                        try {
                            $this->user->notify(new OTPMailNotification($otp));
                        } catch (\Exception $exception) {
                            $this->validate(['phone' => [fn($attribute, $val, $fail) => $fail($exception->getMessage())]]);
                        }
                        break;
                    case 'sms':
                        try {
                            $this->user->notify(new OTPNotification($otp, domain: get_domain(request()->headers->get('referer'))));
                        } catch (\Exception $exception) {
                            if (Str::lower($exception->getMessage()) === Str::lower('Your balance is 0'))
                                $this->validate(['phone' => [fn($attribute, $val, $fail) => $fail('4203 راجع الإدارة .')]]);
                            $this->validate(['phone' => [fn($attribute, $val, $fail) => $fail('خطأ غير متوقع !')]]);
                        }
                        break;
                    case 'whatsapp':
                        try {
                            $this->user->notify(new OTPNotification($otp, domain: get_domain(request()->headers->get('referer')), via: 'waha'));
                        } catch (\Exception $exception) {
                            \Log::error($exception);
                            $this->validate(['phone' => [fn($attribute, $val, $fail) => $fail('خطأ غير متوقع !')]]);
                        }
                        break;
                }
            }
            $this->showMethods = false;
            $this->selectedMethod = $method;
        } else
            $this->init();
    }

    public function login()
    {
        $this->validate([
            'otp' => ['required'],
        ]);
        if (!is_null($this->user) && in_array($this->selectedMethod, array_keys($this->onlyMethods()))) {
            if (in_array($this->selectedMethod, ['email', 'sms', 'whatsapp'])) {
                if (!app()->environment(['production']) && in_array($this->selectedMethod, ['sms', 'whatsapp'])) {
                    // only for dev environment
                    if ($this->otp === '1436') {
                        Auth::login($this->user, $this->remember);
                        request()->session()->regenerate();
                        $this->redirect(route('admin.home', 'home'));
                    } else
                        $this->validate([
                            'phone' => [
                                function ($attribute, $val, $fail) {
                                    $fail('رمز الدخول منتهي !');
                                },
                            ],
                        ]);
                } else {
                    $userOTP = $this->user->otp()
                        ->where('expired_at', '>', now())
                        ->find($this->otp_uuid);
                    if (!is_null($userOTP))
                        if ($this->otp == $userOTP->code) {
                            Auth::login($this->user, $this->remember);
                            request()->session()->regenerate();
                            $userOTP->delete();
                            $this->redirect(route('admin.home', 'home'));
                        } else
                            $this->validate([
                                'otp' => [
                                    function ($attribute, $val, $fail) {
                                        $fail('رمز الدخول غير صحيح !');
                                    },
                                ],
                            ]);
                    else
                        $this->init() && $this->validate([
                            'phone' => [
                                function ($attribute, $val, $fail) {
                                    $fail('رمز الدخول منتهي !');
                                },
                            ],
                        ]);
                }
            } else if ($this->selectedMethod === '2fa') {
                if (app(TwoFactorAuthenticationProvider::class)->verify(decrypt($this->user->two_factor_secret), $this->otp)) {
                    Auth::login($this->user, $this->remember);
                    request()->session()->regenerate();
                    $this->redirect(route('admin.home', 'home'));
                } else
                    $this->validate([
                        'otp' => [
                            function ($attribute, $val, $fail) {
                                $fail('رمز الدخول غير صحيح !');
                            },
                        ],
                    ]);
            }
        } else
            $this->init();
    }

    public function getUserProperty(): ?User
    {
        return $this->user_id ? User::find(decrypt($this->user_id)) : null;
    }

    public function updating($key, $value)
    {
        if (!in_array($key, ['phone', 'otp'])) return;
    }

    public function render()
    {
        return view('livewire.auth-form');
    }
}
