<?php

namespace App\Observers;

use App\Enums\Gender;
use App\Models\HelpForm;
use App\Repositories\WebhookRepository;

class HelpFormObserver
{
    public function __construct(private readonly WebhookRepository $webhookRepository)
    {
    }

    /**
     * Handle the HelpForm "created" event.
     */
    public function created(HelpForm $helpForm): void
    {

        if (!$helpForm->template->family_details_required)
        {
            if ($helpForm->user->gender === Gender::Male)
                $helpForm->updateQuietly([
                    'spouses_count' => $helpForm->user->wives()->count(),
                    'children_count' => $helpForm->user->children()->count(),
                    'underage_children_count' => $helpForm->user->children()->whereDate('dob', '>', now()->subYears(18))->count(),
                    'adult_children_count' => $helpForm->user->children()->whereDate('dob', '<=', now()->subYears(18))->count(),
                ]);
            else
                $helpForm->updateQuietly([
                    'spouses_count' => $helpForm->user->husbands()->count(),
                    'children_count' => $helpForm->user->mother_children()->count(),
                    'underage_children_count' => $helpForm->user->mother_children()->whereDate('dob', '>', now()->subYears(18))->count(),
                    'adult_children_count' => $helpForm->user->mother_children()->whereDate('dob', '<=', now()->subYears(18))->count(),
                ]);
        }
    }

    /**
     * Handle the HelpForm "updating" event.
     */
    public function updating(HelpForm $helpForm): void
    {
        $original = $helpForm->getOriginal();
        if (empty($helpForm->completed_at) && !empty($original['completed_at']))
            $this->webhookRepository->created($helpForm);
        else
            $this->webhookRepository->updated($helpForm, $original);
    }

    /**
     * Handle the HelpForm "deleted" event.
     */
    public function deleted(HelpForm $helpForm): void
    {
        $this->webhookRepository->deleted($helpForm);
    }
}
