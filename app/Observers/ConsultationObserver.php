<?php

namespace App\Observers;

use App\Models\Consultation;
use App\Repositories\WebhookRepository;

class ConsultationObserver
{
    public function __construct(private readonly WebhookRepository $webhookRepository)
    {
    }

    /**
     * Handle the HallReservation "created" event.
     */
    public function created(Consultation $consultation): void
    {
        $this->webhookRepository->created($consultation);
    }

    /**
     * Handle the HallReservation "updating" event.
     */
    public function updating(Consultation $consultation): void
    {
        $this->webhookRepository->updated($consultation, $consultation->getOriginal());
    }

    /**
     * Handle the HallReservation "deleted" event.
     */
    public function deleted(Consultation $consultation): void
    {
        $this->webhookRepository->deleted($consultation);
    }
}
