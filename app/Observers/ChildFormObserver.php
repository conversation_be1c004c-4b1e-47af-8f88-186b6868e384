<?php

namespace App\Observers;

use App\Models\ChildForm;
use App\Repositories\WebhookRepository;

class ChildFormObserver
{
    public function __construct(private readonly WebhookRepository $webhookRepository)
    {
    }

    /**
     * Handle the HallReservation "created" event.
     */
    public function created(ChildForm $childForm): void
    {
        $this->webhookRepository->created($childForm);
    }

    /**
     * Handle the HallReservation "updating" event.
     */
    public function updating(ChildForm $childForm): void
    {
        $this->webhookRepository->updated($childForm, $childForm->getOriginal());
    }

    /**
     * Handle the HallReservation "deleted" event.
     */
    public function deleted(ChildForm $childForm): void
    {
        $this->webhookRepository->deleted($childForm);
    }
}
