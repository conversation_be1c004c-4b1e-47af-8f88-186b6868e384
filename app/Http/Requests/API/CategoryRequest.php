<?php

namespace App\Http\Requests\API;

use App\Enums\DiscountType;
use Illuminate\Validation\Rule;
use App\Http\Requests\DefaultRequest;

class CategoryRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'title' => ['required', 'string', 'max:191'],
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [
            'type' => ['required', 'string', 'max:191'],
        ];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        return [];
    }
}
