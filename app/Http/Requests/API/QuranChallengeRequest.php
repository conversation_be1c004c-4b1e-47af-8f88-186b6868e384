<?php

namespace App\Http\Requests\API;

use App\Http\Requests\DefaultRequest;
use App\Models\ActivityMemberRole;
use Illuminate\Validation\Rule;

class QuranChallengeRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'title' => ['required', 'string', 'max:191'],
            'points' => ['nullable', 'integer', 'min:0'],
            'chapters' => ['required', 'integer', 'min:1', 'max:30'],
            'activity_member_role_id' => ['nullable', Rule::exists(ActivityMemberRole::class, 'id')],
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        return [];
    }
}
