<?php

namespace App\Http\Requests\API;

use App\Http\Requests\DefaultRequest;
use Illuminate\Validation\Rule;

class GroupDateRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'remind_txt' => ['nullable', 'required_with:remind_at', 'string', 'min:4', 'max:1000'],
            'remind_at' => ['nullable', 'required_with:remind_txt', 'date'],
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        $groupId = $this->route('group')?->id;
        return [
            'schedule_at' => [
                'required', 'date',
                Rule::unique('group_dates')->where('group_id', $groupId)->whereNull('deleted_at')
            ],
        ];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        $groupId = $this->route('group')?->id;
        $groupDateId = $this->route('group_date')?->id;
        return [
            'schedule_at' => [
                'required', 'date',
                Rule::unique('group_dates')
                    ->where('group_id', $groupId)
                    ->ignore($groupDateId)
                    ->whereNull('deleted_at')
            ],
        ];
    }
}
