<?php

namespace App\Http\Requests\API;

use App\Enums\Gender;
use App\Http\Requests\DefaultRequest;
use App\Models\FamilyTitle;
use Illuminate\Validation\Rule;

class NonFamilyUserRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'family_title_id' => [
                'required', Rule::exists(FamilyTitle::class, 'id')
            ],
            'name' => [
                'required', 'max:191', 'min:3',
                'regex:/^([\x{0621}-\x{063A}\x{0641}-\x{064A} ]+)$/u',
            ],
            'gender' => [
                'required', Rule::in(Gender::all()),
            ],
            'dob' => ['nullable', 'date'],
            'dod' => ['nullable', 'date'],
            'is_dead' => ['nullable', 'boolean'],
            'health_status' => ['nullable', Rule::in(all_health_status())],
            'educational_status' => ['nullable', Rule::in(all_educational_status())],
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        return [];
    }
}
