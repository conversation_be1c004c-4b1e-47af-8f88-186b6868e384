<?php

namespace App\Http\Requests\API;

use App\Http\Requests\DefaultRequest;
use App\Models\Activity;
use App\Models\ActivityMemberRole;
use App\Models\ActivityUser;
use App\Models\User;
use Illuminate\Validation\Rule;

class ActivityUserRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        /** @var $activity Activity */
        $activity = $this->route('activity');
        return [
            'user_id' => [
                'required', function ($attribute, $value, $fail) use ($activity) {
                    $user = User::query()->find($value);
                    if (is_null($user))
                        $fail(__('validation.exists', ['attribute' => 'المستخدم']));
                    else {
                        $role = ActivityMemberRole::find($this->input('activity_member_role_id'));
                        $age_from = $age_to = null;
                        extract($activity->only(['age_from', 'age_to']));
                        if (
                            !is_null($role) &&
                            !is_null($user->dob) &&
                            $role->check_dob === true &&
                            is_numeric($age_from) &&
                            is_numeric($age_to)
                        ) {
                            $user_age = $user->age_in_years;
                            $age_from = intval($age_from);
                            $age_to = intval($age_to);
                            if (($age_from > 0 && $user_age < $age_from) || $user_age > $age_to)
                                $fail('عمر المستخدم "' . $user_age . '" خارج نطاق العمر المسموح.');
                        }
                    }
                }
            ],
            'activity_member_role_id' => [
                'required', 'exists:activity_member_roles,id',
                Rule::unique('activity_users', 'activity_member_role_id')
                    ->where('activity_id', $activity->id)
                    ->where('user_id', $this->input('user_id'))
            ],
        ];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        /** @var $activityUser ActivityUser */
        $activityUser = $this->route('activity_user');
        return [
            'activity_member_role_id' => [
                'required', 'exists:activity_member_roles,id',
                Rule::unique('activity_users', 'activity_member_role_id')
                    ->where('activity_id', $activityUser->activity_id)
                    ->where('user_id', $this->input('user_id'))
                    ->ignoreModel($activityUser)
            ],
        ];
    }

    public function commonMessages(): array
    {
        return [
            'activity_member_role_id.unique' => 'المستخدم موجود بالفعل بنفس الدور.'
        ];
    }
}
