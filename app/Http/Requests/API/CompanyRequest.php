<?php

namespace App\Http\Requests\API;

use App\Models\Company;
use App\Models\Category;
use App\Enums\CategoryType;
use Illuminate\Validation\Rule;
use App\Http\Requests\DefaultRequest;

class CompanyRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'logo' => ['nullable', 'image'],
            'visible' => ['required', 'boolean'],
            'category_id' => ['required', Rule::exists(Category::class, 'id')->where('type', CategoryType::Company)],
            'description' => ['nullable', 'string', 'max:1000'],
            'website_url' => ['nullable', 'url', 'max:191'],
            'snapchat_url' => ['nullable', 'url', 'max:191'],
            'linkedin_url' => ['nullable', 'url', 'max:191'],
            'facebook_url' => ['nullable', 'url', 'max:191'],
            'instagram_url' => ['nullable', 'url', 'max:191'],
            'twitter_url' => ['nullable', 'url', 'max:191'],
            'google_maps_url' => ['nullable', 'url', 'max:191'],
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [
            'title' => ['required', 'string', 'max:191', Rule::unique(Company::class)],
        ];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        /** @var Company $company */
        $company = $this->route('company');
        return [
            'title' => [
                'required', 'string', 'max:191',
                Rule::unique(Company::class)->ignoreModel($company),
            ],
        ];
    }

    public function attributes()
    {
        return \Lang::get('companyColumns');
    }
}
