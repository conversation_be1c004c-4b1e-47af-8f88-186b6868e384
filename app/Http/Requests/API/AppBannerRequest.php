<?php

namespace App\Http\Requests\API;

use App\Models\Product;
use App\Models\Category;
use App\Enums\StoreBannerType;
use App\Http\Requests\DefaultRequest;
use Illuminate\Validation\Rule;

class AppBannerRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'title' => ['required', 'string', 'max:191'],
            'url' => ['required', 'string', 'max:1000'],
            'guest' => ['required', 'boolean'],
            'authed' => ['required', 'boolean'],
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [
            'image' => ['required', 'image', 'max:2048'],
        ];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        return [
            'image' => ['nullable', 'image', 'max:2048'],
        ];
    }
}
