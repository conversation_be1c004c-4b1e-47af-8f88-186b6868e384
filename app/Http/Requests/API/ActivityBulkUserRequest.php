<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ActivityBulkUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'family_user_ids' => ['required', 'array', 'min:1'],
            'family_user_ids.*' => [
                'required', Rule::exists('users', 'family_user_id')
            ],
            'activity_member_role_id' => ['required', 'exists:activity_member_roles,id'],
        ];
    }
}
