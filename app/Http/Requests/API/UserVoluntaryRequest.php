<?php

namespace App\Http\Requests\API;

use App\Models\User;
use App\Http\Requests\DefaultRequest;
use Illuminate\Validation\Rule;

class UserVoluntaryRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'caption' => ['required', 'string'],
            'total' => ['required', 'numeric', 'min:1'],
            'date' => ['required', 'date'],
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [
            'user_id' => ['required', Rule::exists(User::class, 'id')],
        ];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        return [

        ];
    }
}
