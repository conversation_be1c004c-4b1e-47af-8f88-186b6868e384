<?php

namespace App\Http\Requests\API;

use App\Http\Requests\DefaultRequest;

class GroupRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'name' => ['required', 'string', 'min:4', 'max:191'],
            'notes' => ['required', 'string', 'min:4', 'max:191'],
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        return [];
    }
}
