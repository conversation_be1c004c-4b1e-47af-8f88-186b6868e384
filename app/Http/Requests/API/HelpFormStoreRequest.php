<?php

namespace App\Http\Requests\API;

use App\Http\Requests\DefaultRequest;
use App\Models\HelpFormTemplate;
use App\Models\User;
use App\Permissions\HelpFormPermissions;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;

class HelpFormStoreRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Default rules for the request.
     *
     * @return array
     */
    public function commonRules(): array
    {
        return [
            'help_form_template_id' => ['required', Rule::exists(HelpFormTemplate::class, 'id')],
            'user_id' => [
                'bail', 'required', Rule::exists(User::class, 'id'),
                function ($attribute, $value, $fail) {
                    $user = User::find($value);
                    if (is_null($user->user_region_id)) {
                        $fail('لا يمكن إنشاء ملف مستفيد لمستخدم ليس له مكان إقامة !');
                    }
                }
            ],
            'responsible_user_id' => Rule::when(user()->hasPermissionTo(HelpFormPermissions::createForOther), [
                'nullable', Rule::exists(User::class, 'id'),
                //Rule::notIn($this->user()->id),
            ], ['nullable']),
        ];
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [];
    }

    /**
     * Rules for the "update" (PATCH|PUT) endpoint.
     *
     * @return array
     */
    public function updateRules(): array
    {
        return [];
    }

    public function attributes()
    {
        return Lang::get('helpFormColumns') ?? [];
    }

    public function messages()
    {
        return [
            'responsible_user_id.not_in' => 'لا يمكن تعيين نفس المستخدم كمسئول عن ملف المستفيد !'
        ];
    }
}
