<?php

namespace App\Http\Requests;

use App\Models\BankAccount;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreBankTransactionImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'bank_account_id' => ['required', Rule::exists(BankAccount::class, 'id')],
            'bank_file' => ['required', 'file', 'mimes:csv,xlsx,xls'],
        ];
    }

    public function attributes()
    {
        return [
            'bank_account_id' => 'حساب المؤسسة',
            'bank_file' => __('bankTransactionImportColumns.bank_file'),
        ];
    }
}
