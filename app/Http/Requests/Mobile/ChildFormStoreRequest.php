<?php

namespace App\Http\Requests\Mobile;

use App\Enums\Gender;
use App\Models\UserRegion;
use App\Enums\UserMaritalStatus;
use App\Http\Requests\DefaultRequest;
use App\Models\User;
use Illuminate\Validation\Rule;

class ChildFormStoreRequest extends DefaultRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules for the "store" (POST) endpoint.
     *
     * @return array
     */
    public function storeRules(): array
    {
        return [
            'name' => [
                'required',
                'words_eq:1',
                'regex:/^([\x{0621}-\x{063A}\x{0641}-\x{064A}]+)$/u',
                Rule::notIn(['بن', 'بنت']),
                'min:2', 'max:32',
                function ($attribute, $val, $fail) {
                    $user = $this->user();
                    if (in_array($val, $user->children()->pluck('name')->toArray()))
                        $fail('اسم الطفل موجود مسبقاً !');
                },
            ],
            'gender' => ['required', Rule::in(Gender::all())],
            'mother_id' => ['nullable', Rule::exists('users', 'id')],
            'national_id' => [
                'nullable', 'string', 'min:4', 'max:32',
                'unique:users,national_id' . (isset($user) ? ',' . $user->id . ',id' : '')
            ],
            'dob' => ['nullable', 'date'],
            'marital_status' => ['nullable', Rule::in(UserMaritalStatus::all())],
            'health_status' => ['nullable', Rule::in(all_health_status())],
            'educational_status' => ['nullable', Rule::in(all_educational_status())],
            'user_region_id' => ['nullable', Rule::exists(UserRegion::class, 'id')],
            'phone' => [
                'nullable', function ($attribute, $val, $fail) {
                    $phone = phone_format($val, ['SA', 'AUTO'], false);
                    if (!empty($phone) && User::query()->where($attribute, $phone)->exists())
                        $fail(trans('validation.unique', ['attribute' => trans("userColumns.$attribute")]));
                },
            ],
        ];
    }
}
