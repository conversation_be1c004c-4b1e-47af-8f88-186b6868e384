<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Settings\ServicesSettings;
use Symfony\Component\HttpFoundation\Response;

class CloudDiskMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = tenant();
        if ($tenant) {
            $serviceSetting = app(ServicesSettings::class);
            $disk = config('filesystems.disks.r2');
            config()->set('filesystems.disks.r2', array_merge($disk, [
                'key' => $serviceSetting->cloudAccessKeyId,
                'secret' => $serviceSetting->cloudSecretAccessKey,
                'bucket' => $serviceSetting->cloudBucket,
                'url' => $serviceSetting->cloudUrl,
                'endpoint' => $serviceSetting->cloudEndpoint,
            ]));
        }
        return $next($request);
    }
}
