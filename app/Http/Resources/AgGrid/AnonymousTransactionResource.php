<?php

namespace App\Http\Resources\AgGrid;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AnonymousTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'bank_account' => [
                'id' => $this->bank_account->id,
                'title' => $this->bank_account->title,
            ],
            'upgrade_notes' => $this->upgrade_notes,
            'amount' => $this->amount,
            'due_at' => $this->due_at,
            'is_refund' => $this->is_refund,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
