<?php

namespace App\Http\Resources\AgGrid;

use Illuminate\Http\Request;
use App\Http\Resources\API\UserFilterResource;
use Illuminate\Http\Resources\Json\JsonResource;

class LiteratureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'publish_date' => $this->publish_date,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
            'cover_image_url' => $this->cover_image_url,
            'category' => $this->whenLoaded('category', fn() =>  !is_null($this->category) ? $this->category->only(['id', 'title']) : null),
            'user' => $this->whenLoaded('user', fn() =>  !is_null($this->user) ? new UserFilterResource($this->user) : null),
        ];
    }
}
