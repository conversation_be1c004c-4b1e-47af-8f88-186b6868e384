<?php

namespace App\Http\Resources\API;

use App\Enums\Gender;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Container\ContainerExceptionInterface;
use Illuminate\Http\Resources\Json\JsonResource;

class EventStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     *
     * @return array<string, mixed>
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function toArray(Request $request): array
    {
        $tenantId = tenancy()->tenant?->id;
        $data = [];
        if (!empty($tenantId)) {
            $cachingKey = "tenant-{$tenantId}:event-statistics:e-{$this->id}";
            $cachingData = Redis::get($cachingKey);
            if (is_null($cachingData)) {
                $allowableMale = $this->guests()->where('gender', Gender::Male)->count();
                $allowableFemale = $this->guests()->where('gender', Gender::Female)->count();

                $allowableAgeNull = $this->guests()->whereNull('dob')->count();
                $allowableAgeAdult = $this->guests()->whereNotNull('dob')->whereDate('dob', '>', now()->subYears(20))->count();
                $allowableAgeChild = $this->guests()->whereNotNull('dob')->whereDate('dob', '<=', now()->subYears(12))->count();
                $allowableAgeYouth = $this->guests()
                    ->whereNotNull('dob')->whereDate('dob', '>', now()->subYears(12))
                    ->whereDate('dob', '<=', now()->subYears(20))->count();

                $allowableTotal = $allowableMale + $allowableFemale;
                $allowableBranches = $this->guests()
                    ->select(['users.id', 'users.father_id'])->get()
                    ->groupBy(fn($a) => $a->branch?->id)
                    ->mapWithKeys(
                        fn($v, $k) => [
                            ($v->first()->branch ? 'b_' . $v->first()->branch->id : 'na') => [
                                "title" => $v->first()->branch?->name ?: 'غير معروف',
                                "count" => $v->count(),
                            ],
                        ]
                    );
                $allowableCities = $this->guests()
                    ->select(['users.id', 'users.user_region_id'])->with(['user_region'])->get()
                    ->groupBy(fn($u) => $u->user_region_id)
                    ->mapWithKeys(
                        fn($v, $k) => [
                            ($v->first()->user_region ? 'r_' . $v->first()->user_region->id : 'na') => [
                                "title" => $v->first()->user_region?->title_ar ?: 'غير معروف',
                                "count" => $v->count(),
                            ],
                        ]
                    );

                $attendancesMale = $this->attendance_users()->where('gender', Gender::Male)->count();
                $attendancesFemale = $this->attendance_users()->where('gender', Gender::Female)->count();

                $attendancesAgeNull = $this->attendance_users()->whereNull('dob')->count();
                $attendancesAgeAdult = $this->attendance_users()->whereNotNull('dob')->whereDate('dob', '>', now()->subYears(20))->count();
                $attendancesAgeChild = $this->attendance_users()->whereNotNull('dob')->whereDate('dob', '<=', now()->subYears(12))->count();
                $attendancesAgeYouth = $this->attendance_users()
                    ->whereNotNull('dob')->whereDate('dob', '>', now()->subYears(12))
                    ->whereDate('dob', '<=', now()->subYears(20))->count();

                $attendancesTotal = $this->attendances()->count();
                $attendancesGuests = $this->attendances()->whereHas('event_invitation', fn($q) => $q->whereNotNull('host_user_id'))->count();
                $attendancesBranches = $this->attendance_users()
                    ->select(['users.id', 'users.father_id'])->get()
                    ->groupBy(fn($a) => $a->branch?->id)
                    ->mapWithKeys(
                        fn($v, $k) => [
                            ($v->first()->branch ? 'b_' . $v->first()->branch->id : 'na') => [
                                "title" => $v->first()->branch?->name ?: 'غير معروف',
                                "count" => $v->count(),
                            ],
                        ]
                    );
                $attendancesCities = $this->attendance_users()
                    ->with(['user_region'])
                    ->select(['users.id', 'users.user_region_id'])->get()
                    ->groupBy(fn($u) => $u->user_region_id)
                    ->mapWithKeys(
                        fn($v, $k) => [
                            ($v->first()->user_region ? 'r_' . $v->first()->user_region->id : 'na') => [
                                "title" => $v->first()->user_region?->title_ar ?: 'غير معروف',
                                "count" => $v->count(),
                            ],
                        ]
                    );

                $invitedMale = $this->invited_users()->where('gender', Gender::Male)->count();
                $invitedFemale = $this->invited_users()->where('gender', Gender::Female)->count();

                $invitedAgeNull = $this->invited_users()->whereNull('dob')->count();
                $invitedAgeAdult = $this->invited_users()->whereNotNull('dob')->whereDate('dob', '>', now()->subYears(20))->count();
                $invitedAgeChild = $this->invited_users()->whereNotNull('dob')->whereDate('dob', '<=', now()->subYears(12))->count();
                $invitedAgeYouth = $this->invited_users()
                    ->whereNotNull('dob')->whereDate('dob', '>', now()->subYears(12))
                    ->whereDate('dob', '<=', now()->subYears(20))->count();

                $invitedTotal = $this->invitations()->count();
                $invitedGuests = $this->invitations()->whereNotNull('host_user_id')->count();
                $invitedBranches = $this->invited_users()
                    ->select(['users.id', 'users.father_id'])->get()
                    ->groupBy(fn($a) => $a->branch?->id)
                    ->mapWithKeys(
                        fn($v, $k) => [
                            ($v->first()->branch ? 'b_' . $v->first()->branch->id : 'na') => [
                                "title" => $v->first()->branch?->name ?: 'غير معروف',
                                "count" => $v->count(),
                            ],
                        ]
                    );
                $invitedCities = $this->invited_users()
                    ->with(['user_region'])
                    ->select(['users.id', 'users.user_region_id'])->get()
                    ->groupBy(fn($u) => $u->user_region_id)
                    ->mapWithKeys(
                        fn($v, $k) => [
                            ($v->first()->user_region ? 'r_' . $v->first()->user_region->id : 'na') => [
                                "title" => $v->first()->user_region?->title_ar ?: 'غير معروف',
                                "count" => $v->count(),
                            ],
                        ]
                    );
                $data = [
                    'created_at' => now()->timestamp,
                    'allowable' => [
                        'gender' => [
                            'males' => $allowableMale,
                            'females' => $allowableFemale,
                        ],
                        'age' => [
                            'null' => $allowableAgeNull,
                            'child' => $allowableAgeChild,
                            'youth' => $allowableAgeYouth,
                            'adult' => $allowableAgeAdult,
                        ],
                        'branches' => $allowableBranches->values(),
                        'cities' => $allowableCities->values(),
                        'total' => $allowableTotal,
                    ],
                    'attendances' => [
                        'gender' => [
                            'males' => $attendancesMale,
                            'females' => $attendancesFemale,
                        ],
                        'age' => [
                            'null' => $attendancesAgeNull,
                            'child' => $attendancesAgeChild,
                            'youth' => $attendancesAgeYouth,
                            'adult' => $attendancesAgeAdult,
                        ],
                        'branches' => $attendancesBranches->values(),
                        'cities' => $attendancesCities->values(),
                        'guests' => $attendancesGuests,
                        'total' => $attendancesTotal,
                    ],
                    'invitations' => [
                        'gender' => [
                            'males' => $invitedMale,
                            'females' => $invitedFemale,
                        ],
                        'age' => [
                            'null' => $invitedAgeNull,
                            'child' => $invitedAgeChild,
                            'youth' => $invitedAgeYouth,
                            'adult' => $invitedAgeAdult,
                        ],
                        'branches' => $invitedBranches->values(),
                        'cities' => $invitedCities->values(),
                        'guests' => $invitedGuests,
                        'total' => $invitedTotal,
                    ],
                    'branches' => collect(
                        [
                            ...$allowableBranches->keys(),
                            ...$attendancesBranches->keys(),
                            ...$invitedBranches->keys(),
                        ]
                    )->unique()
                        ->sortBy(function ($k) {
                            return $k === 'na' ? 1 : 0;
                        })
                        ->map(fn($v) => [
                            'title' => optional(@$allowableBranches[$v])['title'] ?? optional(@$attendancesBranches[$v])['title'] ?? optional(@$invitedBranches[$v])['title'],
                            'allowable' => optional(@$allowableBranches[$v])['count'] ?? 0,
                            'attendances' => optional(@$attendancesBranches[$v])['count'] ?? 0,
                            'invitations' => optional(@$invitedBranches[$v])['count'] ?? 0,
                        ]),
                    'cities' => collect(
                        [
                            ...$allowableCities->keys(),
                            ...$attendancesCities->keys(),
                            ...$invitedCities->keys(),
                        ]
                    )->unique()
                        ->sortBy(function ($k) {
                            return $k === 'na' ? 1 : 0;
                        })
                        ->map(fn($v) => [
                            'title' => optional(@$allowableCities[$v])['title'] ?? optional(@$attendancesCities[$v])['title'] ?? optional(@$invitedCities[$v])['title'],
                            'allowable' => optional(@$allowableCities[$v])['count'] ?? 0,
                            'attendances' => optional(@$attendancesCities[$v])['count'] ?? 0,
                            'invitations' => optional(@$invitedCities[$v])['count'] ?? 0,
                        ]),
                ];
                // cache family for 120m
                Redis::set($cachingKey, json_encode($data), 'EX', 60 * 120);
            } else
                $data = json_decode($cachingData, true);
        }
        return $data;
    }
}
