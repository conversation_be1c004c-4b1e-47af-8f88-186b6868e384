<?php

namespace App\Http\Resources\API;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ActivitySponsorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sponsor' => $this->whenLoaded('sponsor', fn() => [
                'type' => $this->sponsor instanceof User ? 'USER' : 'COMPANY',
                'id' => $this->sponsor_id,
                'title' => $this->sponsor instanceof User ? $this->sponsor->getFullName(2, true) : $this->sponsor->title,
                'photo_url' => $this->sponsor instanceof User ? $this->sponsor->profile_photo_url : $this->sponsor->logo_url,
            ]),
            'support_amount' => $this->support_amount,
            'confidential' => $this->confidential,
        ];
    }
}
