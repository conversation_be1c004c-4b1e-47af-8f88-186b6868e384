<?php

namespace App\Http\Resources\API;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class GroupResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'show_reports_tab' => $this->show_reports_tab,
            'users' => $this->users->map(fn($u) => [
                'id' => $u->id,
                'user' => [
                    'id' => $u->user->id,
                    'name' => $u->user->name,
                    'full_name' => $u->user->getFullName(2, true, true),
                    'gender' => $u->user->gender,
                    'family_user_id' => $u->user->family_user_id,
                    'profile_photo_url' => $u->user->profile_photo_url,
                ],
                'index' => $u->index,
                'role' => $u->role,
            ]),
            $this->mergeWhen(isset($this->statistics), fn() => [
                'statistics' => $this->statistics,
            ]),
        ];
    }
}
