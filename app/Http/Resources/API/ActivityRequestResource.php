<?php

namespace App\Http\Resources\API;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ActivityRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'uuid' => $this->uuid,
            'user' => $this->user->only(['id', 'name', 'gender', 'identifier']),
            'status' => $this->status,
            'activity_member_role_id' => $this->activity_member_role_id,
            'default_role_id' => $this->activity->requests_role_id,
        ];
    }
}
