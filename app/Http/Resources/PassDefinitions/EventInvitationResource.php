<?php

namespace App\Http\Resources\PassDefinitions;

use App\Models\EventInvitation;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EventInvitationResource extends JsonResource
{
    /** @var EventInvitation */
    public $resource;

    /**
     * @param EventInvitation $resource
     * @param string $serialNumber
     * @param string $authenticationToken
     */
    public function __construct(EventInvitation $resource, private readonly string $serialNumber, private readonly string $authenticationToken)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "formatVersion" => 1,
            "passTypeIdentifier" => config('passgenerator.pass_type_identifier'),
            "serialNumber" => $this->serialNumber,
            "authenticationToken" => $this->authenticationToken,
            "teamIdentifier" => config('passgenerator.team_identifier'),
            "webServiceURL" => config('passgenerator.web_service_url'),
            "relevantDate" => ($this->resource->expired_at ?? $this->resource->event->end_at->endOfDay() ?? now()->addWeek())->toAtomString(),
            "locations" => [
                $this->mergeWhen($this->resource->event->lat && $this->resource->event->lng, [
                    [
                        "latitude" => (float)$this->resource->event->lat,
                        "longitude" => (float)$this->resource->event->lng,
                        "relevantText" => "مكان المناسبة",
                    ],
                ]),
            ],
            "barcode" => [
                "message" => "EventInvitation|{$this->resource->uuid}",
                "format" => "PKBarcodeFormatQR",
                "messageEncoding" => "iso-8859-1",
            ],
            "organizationName" => config('passgenerator.organization_name'),
            "description" => 'دعوة ' . $this->resource->event->title,
            "foregroundColor" => "rgb(255, 255, 255)",
            "backgroundColor" => "rgb(60, 65, 76)",
            "eventTicket" => [
                "primaryFields" => [
                    [
                        "key" => "user_name",
                        "label" => 'المدعو',
                        "value" => $this->resource->user ? ($this->resource->user->getName(2, true, false) . " " . $this->resource->user->family_user_id) : $this->resource->guest_name,
                    ],
                ],
                "headerFields" => [
                    [
                        "key" => "event_title",
                        "value" => $this->resource->event->title,
                    ],
                ],
                "auxiliaryFields" => [
                    [
                        "key" => "event_start_at",
                        "label" => "تاريخ المناسبة",
                        "value" => $this->resource->event->start_at,
                        "dateStyle" => "PKDateStyleMedium",
                        "timeStyle" => "PKDateStyleShort",
                        "ignoresTimeZone" => true,
                    ],
                ],
                "secondaryFields" => [
                    $this->mergeWhen($this->resource->event->location_description, [
                        [
                            "key" => "event_location",
                            "label" => "مكان المناسبة",
                            "value" => $this->resource->event->location_description,
                        ],
                    ]),
                ],
                "backFields" => [
                    [
                        "key" => "user_full_name",
                        "label" => "الاسم رباعي",
                        "value" => $this->resource->user ? $this->resource->user->full_name : $this->resource->guest_name,
                    ],
                    $this->mergeWhen(!is_null($this->resource->user), fn() => [
                        [
                            "key" => "user_family_user_id",
                            "label" => "الرقم التعريفي",
                            "value" => $this->resource->user->family_user_id,
                        ],
                    ]),
                    $this->mergeWhen($this->resource->event->location_description, [
                        [
                            "key" => "event_location",
                            "label" => "مكان المناسبة",
                            "value" => $this->resource->event->location_description,
                        ],
                    ]),
                    [
                        "key" => "event_start_at",
                        "label" => "تاريخ المناسبة",
                        "value" => $this->resource->event->start_at,
                        "dateStyle" => "PKDateStyleMedium",
                        "timeStyle" => "PKDateStyleShort",
                        "ignoresTimeZone" => true,
                    ],
                ],
            ],
        ];
    }
}
