<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *   @OA\Property(property="id", type="integer", example="123"),
 *   @OA\Property(property="event_type", type="string"),
 *   @OA\Property(property="status", type="string"),
 *   @OA\Property(property="date", type="string"),
 *   @OA\Property(property="period", type="string"),
 *   @OA\Property(property="services", type="string"),
 *   @OA\Property(property="created_at", type="string", example="2022-02-21T13:00:00.000000Z"),
 * )
 */
class HallReservationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array_merge($this->resource->only([
            'id', 'event_type', 'status', 'event_type_locale', 'status_locale', 'start_at', 'end_at', 'created_at',
        ]), [
            'services' => $this->resource->services->map(fn($service) => [
                'id' => $service->id,
                'title' => $service->title,
                'notes' => $service->pivot->notes,
            ]),
        ]);
    }
}
