<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @OA\Schema(
 *   @OA\Property(property="id", type="integer", example="123"),
 *   @OA\Property(property="title", type="string", example="Carrefour - 20% Off"),
 *   @OA\Property(property="description", type="string", example="Get 20% off on all electronics and home appliances. Limited time offer...", nullable=true, maxLength=80),
 *   @OA\Property(property="logo_url", type="string", format="url", example="https://api.example.com/storage/promotions/logo.png", nullable=true),
 *   @OA\Property(property="discount", type="string", example="20"),
 *   @OA\Property(property="discount_type", type="string", enum={"percentage", "fixed"}, example="percentage"),
 *   @OA\Property(property="coupon", type="string", example="SAVE20"),
 *   @OA\Property(property="is_expired", type="boolean", example=false),
 *   @OA\Property(property="days_remaining", type="integer", example=15, nullable=true),
 *   @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00.000000Z"),
 * )
 */
class StorePromotionIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description ? \Str::limit($this->description, 80) : null,
            'logo_url' => $this->logo_url,
            'discount' => $this->discount,
            'discount_type' => $this->discount_type,
            'coupon' => $this->coupon, // Show coupon code in list
            'is_expired' => $this->valid_until ? now()->isAfter($this->valid_until) : false,
            'days_remaining' => $this->valid_until && now()->isBefore($this->valid_until) 
                ? now()->diffInDays($this->valid_until) 
                : null,
            'created_at' => $this->created_at,
        ];
    }
}
