<?php

namespace App\Http\Resources\Mobile;

use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use App\Http\Resources\API\UserFilterResource;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @OA\Schema(
 *   @OA\Property(property="id", type="integer", example=""),
 *   @OA\Property(property="created_at", type="string", example="2022-02-21T13:00:00.000000Z"),
 * )
 */
class FriendshipResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     *
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        return $this->only(['id', 'name', 'full_name', 'family_user_id', 'gender', 'profile_photo_url']);
    }
}
