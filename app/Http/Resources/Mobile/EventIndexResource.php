<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @OA\Schema(
 *   @OA\Property(property="id", type="integer", example="123"),
 *   @OA\Property(property="title", type="string", example="ramdan 2022"),
 *   @OA\Property(property="created_at", type="string", example="2022-02-21T13:00:00.000000Z"),
 * )
 */
class EventIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'cover_thumb_url' => $this->cover_thumb_url,
            'due_at' => $this->start_at->toDateString(),
            'start_at' => $this->start_at->format('h:i A'),
            'end_at' => $this->end_at->format('h:i A'),
            'created_at' => $this->created_at,
        ];
    }
}
