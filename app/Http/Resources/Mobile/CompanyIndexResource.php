<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use JsonSerializable;

/**
 * @OA\Schema(
 *   @OA\Property(property="id", type="integer", example="123"),
 *   @OA\Property(property="title", type="string", example="ramdan 2022"),
 *   @OA\Property(property="created_at", type="string", example="2022-02-21T13:00:00.000000Z"),
 * )
 */
class CompanyIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'thumb_url' => $this->thumb_url,
            'created_at' => $this->created_at,
        ];
    }
}
