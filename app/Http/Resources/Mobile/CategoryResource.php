<?php

namespace App\Http\Resources\Mobile;

use App\Enums\CategoryType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *   @OA\Property(property="id", type="integer", example=""),
 *   @OA\Property(property="title", type="string", example=""),
 *   @OA\Property(property="type", type="string", example=""),
 * )
 */
class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     *
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        return [
            $this->merge($this->resource->only([
                'id', 'title', 'type', 'index', //'created_at'
            ])),
            'data' => [
                $this->mergeWhen($this->type === CategoryType::Product, [
                    'products_count' => $this->products_count,
                ]),
            ],
        ];
    }
}
