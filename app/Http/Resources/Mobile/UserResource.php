<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use App\Settings\AppSettings;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *   @OA\Property(property="id",        type="integer", example="1"),
 *   @OA\Property(property="family_user_id", type="string", example="56814"),
 *   @OA\Property(property="name",      type="string", example="Hesham"),
 *   @OA\Property(property="full_name", type="string", example="Hesham Fouda"),
 *   @OA\Property(property="marital_status", type="string", example="MARRIED"),
 *   @OA\Property(property="phone",     type="string", example="966550000000"),
 *   @OA\Property(property="email",     type="string", example="<EMAIL>"),
 *   @OA\Property(property="gender",    type="string", example="MALE"),
 *   @OA\Property(property="bio",       type="string", example="Bio"),
 *   @OA\Property(property="dob",       type="string", example="2023-03-25"),
 *   @OA\Property(property="is_dead",   type="bool", example="false"),
 *   @OA\Property(property="dod",       type="string", example="2023-03-25"),
 *   @OA\Property(property="profile_photo_url", type="string", example=null),
 *   @OA\Property(property="roles",     type="string", example=null),
 *   @OA\Property(property="parent",     type="string", example=null),
 *   @OA\Property(property="siblings",     type="string", example=null),
 *   @OA\Property(property="children",     type="string", example=null),
 * )
 */
class UserResource extends JsonResource
{
    private mixed $withRelations;

    public function __construct($resource, $withRelations = false)
    {
        parent::__construct($resource);
        $this->withRelations = $withRelations;
    }

    /**
     * @param Request $request
     * @return array
     */
    public function toArray($request)
    {
        $inReview = $this->is_apple_reviewer && app(AppSettings::class)->reviewMode;
        return [
            'id' => $this->id,
            'family_user_id' => $this->family_user_id,
            'name' => $this->name,
            'full_name' => $this->resource->getName(3, true),
            'marital_status' => $this->marital_status,
            'phone' => $this->phone,
            'email' => $this->email,
            'gender' => $this->gender,
            'health_status' => $this->health_status,
            'educational_status' => $this->educational_status,
            'bio' => $this->bio,
            'dob' => $this->dob?->toDateString(),
            'is_dead' => $this->is_dead,
            'is_system_admin' => $this->is_system_admin,
            'dod' => $this->dod?->toDateString(),
            'profile_photo_url' => $this->profile_photo_url,
            'linkedin_url' => $this->linkedin_url,
            'user_region_id' => $this->user_region_id,
            'user_region' => optional($this->user_region)->only(['id', 'title_en', 'title_ar', 'created_at']),
            'father_id' => $this->father_id,
            'membership' => $this->when($this->membership, fn() => array_merge($this->membership->only(['start_at', 'expired_at', 'actual_balance']), [
                'package' => $this->membership->package->only(['id', 'title', 'slug', 'upgradable_packages_id'])
            ])),
            'roles' => $this->roles()->get()->mapWithKeys(fn($role) => [$role->name => $role->title]),
            //'parents' => collect(nested_parents(user()))->map->only(['id', 'name', 'gender', 'profile_photo_url', 'father_id']),
            'parents' => [],
            'siblings' => [],
            'children' => [],
            'in_review' => $inReview,
            'can_update_picture' => !$inReview,
            'unfilled_children' => ($this->unfilled_children_count ?? 0) > 0,
            //'siblings' => $this->whenLoaded('father', fn() => $this->father->children->reject(fn($child) => $child->id === $this->id)->values()->map->only(['id', 'name', 'gender', 'profile_photo_url']), []),
            //'children' => $this->whenLoaded('children', fn() => $this->children->map->only(['id', 'name', 'gender', 'profile_photo_url']), []),
        ];
    }
}
