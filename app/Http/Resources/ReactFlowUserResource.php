<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ReactFlowUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'hasPhone' => !is_null($this->phone),
            //'phone_number' => $this->phone,
            'isDead' => $this->is_dead,
            //'className' => $this->className,
            'fatherId' => $this->father_id,
            'gender' => $this->gender,
        ];
    }
}
