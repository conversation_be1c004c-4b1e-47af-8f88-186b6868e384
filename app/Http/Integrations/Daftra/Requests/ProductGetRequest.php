<?php

namespace App\Http\Integrations\Daftra\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Request\HasConnector;
use App\Http\Integrations\Daftra\APIConnector;

class ProductGetRequest extends Request
{
    use HasConnector;

    protected string $connector = APIConnector::class;
    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::GET;

    /**
     * @param int $ProductId
     */
    public function __construct(protected int $ProductId)
    {
    }

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "/products/{$this->ProductId}.json";
    }
}
