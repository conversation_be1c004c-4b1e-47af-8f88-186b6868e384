<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class StoreLanguageRequest extends Request
{
    protected Method $method = Method::POST;

    public function __construct(
        protected array $data
    ) {
    }

    public function resolveEndpoint(): string
    {
        return '/v1/languages';
    }

    protected function defaultBody(): array
    {
        return $this->data;
    }
}
