<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Traits\Body\HasJsonBody;

class RenderPdfRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(
        protected string $documentTemplateUuid,
        protected array  $data = []
    )
    {
    }

    public function resolveEndpoint(): string
    {
        return "/v1/document-templates/{$this->documentTemplateUuid}/pdfs";
    }

    protected function defaultBody(): array
    {
        return [
            'variables' => $this->data ?? [],
        ];
    }
}
