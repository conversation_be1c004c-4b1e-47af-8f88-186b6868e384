<?php

namespace App\Http\Integrations\Waha\Requests;

use App\Models\BroadcastChannelSender;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Request\HasConnector;
use App\Http\Integrations\Waha\APIConnector;

class StopTypingRequest extends Request implements HasBody
{
    use HasConnector, HasJsonBody;

    protected string $connector = APIConnector::class;
    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * @param string $phone
     * @param BroadcastChannelSender|null $channelSender
     */
    public function __construct(
        protected string                   $phone,
        public BroadcastChannelSender|null $channelSender = null,
    )
    {
        //
    }

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "/api/stopTyping";
    }

    protected function defaultBody(): array
    {
        $settings = app(\App\Settings\ServicesSettings::class);
        return [
            'chatId' => $this->phone,
            'session' => $this->channelSender?->sender_name ?? $settings->wahaSession,
        ];
    }
}
