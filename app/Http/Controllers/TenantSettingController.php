<?php

namespace App\Http\Controllers;

use App\Http\Middleware\BasicAuthMiddleware;
use App\Rules\FQDN;
use App\Settings\AppSettings;
use App\Settings\FeatureSettings;
use App\Settings\GeneralSettings;
use App\Settings\ServicesSettings;
use Illuminate\Http\Request;

class TenantSettingController extends BaseController
{
    public function __construct()
    {
        $this->middleware(BasicAuthMiddleware::class);
    }

    public function index(
        GeneralSettings  $generalSettings,
        FeatureSettings  $featureSettings,
        ServicesSettings $servicesSettings,
        AppSettings      $appSettings,
    )
    {
        return view('tenant-setting.index', compact(['appSettings', 'generalSettings', 'featureSettings', 'servicesSettings']));
    }

    public function store(
        Request          $request,
        GeneralSettings  $generalSettings,
        FeatureSettings  $featureSettings,
        ServicesSettings $servicesSettings,
        AppSettings      $appSettings
    )
    {
        $data = $request->validate([
            'isDemo' => ['nullable'],
            'familyNameEn' => ['required'],
            'familyNameAr' => ['required'],
            'orgName' => ['required'],
            'appLongName' => ['required'],
            'appDescription' => ['required'],
            'infoMail' => ['nullable', 'email'],
            'phone' => ['nullable'],
            'portalDomain' => ['nullable', new FQDN()],
            'domain' => ['nullable', new FQDN()],

            'my_sign_url' => ['nullable', 'url'],
            'my_sign_api_key' => ['nullable', 'string'],

            'waha_url' => ['nullable', 'url'],
            'waha_api_key' => ['nullable', 'string'],
            'waha_session' => ['nullable', 'string'],

            'app_domain' => ['nullable', new FQDN()],
            'app_url' => ['nullable', 'url'],
            'store_url' => ['nullable', 'url'],

            'appLottiePlayer' => ['nullable', 'url'],

            'primaryColor' => ['nullable'],
            'secondaryColor' => ['nullable'],
            'primaryDarkColor' => ['nullable'],
            'secondaryDarkColor' => ['nullable'],
            'svgPrimaryColor' => ['nullable'],
            'svgSecondaryColor' => ['nullable'],

            'memberships_enabled' => ['nullable'],
            'strava_enabled' => ['nullable'],
            'short_url_enabled' => ['nullable'],
            'waha_enabled' => ['nullable'],
            //'daftra_treasury_id' => ['nullable'],

            'taqnyat_key' => ['nullable'],
            'taqnyat_sender' => ['nullable'],

            'cloudAccessKeyId' => ['nullable'],
            'cloudSecretAccessKey' => ['nullable'],
            'cloudBucket' => ['nullable'],
            'cloudEndpoint' => ['nullable'],
            'cloudUrl' => ['nullable'],

            'iconPath' => ['nullable', 'image'],
            'iconPathLight' => ['nullable', 'image'],
            'iconPathDark' => ['nullable', 'image'],
            'logoPathLight' => ['nullable', 'image'],
            'logoPathDark' => ['nullable', 'image'],
            'awardUserIcon' => ['nullable', 'image'],

            'iosMinVersion' => ['nullable', 'string', 'regex:/^\d+\.\d+\.\d+$/'],
            'iosCurrentVersion' => ['nullable', 'string', 'regex:/^\d+\.\d+\.\d+$/'],
            'iosAppUrl' => ['nullable', 'url'],
            'collectingFormUrl' => ['nullable', 'url'],
        ]);
        $generalSettings->isDemo = !empty($data['isDemo']);
        $generalSettings->appLongName = $data['appLongName'];
        $generalSettings->familyNameEn = $data['familyNameEn'];
        $generalSettings->familyNameAr = $data['familyNameAr'];
        $generalSettings->appDescription = $data['appDescription'];
        $generalSettings->phone = $data['phone'];
        $generalSettings->orgName = $data['orgName'];
        $generalSettings->infoMail = $data['infoMail'];
        $generalSettings->domain = $data['domain'];
        $generalSettings->portalDomain = $data['portalDomain'];

        $featureSettings->membershipsEnabled = !empty($data['memberships_enabled']);
        $featureSettings->stravaEnabled = !empty($data['strava_enabled']);
        $featureSettings->shortUrlEnabled = !empty($data['short_url_enabled']);
        $featureSettings->wahaEnabled = !empty($data['waha_enabled']);
        //$featureSettings->daftraTreasuryId = !empty($data['daftra_treasury_id']);

        $servicesSettings->mySignUrl = $data['my_sign_url'];
        $servicesSettings->mySignApiKey = $data['my_sign_api_key'];
        $servicesSettings->wahaUrl = $data['waha_url'];
        $servicesSettings->wahaApiKey = $data['waha_api_key'];
        $servicesSettings->wahaSession = $data['waha_session'];
        $servicesSettings->taqnyatKey = $data['taqnyat_key'];
        $servicesSettings->taqnyatSender = $data['taqnyat_sender'];
        $servicesSettings->cloudAccessKeyId = $data['cloudAccessKeyId'];
        $servicesSettings->cloudSecretAccessKey = $data['cloudSecretAccessKey'];
        $servicesSettings->cloudBucket = $data['cloudBucket'];
        $servicesSettings->cloudEndpoint = $data['cloudEndpoint'];
        $servicesSettings->cloudUrl = $data['cloudUrl'];

        $appSettings->appDomain = $data['app_domain'];
        $appSettings->appUrl = $data['app_url'];
        $appSettings->storeUrl = $data['store_url'];

        $appSettings->iosMinVersion = $data['iosMinVersion'];
        $appSettings->iosCurrentVersion = $data['iosCurrentVersion'];
        $appSettings->iosAppUrl = $data['iosAppUrl'];
        $appSettings->collectingFormUrl = $data['collectingFormUrl'];

        $appSettings->appLottiePlayer = $data['appLottiePlayer'];

        foreach (['primaryColor', 'secondaryColor', 'primaryDarkColor', 'secondaryDarkColor', 'svgPrimaryColor', 'svgSecondaryColor'] as $key) {
            $appSettings->$key = $data[$key];
        }

        foreach (['iconPath', 'iconPathLight', 'iconPathDark', 'logoPathLight', 'logoPathDark', 'awardUserIcon'] as $key) {
            if ($request->hasFile($key)) {
                $fileName = basename($request->file($key)->storePublicly('public/uploads'));
                $appSettings->$key = [
                    'path' => "uploads/$fileName",
                    'disk' => 'tenant',
                ];
            }
        }

        $generalSettings->save();
        $featureSettings->save();
        $servicesSettings->save();
        $appSettings->save();

        return back()->with('message', 'تم تعديل الإعدادات بنجاح');
    }
}
