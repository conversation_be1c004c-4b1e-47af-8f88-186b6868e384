<?php

namespace App\Http\Controllers\Portal;

use Str;
use Auth;
use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use App\Settings\AppSettings;
use App\Models\FamilyGraphShare;
use Illuminate\Http\JsonResponse;
use App\Settings\FeatureSettings;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\ViewErrorBag;
use Illuminate\Contracts\View\Factory;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Validator;
use Illuminate\Contracts\Foundation\Application;
use AshAllenDesign\ShortURL\Exceptions\ShortURLException;
use AshAllenDesign\ShortURL\Facades\ShortURL as ShortURLFacade;
use function user;

class FamilyGraphShareController extends BaseController
{
    /**
     * GroupReportController constructor.
     */
    public function __construct()
    {
        $this->authorizeResource(FamilyGraphShare::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|View|JsonResponse
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax() || $request->has('draw')) {
            return DataTables::eloquent(FamilyGraphShare::query()->orderByDesc('id')->with(['user.father.father.father']))
                ->addColumn('user_name', fn($familyGraph) => $familyGraph->user->full_name)
                ->editColumn('created_at', fn($familyGraph) => $familyGraph->created_at ? $familyGraph->created_at->timezone(app("timezone"))->toDayDateTimeString() : null)
                ->editColumn('expired_at', fn($familyGraph) => $familyGraph->expired_at ? $familyGraph->expired_at->timezone(app("timezone"))->toDateString() : 'بدون')
                ->editColumn('last_view_at', fn($familyGraph) => $familyGraph->last_view_at ? $familyGraph->last_view_at->diffForHumans() : 'لا يوجد')
                ->addColumn('update_url', fn($familyGraph) => can('update', $familyGraph) ? route('admin.family-graph-shares.edit', $familyGraph) : null)
                ->addColumn('delete_url', fn($familyGraph) => can('delete', $familyGraph) ? route('admin.family-graph-shares.destroy', $familyGraph) : null)
                ->addColumn('shared_url', fn($familyGraph) => $familyGraph->share_url)
                ->addIndexColumn()
                ->only([
                    'id', 'user_name', 'views', 'last_view_at', 'expired_at', 'created_at', 'deleted_at', 'last_view_at',
                    'shared_url', 'update_url', 'delete_url',
                ])
                ->make();
        }
        return view('family-graph-shares.index');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @param AppSettings $appSettings
     * @param FeatureSettings $featureSettings
     *
     * @return Application|Factory|View
     * @throws ShortURLException
     */
    public function store(Request $request, AppSettings $appSettings, FeatureSettings $featureSettings)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => ['required', 'exists:users,id'],
            'expired_at' => ['nullable', 'date', 'after_or_equal:' . now()->toDateString()],
            'password' => ['nullable', 'string', 'max:12'],
        ], [], Lang::get('familyGraphShareColumns'));
        if ($validator->fails())
            return view('family-graph-shares.createUpdateForm')
                ->with('errors', (new ViewErrorBag())->put('default', $validator->getMessageBag()));
        $tries = 0;
        do {
            $tries += 1;
            $slug = Str::random($tries > 10 ? 8 : 6);
        } while (FamilyGraphShare::query()->where('slug', $slug)->exists());
        /** @var FamilyGraphShare $familyGraphShare */
        $familyGraphShare = FamilyGraphShare::query()->create(array_merge([
            'slug' => $slug,
            'created_by_id' => Auth::id(),
        ], $request->only(['user_id', 'password', 'expired_at'])));

        if ($featureSettings->shortUrlEnabled) {
            $user = User::query()->with(['father'])->find($request->post('user_id'));
            $domain = "https://{$appSettings->appDomain}";
            URL::forceRootUrl($domain);
            $url = url("family-graph/{$slug}");
            $shortURL = ShortURLFacade::destinationUrl($url)
                ->trackVisits(true)
                ->make();
            $shortURL->update([
                'title' => 'مشجرة ' . $user->getName(1),
                'source' => 'FAMILY_GRAPH_SHARES',
            ]);
            $familyGraphShare->short_url()->associate($shortURL)->save();
        }

        return view('family-graph-shares.view', compact('familyGraphShare'));
        /*if ($request->post('ref') === 'users-view')
        $request->request->replace([]);
        return view('family-graph-shares.createUpdateForm', [
            'url' => signedRoute('family-graph', $familyGraphShare),
            'message' => 'تم إضافة الرابط بنجاح .'
        ]);*/
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('family-graph-shares.createUpdateForm');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param FamilyGraphShare $familyGraphShare
     *
     * @return Application|Factory|View
     * @throws ShortURLException
     */
    public function update(Request $request, FamilyGraphShare $familyGraphShare, FeatureSettings $featureSettings)
    {
        $validator = Validator::make($request->all(), [
            'expired_at' => ['nullable', 'date'],
            'password' => ['nullable', 'string', 'max:12'],
        ], [], Lang::get('familyGraphShareColumns'));
        if ($validator->fails())
            return view('family-graph-shares.createUpdateForm', [
                'familyGraphShare' => $familyGraphShare,
            ])->with('errors', (new ViewErrorBag())->put('default', $validator->getMessageBag()));
        $familyGraphShare->update(array_merge([

        ], $request->only(['password', 'expired_at'])));
        if (is_null($familyGraphShare->short_url) && $featureSettings->shortUrlEnabled) {
            $user = $familyGraphShare->user->load(['father']);
            $shortURL = ShortURLFacade::destinationUrl(signedRoute('family-graph', $familyGraphShare))
                ->trackVisits(true)->make();
            $shortURL->update([
                'title' => 'مشجرة ' . $user->getName(1),
                'source' => 'FAMILY_GRAPH_SHARES',
            ]);
            $familyGraphShare->short_url()->associate($shortURL)->save();
        }
        $request->request->replace([]);
        return view('family-graph-shares.view', [
            'familyGraphShare' => $familyGraphShare,
            'message' => 'تم تعديل الرابط بنجاح .',
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param FamilyGraphShare $familyGraphShare
     *
     * @return Application|Factory|\Illuminate\Foundation\Application|\Illuminate\View\View|View
     */
    public function show(FamilyGraphShare $familyGraphShare)
    {
        return view('family-graph-shares.view', [
            'familyGraphShare' => $familyGraphShare,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param FamilyGraphShare $familyGraphShare
     *
     * @return Application|Factory|View
     */
    public function edit(FamilyGraphShare $familyGraphShare)
    {
        return view('family-graph-shares.createUpdateForm', [
            'familyGraphShare' => $familyGraphShare,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param FamilyGraphShare $familyGraphShare
     *
     * @return JsonResponse
     */
    public function destroy(FamilyGraphShare $familyGraphShare)
    {
        $familyGraphShare->update([
            'deleted_by_id' => user()->id,
        ]);
        $familyGraphShare->delete();
        return response()->json(['success' => true]);
    }
}
