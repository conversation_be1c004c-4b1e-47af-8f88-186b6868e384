<?php

namespace App\Http\Controllers\Portal;

use Exception;
use App\Models\User;
use App\Models\Supporter;
use App\Models\BankAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\ViewErrorBag;
use Illuminate\Contracts\View\Factory;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use niklasravnsborg\LaravelPdf\Facades\Pdf;
use Illuminate\Contracts\Foundation\Application;

class SupporterController extends \App\Http\Controllers\BaseController
{
    /**
     * GroupReportController constructor.
     */
    public function __construct()
    {
        $this->authorizeResource(Supporter::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|JsonResponse|View
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax() || $request->has('draw')) {
            return DataTables::eloquent(
                Supporter::query()->withCount('transactions')->withSum('transactions', 'amount')
            )->addColumn('support_url', function (Supporter $supporter) {
                return canAny(['create', 'update'], $supporter) ? Route('admin.supporters.transactions.index', $supporter) : null;
            })->addColumn('update_url', function (Supporter $supporter) {
                return can('update', $supporter) ? Route('admin.supporters.show', $supporter) : null;
            })->addColumn('updated_at', function (Supporter $supporter) {
                return $supporter->updated_at ? $supporter->updated_at->timezone(app('timezone'))->toDayDateTimeString() : null;
            })->addColumn('delete_url', function (Supporter $supporter) {
                return can('delete', $supporter) ? Route('admin.supporters.destroy', $supporter) : null;
            })->addColumn('pdf_url', function (Supporter $supporter) {
                return $supporter->transactions_count > 0 ? Route('admin.supporters.pdf', $supporter) : null;
            })->editColumn(
                'transactions_sum_amount', '{{number_format($transactions_sum_amount)}}'
            )->only([
                'id', 'name', 'account', 'phone', 'transactions_count', 'transactions_sum_amount',
                'support_url', 'pdf_url', 'update_url', 'updated_at', 'delete_url',
            ])->make(true);
        }
        return view('supporter.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('supporter.createUpdateForm', [
            'bankAccounts' => BankAccount::all(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['nullable', 'string'],
            'phone' => ['nullable', 'string', 'unique:supporters,phone'],
            'account' => ['required', 'numeric'],
            'user_id' => ['nullable', Rule::exists(User::class, 'id')],

            'bank_account_id' => ['required_with:amount', 'nullable', 'exists:bank_accounts,id'],
            'amount' => ['required_with:bank_account_id', 'nullable', 'numeric'],
            'reference' => ['nullable', 'string', 'max:191'],
            'due_at' => ['nullable', 'date'],
        ], [], Lang::get('supporterColumns'));
        if ($validator->fails())
            return view('supporter.createUpdateForm', [
                'bankAccounts' => BankAccount::all(),
                'errors' => (new ViewErrorBag())->put('default', $validator->getMessageBag()),
            ]);
        /** @var Supporter $supporter */
        $supporter = Supporter::query()->where('account', $request->post('account'))->first();
        if (is_null($supporter)) {
            $supporter = Supporter::query()->create($request->only(['name', 'phone', 'account', 'user_id']));
            $message = 'تم إضافة حساب الداعم بنجاح.';
        } else
            $message = 'تم إضافة التحويل بنجاح.';
        if ($request->filled(['bank_account_id', 'amount']))
            $supporter->transactions()->create($request->only(['bank_account_id', 'amount', 'due_at', 'reference']));
        $request->request->replace([]);
        return view('supporter.createUpdateForm', [
            'bankAccounts' => BankAccount::all(),
            'message' => $message,
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param Supporter $supporter
     *
     * @return Application|Factory|View
     */
    public function show(Supporter $supporter)
    {
        return view('supporter.createUpdateForm', [
            'bankAccounts' => BankAccount::all(),
            'supporter' => $supporter,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param Supporter $supporter
     *
     * @return Application|Factory|View
     */
    public function update(Request $request, Supporter $supporter)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['nullable', 'string'],
            'phone' => ['nullable', 'string', 'unique:supporters,phone,' . $supporter->id . ',id'],
            'account' => ['required', 'numeric', 'unique:supporters,account,' . $supporter->id . ',id'],
            'user_id' => ['nullable', Rule::exists(User::class, 'id')],
        ], [], Lang::get('supporterColumns'));
        if ($validator->fails())
            return view('supporter.createUpdateForm', [
                'bankAccounts' => BankAccount::all(),
                'supporter' => $supporter,
                'errors' => (new ViewErrorBag())->put('default', $validator->getMessageBag()),
            ]);

        $message = 'تم تحديث حساب الداعم بنجاح .';
        $supporter->update($request->only(['name', 'phone', 'account', 'user_id']));
        $request->request->replace([]);


        return view('supporter.createUpdateForm', [
            'bankAccounts' => BankAccount::all(),
            'supporter' => $supporter,
            'message' => $message,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Supporter $supporter
     *
     * @return void
     */
    public function destroy(Supporter $supporter)
    {
        try {
            DB::beginTransaction();
            $supporter->transactions()->delete();
            $supporter->delete();
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            abort(401, $exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param Supporter $supporter
     *
     * @return Response
     */
    public function pdf(Request $request, Supporter $supporter)
    {
        $supporter->load('transactions.bank_account');
        return (Pdf::loadView('supporter.pdf', compact('supporter')))->stream('supporter-' . $supporter->id . '.pdf');
    }
}
