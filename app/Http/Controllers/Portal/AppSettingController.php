<?php

namespace App\Http\Controllers\Portal;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Settings\GeneralSettings;
use App\Settings\FeatureSettings;
use App\Permissions\GeneralPermissions;

class AppSettingController extends \App\Http\Controllers\BaseController
{
    public function show(Request $request, GeneralSettings $generalSettings, FeatureSettings $featureSettings)
    {
        user()->canAny([
            GeneralPermissions::SETTING_ZAKAT,
            GeneralPermissions::GENERAL,
        ]);
        return view('app-setting', compact('generalSettings', 'featureSettings'));
    }

    public function store(Request $request, GeneralSettings $generalSettings, FeatureSettings $featureSettings)
    {
        $rules = [
            'daftra_treasury_id' => ['nullable'],
            'waha_enabled' => ['required', 'boolean'],
        ];
        if (user()->hasPermissionTo(GeneralPermissions::SETTING_ZAKAT)) {
            $rules = array_merge($rules, [
                'zakat_type' => ['required', 'in:PERCENTAGE,STATIC'],
                'zakat_value' => [
                    'required', 'numeric', 'min:0',
                    Rule::when($request->post('zakat_type') === 'PERCENTAGE', [
                        'max:100',
                    ]),
                ],
            ]);
        }
        $data = $request->validate($rules);
        if (user()->hasPermissionTo(GeneralPermissions::SETTING_ZAKAT)) {
            $generalSettings->zakatType = $data['zakat_type'];
            $generalSettings->zakatValue = $data['zakat_value'];
            $generalSettings->save();
        }
        if (user()->hasPermissionTo(GeneralPermissions::GENERAL)) {
            $featureSettings->daftraTreasuryId = $data['daftra_treasury_id'];
        }
        $featureSettings->wahaEnabled = boolval(@$data['waha_enabled']);
        $featureSettings->save();
        return back()->with('message', 'تم تعديل الإعدادات بنجاح');
    }
}
