<?php

namespace App\Http\Controllers\Portal;

use Illuminate\Http\Request;
use App\Models\BulkNotification;
use App\Permissions\GeneralPermissions;
use Yajra\DataTables\Facades\DataTables;

class NotificationController extends \App\Http\Controllers\BaseController
{
    public function index(Request $request)
    {
        $this->authorize(GeneralPermissions::BULK_NOTIFICATIONS);

        if ($request->ajax() || $request->has('draw')) {
            /*BulkNotification::query()
                ->whereDoesntHave('items', fn($q) => $q->whereNull('sent_at'))
                ->get()->each(function ($i) {
                    $i->update(['sent_at' => $i->items()->max('sent_at')]);
                });
            BulkNotification::query()
                ->whereDoesntHave('items', fn($q) => $q->whereNull('delivered_at'))
                ->get()->each(function ($i) {
                    $i->update(['delivered_at' => $i->items()->max('delivered_at')]);
                });*/
            return DataTables::eloquent(BulkNotification::query()->orderByDesc('id'))
                ->addColumn('created_at', function (BulkNotification $bulkNotification) {
                    return $bulkNotification->created_at ?
                        $bulkNotification->created_at->timezone(app('timezone'))->toDayDateTimeString() : null;
                })
                ->addColumn('sent_at', function (BulkNotification $bulkNotification) {
                    return $bulkNotification->sent_at ?
                        $bulkNotification->sent_at->timezone(app('timezone'))->toDayDateTimeString() : null;
                })
                ->addColumn('delivered_at', function (BulkNotification $bulkNotification) {
                    return $bulkNotification->delivered_at ?
                        $bulkNotification->delivered_at->timezone(app('timezone'))->toDayDateTimeString() : null;
                })
                ->addColumn('user_name', function (BulkNotification $bulkNotification) {
                    return $bulkNotification->is_system_notification ?
                        'SYSTEM' : optional($bulkNotification->user)->getName(2);
                })
                ->addColumn('status', function (BulkNotification $bulkNotification) {
                    $status = [];
                    /*$sent = $bulkNotification->items()->whereNotNull('sent_at')->whereNull('delivered_at')->count();
                    $delivered = $bulkNotification->items()->whereNotNull('delivered_at')->count();
                    $pending = $bulkNotification->items()->whereNull('sent_at')->whereNull('delivered_at')->count();
                    $failed = $bulkNotification->items()->whereNotNull('failed_at')->count();*/
                    $sent = '-';
                    $delivered = '-';
                    $pending = '-';
                    $failed = '-';
                    //if ($pending > 0)
                    $status[] = "<span class=\"p-50 badge badge-pill badge-light-secondary\">{$pending}</span>";
                    //if ($failed > 0)
                    $status[] = "<span class=\"p-50 badge badge-pill badge-light-danger\">{$failed}</span>";
                    //if ($sent > 0)
                    $status[] = "<span class=\"p-50 badge badge-pill badge-light-info\">{$sent}</span>";
                    //if ($delivered > 0)
                    $status[] = "<span class=\"p-50 badge badge-pill badge-light-success\">{$delivered}</span>";
                    return implode('&nbsp;', $status);
                })
                ->rawColumns(['status'])
                ->only([
                    'id', 'user_name', 'status', 'created_at', 'sent_at', 'delivered_at',
                ])
                ->make();
        }
        return view('notifications.index');
    }

    public function create()
    {
        $this->authorize(GeneralPermissions::BULK_NOTIFICATIONS);
        return view('notifications.form');
    }
}
