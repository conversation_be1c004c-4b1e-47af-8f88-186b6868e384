<?php

namespace App\Http\Controllers\Portal;

use Log;
use Exception;
use Throwable;
use setasign\Fpdi\Fpdi;
use App\Models\HelpForm;
use App\Enums\CaseStatus;
use App\Models\ModelAudit;
use App\Models\HelpFormItem;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Utils\HelpFormPdfUtil;
use App\Models\HelpFormInvoice;
use Illuminate\Validation\Rule;
use App\Models\HelpFormTemplate;
use Yajra\DataTables\DataTables;
use App\Enums\FamilyRelationType;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\View;
use App\Models\HelpFormFamilyMember;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Contracts\View\Factory;
use Illuminate\Support\Facades\Storage;
use App\Permissions\HelpFormPermissions;
use App\Http\Controllers\BaseController;
use Illuminate\Database\Eloquent\Builder;
use niklasravnsborg\LaravelPdf\Facades\Pdf;
use setasign\Fpdi\PdfParser\PdfParserException;
use setasign\Fpdi\PdfReader\PdfReaderException;
use Illuminate\Contracts\Foundation\Application;
use setasign\Fpdi\PdfParser\Type\PdfTypeException;
use App\Notifications\HelpFormApprovedNotification;
use App\Notifications\HelpFormDeclinedNotification;
use setasign\Fpdi\PdfParser\Filter\FilterException;
use Webklex\PDFMerger\Facades\PDFMergerFacade as PDFMerger;
use setasign\Fpdi\PdfParser\CrossReference\CrossReferenceException;

class HelpFormController extends BaseController
{
    public function __construct()
    {
        $this->authorizeResource(HelpForm::class);
        $this->middleware('can:restore,trashedHelpForm')->only('restore');
    }

    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|View|JsonResponse
     * @throws Exception
     */
    public function index()
    {
        if (request()->ajax() || request()->has('draw')) {
            $query = HelpForm::query()
                ->where(fn(Builder $query) => $query->whereNotNull('completed_at')->orWhere(function (Builder $query) {
                    $query->whereNull('completed_at')->where('created_by_id', auth()->id());
                }))
                ->where(function (Builder $q) {
                    if (!user()->hasPermissionTo(HelpFormPermissions::view))
                        $q->where('user_id', Auth::id())
                            ->orWhere('responsible_user_id', Auth::id())
                            ->orWhere('created_by_id', Auth::id());
                })
                ->with(['template', 'user.father.father.father', 'responsible_user.father.father.father', 'created_by.father.father.father']);
            match ($query->driver()) {
                'mariadb', 'mysql' => $query->orderByRaw('ISNULL(completed_at) DESC')->orderByDesc('completed_at'),
                'pgsql' => $query->orderByRaw('(completed_at IS NULL) DESC')->orderByDesc('completed_at'),
                //'sqlite' => ,
            };
            return DataTables::of($query)
                ->filterColumn('template', function ($query, $id) {
                    if (!empty($id))
                        $query->where('help_form_template_id', $id);
                })
                ->filterColumn('completed_at', function ($query, $keyword) {
                    if (!empty($keyword))
                        $query->where('hijri_year', $keyword);
                })
                ->filterColumn('responsible_user_id', function ($query, $keyword) {
                    $words = array_values(array_filter(explode(' ', $keyword), function ($i) {
                        return !empty($i) && $i !== 'بن' && $i !== 'بنت';
                    }));
                    if (count($words) >= 1) {
                        $query
                            ->whereNotNull('completed_at')
                            ->whereHas('responsible_user', function ($query) use ($words, $keyword) {
                                $query->where(function ($query) use ($words) {
                                    $query->whereSplit('name', $words[0]);
                                    collect(array_slice($words, 1))->each(function ($word, $index) use (&$query, $words) {
                                        $rel = trim(str_repeat('father.', $index + 1), '.');
                                        $query->where(function ($query) use ($rel, $word, $words, $index) {
                                            $query->whereHas($rel, function ($q) use ($word) {
                                                $q->whereSplit('name', $word);
                                            });
                                            if (count($words) === $index + 2)
                                                $query->orWhereHas('family_title', function ($q) use ($word) {
                                                    $q->where('title', $word);
                                                });
                                        });
                                    });
                                })->orWhere('family_user_id', $keyword);
                            })
                            ->orWhereHas('created_by', function ($query) use ($words, $keyword) {
                                $query->where(function ($query) use ($words) {
                                    $query->whereSplit('name', $words[0]);
                                    collect(array_slice($words, 1))->each(function ($word, $index) use (&$query, $words) {
                                        $rel = trim(str_repeat('father.', $index + 1), '.');
                                        $query->where(function ($query) use ($rel, $word, $words, $index) {
                                            $query->whereHas($rel, function ($q) use ($word) {
                                                $q->whereSplit('name', $word);
                                            });
                                            if (count($words) === $index + 2)
                                                $query->orWhereHas('family_title', function ($q) use ($word) {
                                                    $q->where('title', $word);
                                                });
                                        });
                                    });
                                })->orWhere('family_user_id', $keyword);
                            });
                    }
                })
                ->filterColumn('user', function ($query, $keyword) {
                    $words = array_values(array_filter(explode(' ', $keyword), function ($i) {
                        return !empty($i) && $i !== 'بن' && $i !== 'بنت';
                    }));
                    if (count($words) >= 1) {
                        $query
                            ->whereNotNull('completed_at')
                            ->whereHas('user', function ($query) use ($words, $keyword) {
                                $query->where(function ($query) use ($words) {
                                    $query->whereSplit('name', $words[0]);
                                    collect(array_slice($words, 1))->each(function ($word, $index) use (&$query, $words) {
                                        $rel = trim(str_repeat('father.', $index + 1), '.');
                                        $query->where(function ($query) use ($rel, $word, $words, $index) {
                                            $query->whereHas($rel, function ($q) use ($word) {
                                                $q->whereSplit('name', $word);
                                            });
                                            if (count($words) === $index + 2)
                                                $query->orWhereHas('family_title', function ($q) use ($word) {
                                                    $q->where('title', $word);
                                                });
                                        });
                                    });
                                })->orWhere('family_user_id', $keyword);
                            });
                    }
                })
                ->setRowClass(fn($helpForm) => is_null($helpForm->completed_at) ? 'pending-help-form' : '')
                ->addColumn('template', '{{ $template["title"] }}')
                ->addColumn('responsible_user_id', function (HelpForm $helpForm) {
                    $responsible_user_name = '';
                    if (isset($helpForm->responsible_user) && $helpForm->responsible_user_id != $helpForm->created_by_id)
                        $responsible_user_name = ($helpForm->created_by ? '<br>' : '') .
                            'الباحث: ' . $helpForm->responsible_user->full_name;
                    return ($helpForm->created_by ? $helpForm->created_by->full_name : null) . $responsible_user_name;
                })
                ->addColumn('user', fn($f) => $f->user->full_name)
                ->addColumn('view_url', fn($helpForm) => can('view', $helpForm) ? Route('admin.help-forms.show', $helpForm) : null)
                ->addColumn('pdf_url', fn($helpForm) => can('pdf', $helpForm) ? Route('admin.help-forms.pdf', [$helpForm, $helpForm->form_id]) : null)
                ->addColumn('delete_url', fn($helpForm) => can('delete', $helpForm) ? Route('admin.help-forms.destroy', $helpForm) : null)
                ->addColumn('update_url', fn($helpForm) => can('update', $helpForm) ? Route('admin.help-forms.edit', $helpForm) : null)
                ->addColumn('duplicate_url', fn($helpForm) => can('duplicate', $helpForm) ? Route('admin.help-forms.duplicate', $helpForm) : null)
                ->editColumn('created_at', fn($helpForm) => $helpForm->created_at ? $helpForm->created_at->timezone(app('timezone'))->toDayDateTimeString() : null)
                ->editColumn('completed_at', fn($helpForm) => $helpForm->completed_at ? $helpForm->completed_at->timezone(app('timezone'))->toDayDateTimeString() : null)
                ->editColumn('updated_at', fn($helpForm) => $helpForm->updated_at ? $helpForm->updated_at->timezone(app('timezone'))->toDayDateTimeString() : null)
                ->editColumn('deleted_at', fn($helpForm) => $helpForm->trashed() ? $helpForm->deleted_at->timezone(app('timezone'))->toDayDateTimeString() : null)
                ->rawColumns(['responsible_user_id'])
                ->addIndexColumn()->toJson();
        }
        return view('help-forms.index', [
            'templates' => HelpFormTemplate::whereHas('help_forms')->select(['id', 'title'])->get(),
            'years' => HelpForm::query()->whereNotNull('completed_at')->distinct()->pluck('hijri_year')
                ->map(fn($year) => ['id' => $year, 'title' => $year . ' هـ']),
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param HelpForm $helpForm
     *
     * @return Application|Factory|View
     */
    public function show(HelpForm $helpForm)
    {
        if (is_null($helpForm->completed_at) && !Auth::user()->hasRole('admin'))
            abort(404);
        $helpForm->load([
            'family_members' => fn($q) => $q->where('relation', '!=', FamilyRelationType::Child)->orWhere('is_active', true),
            'family_members.member',
            'responsible_user',
            'created_by',
            'template',
            'items' => fn($q) => $q->whereNotNull('submitted_at'),
            'items.template_section',
            'items.template_item',
            'old_comments',
        ]);
        return view('help-forms.view', compact('helpForm'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param HelpForm $helpForm
     *
     * @return Application|Factory|View
     */
    public function edit(HelpForm $helpForm)
    {
        return view('help-forms.edit', compact('helpForm'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param HelpForm $helpForm
     *
     * @return RedirectResponse|Response
     */
    public function updateStatus(Request $request, HelpForm $helpForm)
    {
        $request->validate([
            'reason' => ['required', 'string', 'min:4'],
            'helping_value' => ['nullable', 'required_if:status,' . CaseStatus::Approved, 'numeric', 'min:1'],
            'status' => ['required', Rule::in([CaseStatus::Approved, CaseStatus::Declined])],
        ], [], [
            'reason' => 'السبب',
            'helping_value' => trans('helpFormColumns.helping_value'),
        ]);
        $helpForm->reasons()->create([
            'content' => $request->post('reason'),
            'status' => $request->post('status'),
            'user_id' => Auth::id(),
        ]);
        if ($request->post('status') === CaseStatus::Approved) {
            $helpForm->update([
                'status' => $request->post('status'),
                'helping_value' => $request->post('helping_value'),
                'closed_at' => now(),
            ]);
            /*$user = $helpForm->responsible_user ?: $helpForm->created_by;
            $user->notify(new HelpFormApprovedNotification($helpForm));*/
        } else {
            $helpForm->update([
                'status' => $request->post('status'),
            ]);
            /*$user = $helpForm->responsible_user ?: $helpForm->created_by;
            $user->notify(new HelpFormDeclinedNotification($helpForm));*/
        }
        return redirect()->route('admin.help-forms.show', $helpForm)->with('message', 'تم تحديث ملف المستفيد بنجاح');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('help-forms.create');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param HelpForm $helpForm
     *
     * @return JsonResponse
     */
    public function destroy(Request $request, HelpForm $helpForm)
    {
        if (is_null($helpForm->completed_at))
            $helpForm->forceDelete();
        else
            $helpForm->delete();
        return $this->apiSuccess('تم حذف الملف بنجاح');
    }


    /**
     * Restore the specified trashed resource to storage.
     *
     * @param Request $request
     * @param HelpForm $helpForm
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function restore(Request $request, HelpForm $helpForm)
    {
        $helpForm->restore();
        $helpForm->audits()->save(new ModelAudit([
            'action' => 'restore',
            'user_id' => Auth::id(),
        ]));
        return $this->apiSuccess('تم استعادة الملف بنجاح');
    }

    /**
     * @param Request $request
     * @param HelpForm $helpForm
     * @param HelpFormPdfUtil $pdfUtil
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function pdf(Request $request, HelpForm $helpForm, HelpFormPdfUtil $pdfUtil)
    {
        $pdf = $pdfUtil->generatePdf($helpForm);
        return match (get_class($pdf)) {
            \niklasravnsborg\LaravelPdf\Pdf::class => $pdf->stream('helpForm-' . $helpForm->form_id . '.pdf'),
            \Webklex\PDFMerger\PDFMerger::class => $pdf->stream(),
        };
    }

    /**
     * @param HelpForm $helpForm
     *
     * @return RedirectResponse
     * @throws Throwable
     */
    public function duplicate(HelpForm $helpForm)
    {
        try {
            if (HelpForm::query()->whereNull('completed_at')->where([
                'user_id' => $helpForm->user_id,
                'created_by_id' => Auth::id(),
                'help_form_template_id' => $helpForm->help_form_template_id,
            ])->exists())
                return redirect()->route('admin.help-forms.index')->with('error', 'يوجد نموذج مفتوح لنفس المستفيد !');
            $limit = help_form_limit_allowed($helpForm->template, $helpForm->user_id);
            $overAllowedSupport = help_form_supporting_value_limit_allowed($helpForm->template, $helpForm->user_id);
            if ($overAllowedSupport) {
                return redirect()->route('admin.help-forms.index')->with('error', 'المستفيد تخطى الدعم المسموح .');
            } else if ($limit) {
                return redirect()->route('admin.help-forms.index')
                    ->with('error', sprintf('المستفيد تخطى عدد نماذج "%s" المسموح بها.', $helpForm->template->title));
            }
            DB::beginTransaction();
            $duplicatedHelpForm = HelpForm::create(array_merge($helpForm->only([
                'help_form_template_id', 'user_id', //'created_by_id', 'responsible_user_id',
                'gender', 'marital_status',
                'data', 'max_income',
                'is_employee', 'salary',
                'has_social_support', 'social_support',
                'has_citizen_account', 'citizen_account',
                'has_spouses_salary', 'spouses_salary',
                'expenses',
                'address', 'notes',
                'residence', 'residence_type', 'residence_status', 'residence_size', 'residence_rent_cost', 'residence_services',
                'dowry',
                'spouses_count',
                'underage_children_count',
                'adult_children_count',
                'children_count',
                'required_helping_value',
            ]), [
                'name' => $helpForm->user->name,
                'age' => age($helpForm->user->dob),
                'created_by_id' => Auth::id(),
            ]));
            $duplicatedHelpForm->family_members()->saveMany(
                $helpForm->family_members
                    ->map(fn($member) => new HelpFormFamilyMember($member->only([
                        'help_form_template_id', 'member_id', 'is_active',
                        'first_name', 'name', 'dob', 'age', 'health_status', 'educational_status', 'gender', 'relation',
                    ])))
            );
            $duplicatedHelpForm->invoices()->saveMany(
                $helpForm->invoices
                    ->map(fn($invoice) => new HelpFormInvoice($invoice->only(['type', 'no', 'value', 'due_at'])))
            );
            $duplicatedHelpForm->items()->saveMany(
                $helpForm->items
                    ->map(fn($item) => new HelpFormItem($item->only([
                        'help_form_template_item_id',
                        'type', 'submitted_at', 'data',
                    ])))
            );
            DB::commit();
            return redirect()->route('admin.help-forms.edit', ['help_form' => $duplicatedHelpForm]);
        } catch (Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            return redirect()->route('admin.help-forms.index')->with('error', 'خطأ أثناء تكرار النموذج !');
        }
    }
}
