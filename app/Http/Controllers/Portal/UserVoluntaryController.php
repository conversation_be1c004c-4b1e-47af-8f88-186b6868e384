<?php

namespace App\Http\Controllers\Portal;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\UserVoluntary;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\ViewErrorBag;
use Illuminate\Contracts\View\Factory;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use App\Notifications\UserVoluntaryNotification;
use Illuminate\Contracts\Foundation\Application;

class UserVoluntaryController extends \App\Http\Controllers\BaseController
{
    /**
     * FamilyCaseController constructor.
     */
    public function __construct()
    {
        $this->authorizeResource(UserVoluntary::class);
        $this->middleware('can:restore,trashedUserVoluntary')->only('restore');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return Application|Factory|View|Response
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax() || $request->has('draw')) {
            $query = UserVoluntary::query();
            /*if (user()->hasPermissionTo(VoluntaryPermissions::restore))
                $query = $query->withTrashed();*/
            return DataTables::eloquent($query)
                ->addColumn('update_url', function (UserVoluntary $userVoluntary) {
                    return can('update', $userVoluntary) ? route('admin.user-voluntaries.edit', $userVoluntary) : null;
                })
                ->addColumn('view_url', function (UserVoluntary $userVoluntary) {
                    return canAny('view', $userVoluntary) ? route('admin.user-voluntaries.show', $userVoluntary) : null;
                })
                ->addColumn('delete_url', function (UserVoluntary $userVoluntary) {
                    return can('delete', $userVoluntary) ? route('admin.user-voluntaries.destroy', $userVoluntary) : null;
                })
                ->addColumn('deleted_at', function (UserVoluntary $userVoluntary) {
                    return $userVoluntary->trashed() ? $userVoluntary->deleted_at->timezone(app('timezone'))->toDayDateTimeString() : null;
                })
                ->addColumn('user_name', function (UserVoluntary $userVoluntary) {
                    return optional($userVoluntary->user)->getFullName(2, true);
                })
                ->addColumn('created_by_name', function (UserVoluntary $userVoluntary) {
                    return optional($userVoluntary->created_by)->getFullName(2, true);
                })
                ->addColumn('created_at', function (UserVoluntary $userVoluntary) {
                    return $userVoluntary->created_at ? $userVoluntary->created_at->timezone(app('timezone'))->toDayDateTimeString() : null;
                })
                ->addColumn('updated_at', function (UserVoluntary $userVoluntary) {
                    return $userVoluntary->updated_at ? $userVoluntary->updated_at->timezone(app('timezone'))->toDayDateTimeString() : null;
                })
                ->addColumn('deleted_at', function (UserVoluntary $userVoluntary) {
                    return $userVoluntary->trashed() ? $userVoluntary->deleted_at->timezone(app('timezone'))->toDayDateTimeString() : null;
                })
                ->only([
                    'id', 'caption', 'date', 'total', 'user_name', 'created_by_name',
                    'created_at', 'update_url', 'delete_url', 'updated_at', 'deleted_at',
                ])
                ->rawColumns(['type'])->make(true);
        }
        return view('voluntaries.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('voluntaries.createUpdate');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), ...$this->rules($request));
        if ($validator->fails())
            return view('voluntaries.createUpdate', [
                'errors' => (new ViewErrorBag())->put('default', $validator->getMessageBag()),
            ]);
        $userVoluntary = UserVoluntary::query()->create(array_merge([
            'created_by_id' => \Auth::id(),
        ], $validator->validated()));
        $userVoluntary->user->notify(new UserVoluntaryNotification($userVoluntary));
        $request->request->replace([]);
        return view('voluntaries.createUpdate', [
            'message' => 'تم إضافة ساعات التطوع بنجاح .',
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param UserVoluntary $userVoluntary
     *
     * @return Response
     */
    public function show(UserVoluntary $userVoluntary)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UserVoluntary $userVoluntary
     *
     * @return Application|Factory|View
     */
    public function edit(UserVoluntary $userVoluntary)
    {
        return view('voluntaries.createUpdate', compact('userVoluntary'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param UserVoluntary $userVoluntary
     *
     * @return Application|Factory|View
     */
    public function update(Request $request, UserVoluntary $userVoluntary)
    {
        $validator = Validator::make($request->all(), ...$this->rules($request, $userVoluntary));
        if ($validator->fails())
            return view('voluntaries.createUpdate', [
                'userVoluntary' => $userVoluntary,
                'errors' => (new ViewErrorBag())->put('default', $validator->getMessageBag()),
            ]);
        $userVoluntary->update($validator->validated());
        $request->request->replace([]);
        return view('voluntaries.createUpdate', [
            'userVoluntary' => $userVoluntary,
            'message' => 'تم تعديل ساعات التطوع بنجاح .',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param UserVoluntary $userVoluntary
     *
     * @return JsonResponse
     */
    public function destroy(UserVoluntary $userVoluntary)
    {
        $userVoluntary->delete();
        return $this->apiSuccess('تم حذف ساعات التطوع بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param UserVoluntary $trashedUserVoluntary
     *
     * @return JsonResponse
     */
    public function restore(UserVoluntary $trashedUserVoluntary)
    {
        $trashedUserVoluntary->restore();
        return $this->apiSuccess('تم استعادة ساعات التطوع بنجاح');
    }

    /**
     * Return rules for creating/updating requests.
     *
     * @param Request $request
     * @param ?UserVoluntary $userVoluntary
     *
     * @return array
     * @noinspection PhpUnusedParameterInspection
     */
    public function rules(Request $request, UserVoluntary $userVoluntary = null): array
    {
        return [[
                    'caption' => ['required', 'string'],
                    'total' => ['required', 'numeric', 'min:1'],
                    'date' => ['required', 'date'],
                    'user_id' => Rule::when(is_null($userVoluntary), [
                        'required', Rule::exists(User::class, 'id'),
                    ], [
                        'nullable',
                    ]),
                ], [

                ], Lang::get('voluntaryColumns') ?? []];
    }
}
