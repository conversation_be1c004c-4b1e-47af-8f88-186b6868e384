<?php

namespace App\Http\Controllers\Portal;

use Exception;
use App\Enums\Gender;
use App\Models\FamilyTitle;
use Illuminate\Http\Request;
use App\Models\NonFamilyUser;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\ViewErrorBag;
use Illuminate\Contracts\View\Factory;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use Illuminate\Contracts\Foundation\Application;

class NonFamilyUserController extends \App\Http\Controllers\BaseController
{
    public function __construct()
    {
        $this->authorizeResource(NonFamilyUser::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|View|JsonResponse
     * @throws Exception
     */
    public function index()
    {
        if (request()->ajax() || request()->has('draw')) {
            return DataTables::eloquent(NonFamilyUser::query()->withCount(['wives', 'husbands'])->with([
                'family_title',
            ]))->filterColumn('name', function ($query, $keyword) {
                $words = array_values(array_filter(explode(' ', $keyword), function ($i) {
                    return !empty($i) && $i !== 'بن' && $i !== 'بنت';
                }));
                if (count($words) >= 1) {
                    $query->where(function ($query) use ($words) {
                        $query->whereSplit('name', $words[0]);
                        collect(array_slice($words, 1))->each(function ($word, $index) use (&$query, $words) {
                            $rel = trim(str_repeat('father.', $index + 1), '.');
                            $query->where(function ($query) use ($rel, $word, $words, $index) {
                                $query->whereHas($rel, function ($q) use ($word) {
                                    $q->whereSplit('name', $word);
                                });
                                if (count($words) === $index + 2)
                                    $query->orWhereHas('family_title', function ($q) use ($word) {
                                        $q->where('title', $word);
                                    });
                            });
                        });
                    });
                }
            })->filterColumn('gender', function ($query, $keyword) {
                if (!empty($keyword))
                    $query->where('gender', "$keyword");
            })->filterColumn('family_title.title', function ($query, $keyword) {
                if (!empty($keyword))
                    $query->where('family_title_id', "$keyword");
            })->editColumn('name', function (NonFamilyUser $nonFamilyUser) {
                return $nonFamilyUser->getName(3, true);
            })->addColumn('update_url', function (NonFamilyUser $nonFamilyUser) {
                return can('update', $nonFamilyUser) ? Route('admin.non-family-users.edit', $nonFamilyUser) : null;
            })->addColumn('delete_url', function (NonFamilyUser $nonFamilyUser) {
                return can('delete', $nonFamilyUser) ? Route('admin.non-family-users.destroy', $nonFamilyUser) : null;
            })->addColumn('updated_at', function (NonFamilyUser $nonFamilyUser) {
                return $nonFamilyUser->updated_at ? $nonFamilyUser->updated_at->timezone(app('timezone'))->toDayDateTimeString() : null;
            })->addColumn('spouses_count', function (NonFamilyUser $nonFamilyUser) {
                return $nonFamilyUser->wives_count + $nonFamilyUser->husbands_count;
            })->editColumn('family_title', function (NonFamilyUser $nonFamilyUser) {
                if ($nonFamilyUser->family_title)
                    return [
                        'id' => $nonFamilyUser->family_title->id,
                        'title' => $nonFamilyUser->family_title->title,
                    ];
                return null;
            })->addIndexColumn()->only([
                'id', 'gender', 'family_user_id', 'name', 'family_title', 'spouses_count', 'updated_at',
                'update_url', 'delete_url',
            ])->make(true);
        }
        return view('non-family-users.index', $this->getFormBinding());
    }

    /**
     * @return array
     */
    public function getFormBinding()
    {
        return [
            'families' => FamilyTitle::query()->select(['id', 'title'])->get(),
            'genders' => collect(Gender::all())->map(fn($g) => ['id' => $g, 'title' => trans("userColumns.gender.$g")]),
        ];
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     *
     * @return Application|Factory|View|Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'family_title_id' => [
                'required', Rule::exists(FamilyTitle::class, 'id'),
            ],
            'name' => [
                'required', 'max:191', 'min:3',
                'regex:/^([\x{0621}-\x{063A}\x{0641}-\x{064A} ]+)$/u',
            ],
            'gender' => [
                'required', Rule::in(Gender::all()),
            ],
            'dob' => ['nullable', 'date'],
            'dod' => ['nullable', 'date'],
            'health_status' => ['nullable', Rule::in(all_health_status())],
            'educational_status' => ['nullable', Rule::in(all_educational_status())],
        ], [], Lang::get('userColumns'));
        if ($validator->fails())
            return view('non-family-users.createUpdateForm', $this->getFormBinding())
                ->with('errors', (new ViewErrorBag())->put('default', $validator->getMessageBag()));
        NonFamilyUser::query()->create(array_merge($request->only(['family_title_id', 'gender', 'name', 'dob', 'dod', 'health_status', 'educational_status']), [
            'is_dead' => $request->has('is_user_dead') || !empty($request->post('dod')),
        ]));
        $request->request->replace([]);
        return view('non-family-users.createUpdateForm', array_merge($this->getFormBinding(), [
            'message' => 'تم إضافة المستخدم بنجاح .',
        ]));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        return view('non-family-users.createUpdateForm', $this->getFormBinding());
    }

    /**
     * Display the specified resource.
     *
     * @param NonFamilyUser $nonFamilyUser
     *
     * @return Response
     */
    public function show(NonFamilyUser $nonFamilyUser)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param NonFamilyUser $nonFamilyUser
     *
     * @return Application|Factory|View
     */
    public function edit(NonFamilyUser $nonFamilyUser)
    {
        return view('non-family-users.createUpdateForm', array_merge($this->getFormBinding(), [
            'nonFamilyUser' => $nonFamilyUser,
            'blockGender' => $nonFamilyUser->gender === Gender::Male ? $nonFamilyUser->wives()->count() > 0 : $nonFamilyUser->husbands()->count() > 0,
        ]));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param NonFamilyUser $nonFamilyUser
     *
     * @return Application|Factory|View
     */
    public function update(Request $request, NonFamilyUser $nonFamilyUser)
    {
        $validator = Validator::make($request->all(), [
            'family_title_id' => [
                'required', Rule::exists(FamilyTitle::class, 'id'),
            ],
            'name' => [
                'required', 'max:191', 'min:3',
                'regex:/^([\x{0621}-\x{063A}\x{0641}-\x{064A} ]+)$/u',
            ],
            'gender' => [
                'required', Rule::in(Gender::all()), function ($attribute, $val, $fail) use ($nonFamilyUser) {
                    if ($val !== $nonFamilyUser->gender && ($nonFamilyUser->gender === Gender::Male ? $nonFamilyUser->wives()->count() > 0 : $nonFamilyUser->husbands()->count() > 0))
                        $fail('لا يمكن تعديل جنس المستخدم فى حالة وجود أزواج/زوجات .');
                },
            ],
            'dob' => ['nullable', 'date'],
            'dod' => ['nullable', 'date'],
            'health_status' => ['nullable', Rule::in(all_health_status())],
            'educational_status' => ['nullable', Rule::in(all_educational_status())],
        ], [], Lang::get('userColumns'));
        if ($validator->fails())
            return view('non-family-users.createUpdateForm', array_merge($this->getFormBinding(), [
                'nonFamilyUser' => $nonFamilyUser,
                'blockGender' => $nonFamilyUser->gender === Gender::Male ? $nonFamilyUser->wives()->count() > 0 : $nonFamilyUser->husbands()->count() > 0,
            ]))->with('errors', (new ViewErrorBag())->put('default', $validator->getMessageBag()));
        $nonFamilyUser->update(array_merge($request->only(['family_title_id', 'gender', 'name', 'dob', 'dod', 'health_status', 'educational_status']), [
            'is_dead' => $request->has('is_user_dead') || !empty($request->post('dod')),
        ]));
        $request->request->replace([]);
        return view('non-family-users.createUpdateForm', array_merge($this->getFormBinding(), [
            'nonFamilyUser' => $nonFamilyUser,
            'blockGender' => $nonFamilyUser->gender === Gender::Male ? $nonFamilyUser->wives()->count() > 0 : $nonFamilyUser->husbands()->count() > 0,
            'message' => 'تم تعديل المستخدم بنجاح .',
        ]));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param NonFamilyUser $nonFamilyUser
     *
     * @return JsonResponse
     */
    public function destroy(NonFamilyUser $nonFamilyUser)
    {
        if ($nonFamilyUser->gender === Gender::Male ? $nonFamilyUser->wives()->count() > 0 : $nonFamilyUser->husbands()->count() > 0)
            return response()->json([
                'message' => 'لا يمكن حذف المستخدم فى حالة وجود أزواج/زوجات .',
            ], 400);
        $nonFamilyUser->forceDelete();
        return $this->apiSuccess('تم حذف المستخدم بنجاح .');
    }
}
