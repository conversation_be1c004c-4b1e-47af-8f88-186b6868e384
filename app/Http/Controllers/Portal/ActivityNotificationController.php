<?php

namespace App\Http\Controllers\Portal;

use App\Models\User;
use App\Models\Activity;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Lang;
use Illuminate\Contracts\View\Factory;
use App\Notifications\SMSNotification;
use Illuminate\Contracts\Foundation\Application;

class ActivityNotificationController extends \App\Http\Controllers\BaseController
{
    /**
     * Show the form for creating a new resource.
     *
     * @param Activity $activity
     *
     * @return Application|Factory|View
     */
    public function create(Activity $activity)
    {
        $activity->load(['members', 'members.role', 'members.user', 'members.user.father', 'members.user.mother']);
        return view('activity.notification.create', compact('activity'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @param Activity $activity
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request, Activity $activity)
    {
        $data = $request->validate([
            'notify_users' => ['required', 'array', 'min:1'],
            'notify_users.*' => ['required', 'exists:users,id'],
            'notify_txt' => ['required', 'string', 'min:4', 'max:1000'],
        ], [], Lang::get('activityColumns') ?? []);
        $data['notify_users'] = array_map('intval', $data['notify_users']);
        $members = User::query()->whereIn('id', $data['notify_users'])->with(['father', 'mother'])->get();
        if ($members->count() > 0) {
            $activity->notifications()->create([
                'user_ids' => $data['notify_users'],
                'content' => $data['notify_txt'],
            ]);
            $notify_txt = $request->post('notify_txt');
            $members->each(function (User $user) use ($notify_txt) {
                if (!is_null($user->phone))
                    $user->notify(new SMSNotification($notify_txt));
                else if (!is_null($user->father->phone))
                    $user->father->notify(new SMSNotification($notify_txt, $user));
                else if (!is_null($user->mother->phone))
                    $user->mother->notify(new SMSNotification($notify_txt, $user));
            });
            return redirect()->route('admin.activities.index')
                ->with('message', sprintf('تم إرسال الرسالة لـ %s أعضاء', $members->count()));
        }
        return redirect()->route('admin.activities.index')->with('error', 'fail');
    }
}
