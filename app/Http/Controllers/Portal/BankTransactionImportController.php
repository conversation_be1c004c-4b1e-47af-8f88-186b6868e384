<?php

namespace App\Http\Controllers\Portal;

use Storage;
use Exception;
use Carbon\Carbon;
use App\Models\BankAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Models\BankTransactionImport;
use Illuminate\Http\RedirectResponse;
use App\Models\BankAccountTransaction;
use Illuminate\Contracts\View\Factory;
use Yajra\DataTables\Facades\DataTables;
use App\Models\BankTransactionImportItem;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Illuminate\Contracts\Foundation\Application;
use App\Http\Requests\StoreBankTransactionImportRequest;
use App\Http\Requests\UpdateBankTransactionImportRequest;

class BankTransactionImportController extends \App\Http\Controllers\BaseController
{
    /**
     * BankTransactionImportController constructor.
     */
    public function __construct()
    {
        $this->authorizeResource(BankTransactionImport::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return Application|Factory|View|JsonResponse
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax() || $request->has('draw')) {
            return DataTables::eloquent(BankTransactionImport::query()->whereNotNull('completed_at')
                ->orWhere(function ($q) {
                    $q->where('user_id', \Auth::id())->whereNull('completed_at');
                })->with(['user'])->withCount(['items']))
                ->addIndexColumn()
                ->addColumn('user_name', fn($bTI) => optional($bTI->user)->full_name)
                ->addColumn('bank_account_title', fn($bTI) => optional($bTI->bank_account)->title)
                ->editColumn('created_at', fn($bTI) => $bTI->created_at ? $bTI->created_at->timezone(app("timezone"))->toDateTimeString() : null)
                ->editColumn('completed_at', fn($bTI) => $bTI->completed_at ? $bTI->completed_at->timezone(app("timezone"))->toDateTimeString() : null)
                ->editColumn('view_url', fn($bTI) => !is_null($bTI->completed_at) ? route('admin.bank-transaction-import.show', $bTI) : null)
                ->editColumn('update_url', fn($bTI) => is_null($bTI->completed_at) && $bTI->user_id === \Auth::id() ? route('admin.bank-transaction-import.edit', $bTI) : null)
                ->setRowClass(fn($bTI) => is_null($bTI->completed_at) ? 'pending-file' : '')
                ->only([
                    'uuid', 'items_count', 'user_name', 'bank_account_title', 'created_at', 'completed_at',
                    'view_url', 'update_url', 'delete_url',
                ])->make();
        }
        return view('bank-account.transaction-import.index');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreBankTransactionImportRequest $request
     *
     * @return RedirectResponse
     */
    public function store(StoreBankTransactionImportRequest $request)
    {
        $bankFile = $request->file('bank_file');
        if (BankTransactionImport::query()->where('file_hash', md5_file($bankFile->getRealPath()))->count() > 0)
            return back()->withErrors([
                'bank_file' => 'الملف مرفوع مسبقاً .',
            ]);
        $bankFileData = (new \App\Imports\BankTransactionImport())->toCollection($bankFile)->first();
        $otherData = $bankFileData->take(19)->values();
        $transactionData = $bankFileData->skip(19)->where(fn($d) => !empty($d[3]))->values();
        $debitTransactionData = $bankFileData->skip(19)->where(fn($d) => !empty($d[4]))->values();
        if ($transactionData->count() === 0)
            return back()->withErrors([
                'bank_file' => 'لا يوجد عمليات .',
            ]);
        try {
            \DB::beginTransaction();
            $path = $bankFile->store('excel-files');
            $bankTransactionImport = BankTransactionImport::query()->create([
                'bank_account_id' => $request->post('bank_account_id'),
                'user_id' => \Auth::id(),
                'file_name' => $bankFile->getClientOriginalName(),
                'file_path' => $path,
                'file_size' => $bankFile->getSize(),
                'file_hash' => md5_file(Storage::path($path)),
            ]);
            $transactionData->each(function ($item) use ($bankTransactionImport) {
                $date = (new Carbon(Date::excelToDateTimeObject($item[8])))
                    ->setTimeFrom(Date::excelToDateTimeObject($item[6]));
                $amount = str((string)$item[3])
                    ->replace('٫', '.')
                    ->replace(',', '')
                    ->replace('ر.س', '')
                    ->replace(' ', '');
                $hash = sha1(implode('_', [
                    trim($item[4]),
                    $date->format('Y-m-d|H:i:s'),
                    $amount->toString(),
                ]));
                //$date = Carbon::createFromFormat('d/m/Y H:i', "$item[8] $item[6]");
                $bankTransactionImport->items()->create([
                    'notes' => trim($item[4]),
                    'amount' => $amount->toFloat(),
                    'error' => BankTransactionImportItem::query()
                        ->whereHas('bank_transaction_import')
                        ->where(compact('hash'))->exists() ? 'عملية مكررة' : null,
                    'due_at' => $date,
                    'hash' => $hash,
                ]);
            });
            \DB::commit();
        } catch (\Exception $exception) {
            \DB::rollBack();
            return back()->withErrors([
                'bank_file' => 'الملف غير صالح .',
            ]);
        }

        return redirect()->route('admin.bank-transaction-import.edit', $bankTransactionImport);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('bank-account.transaction-import.create', [
            'bankAccounts' => BankAccount::all(),
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param BankTransactionImport $bankTransactionImport
     *
     * @return Application|Factory|View
     */
    public function show(BankTransactionImport $bankTransactionImport)
    {
        return view('bank-account.transaction-import.view', compact('bankTransactionImport'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param BankTransactionImport $bankTransactionImport
     *
     * @return Application|Factory|View
     */
    public function edit(BankTransactionImport $bankTransactionImport)
    {
        return view('bank-account.transaction-import.edit', compact('bankTransactionImport'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param BankTransactionImport $bankTransactionImport
     *
     * @return RedirectResponse
     */
    public function actions(Request $request, BankTransactionImport $bankTransactionImport)
    {
        if (!is_null($bankTransactionImport->completed_at))
            abort(400);
        if ($request->get('action') === 'confirm') {
            try {
                \DB::beginTransaction();
                $items = $bankTransactionImport->items()->whereNotNull('supporter_id')->get();
                foreach ($items as $item) {
                    $tr = BankAccountTransaction::create([
                        'bank_account_id' => $bankTransactionImport->bank_account_id,
                        'amount' => $item->amount,
                        'due_at' => $item->due_at,
                        'reference' => $item->hash,
                        'supporter_id' => $item->supporter_id,
                    ]);
                    $item->transaction()->associate($tr)->save();
                }
                $bankTransactionImport->update([
                    'completed_at' => now(),
                ]);
                \DB::commit();
                return redirect()->route('admin.bank-transaction-import.index')->with('message', 'تم تأكيد بيانات الملف بنجاح');
            } catch (\Exception $exception) {
                \DB::rollBack();
                return redirect()->route('admin.bank-transaction-import.edit', $bankTransactionImport)->with('message', 'خطأ غير متوقع');
            }
        } else if ($request->get('action') === 'cancel') {
            $bankTransactionImport->delete();
            return redirect()->route('admin.bank-transaction-import.index')->with('message', 'تم حذف بيانات الملف بنجاح');
        }
        return redirect()->route('admin.bank-transaction-import.index');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateBankTransactionImportRequest $request
     * @param BankTransactionImport $bankTransactionImport
     *
     * @return RedirectResponse
     */
    public function update(UpdateBankTransactionImportRequest $request, BankTransactionImport $bankTransactionImport)
    {
        foreach (array_filter($request->post('items', []), fn($i) => !empty($i['supporter_id'])) as $item) {
            BankTransactionImportItem::query()
                ->whereNull('supporter_id')
                ->where('id', $item['id'])->update(['supporter_id' => $item['supporter_id']]);
        }
        return redirect()->route('admin.bank-transaction-import.edit', $bankTransactionImport);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param BankTransactionImport $bankTransactionImport
     *
     * @return Response
     */
    public function destroy(BankTransactionImport $bankTransactionImport)
    {
        //
    }
}
