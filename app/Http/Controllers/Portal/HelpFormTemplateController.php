<?php

namespace App\Http\Controllers\Portal;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\HelpFormTemplate;
use Yajra\DataTables\DataTables;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\Foundation\Application;

class HelpFormTemplateController extends \App\Http\Controllers\BaseController
{
    public function __construct()
    {
        $this->authorizeResource(HelpFormTemplate::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|View|JsonResponse
     * @throws Exception
     */
    public function index()
    {
        if (request()->ajax() || request()->has('draw'))
            return DataTables::of(HelpFormTemplate::query())
                //->editColumn('deleted_at', fn($helpForm) => $helpForm->trashed() ? $helpForm->deleted_at->timezone(app('timezone'))->toDayDateTimeString() : null)
                ->addIndexColumn()
                ->editColumn('active', function (HelpFormTemplate $template) {
                    return $template->active ?
                        '<span class="badge badge-pill badge-light-success w-25">' . 'نعم' . '</span>' :
                        '<span class="badge badge-pill badge-light-danger w-25">' . 'لا' . '</span>';
                })
                ->addColumn('update_url', fn($template) => can('update', $template) ? Route('admin.help-form-templates.edit', $template) : null)
                ->rawColumns(['active'])
                ->only(['id', 'title', 'active', 'update_url'])
                ->toJson();
        return view('help-form-templates.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('help-form-templates.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param HelpFormTemplate $helpFormTemplate
     *
     * @return Response
     */
    public function show(HelpFormTemplate $helpFormTemplate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param HelpFormTemplate $helpFormTemplate
     *
     * @return Application|Factory|View
     */
    public function edit(HelpFormTemplate $helpFormTemplate)
    {
        return view('help-form-templates.edit', compact('helpFormTemplate'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param HelpFormTemplate $helpFormTemplate
     *
     * @return Application|Factory|View
     */
    public function update(Request $request, HelpFormTemplate $helpFormTemplate)
    {
        $helpFormTemplate->update([
            'active' => $request->has('active'),
        ]);
        return view('help-form-templates.edit', compact('helpFormTemplate'))
            ->with('message', 'تم التعديل  بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param HelpFormTemplate $helpFormTemplate
     *
     * @return Response
     */
    public function destroy(HelpFormTemplate $helpFormTemplate)
    {
        //
    }
}
