<?php

namespace App\Http\Controllers\Portal;

use Exception;
use App\Models\User;
use App\Models\Group;
use App\Models\GroupReport;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Http\RedirectResponse;
use App\Notifications\SMSNotification;
use Illuminate\Contracts\View\Factory;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Contracts\Foundation\Application;

class GroupController extends \App\Http\Controllers\BaseController
{
    /**
     * FamilyCaseController constructor.
     */
    public function __construct()
    {
        $this->authorizeResource(Group::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return Application|Factory|JsonResponse|View
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax() || $request->expectsJson() || $request->has('draw'))
            return DataTables::eloquent(
                Group::withTrashed()->withCount(['users'])->orderByDesc('updated_at')
            )->addColumn('update_url', function (Group $group) {
                return can('update', $group) ? Route('admin.groups.edit', $group) : null;
            })->addColumn('view_url', function (Group $group) {
                return can('view', $group) ? Route('admin.groups.show', $group) : null;
            })->addColumn('created_at', function (Group $group) {
                return $group->created_at ? $group->created_at->timezone(app('timezone'))->toDayDateTimeString() : null;
            })->addColumn('updated_at', function (Group $group) {
                return $group->updated_at ? $group->updated_at->timezone(app('timezone'))->toDayDateTimeString() : null;
            })->addColumn('deleted_at', function (Group $group) {
                return $group->trashed() ? $group->deleted_at->timezone(app('timezone'))->toDayDateTimeString() : null;
            })->addColumn('user_name', function (Group $group) {
                return $group->user ? $group->user->name : null;
            })->only([
                'id', 'name', 'user_name', 'users_count',
                'update_url', 'view_url', 'created_at', 'updated_at', 'deleted_at',
            ])->make(true);
        else
            return view('group.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('group.createUpdate');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate(...$this->rules($request));
        $group = new Group(array_merge($request->only(['name', 'notes']), [
            'user_id' => Auth::id(),
        ]));
        $group->save();
        $group->users()->createMany(collect(array_values($request->post('users')))->map(fn($user, $k) => [
            'user_id' => $user['user_id'],
            'role' => $user['role'],
            'index' => $k,
        ]));

        return redirect()->route('admin.groups.show', $group)
            ->with('message', 'تم إضافة لجنة ' . "\"{$group->name}\"" . ' بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param Group $group
     *
     * @return Application|Factory|View
     */
    public function show(Request $request, Group $group)
    {
        $locationData = GroupReport::query()->distinct()->pluck('location')
            ->prepend('مقر الصندوق')->unique()->filter();
        if (!empty($request->old('location')) && $locationData->contains(!$request->old('location')))
            $locationData->prepend($request->old('location'));

        return view('group.view', compact('group', 'locationData'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param Group $group
     *
     * @return Application|Factory|View|Response
     */
    public function edit(Group $group)
    {
        return view('group.createUpdate', compact('group'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param Group $group
     *
     * @return RedirectResponse|Response
     */
    public function update(Request $request, Group $group)
    {
        $request->validate(...$this->rules($request));
        $group->update($request->only(['name', 'notes']));
        $group->users()->delete();
        $group->users()->createMany(collect(array_values($request->post('users', [])))->map(fn($user, $k) => [
            'user_id' => $user['user_id'],
            'role' => $user['role'],
            'index' => $k,
        ]));

        return redirect()->route('admin.groups.show', $group)
            ->with('message', 'تم تعديل اللجنة ' . "\"{$group->name}\"" . ' بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Group $group
     *
     * @return Response
     */
    public function destroy(Group $group)
    {
        //
    }

    /**
     * @param Request $request
     * @param Group $group
     *
     * @return JsonResponse|RedirectResponse|Response
     */
    public function notify(Request $request, Group $group)
    {
        $request->validate([
            'notify_users' => ['required', 'array', 'min:1'],
            'notify_users.*' => ['required', 'exists:users,id', Rule::in($group->users()->pluck('user_id') ?? [])],
            'notify_txt' => ['required', 'string', 'min:4', 'max:1000'],
        ], [], Lang::get('groupColumns') ?? []);

        $users = User::query()->whereIn('id', $request->post('notify_users'))
            ->whereNotNull('phone')->get();

        if ($users->count() > 0)
            $users->each(fn(User $user) => $user->notify(new SMSNotification($request->post('notify_txt'))));

        return redirect()->route('admin.groups.show', $group)
            ->with('message', 'تم تسجيل الرسالة بنجاح .');
    }

    /**
     * Return rules for creating/updating requests.
     *
     * @param Request $request
     * @param ?Group $group
     *
     * @return array
     */
    public function rules(Request $request, Group $group = null)
    {
        return [[
                    'name' => ['required', 'string', 'min:4', 'max:240'],
                    'notes' => ['required', 'string', 'min:4', 'max:1000'],

                    'users' => ['nullable', 'array'],
                    'users.*.user_id' => [
                        'exists:users,id', 'required_with:users.*.role',
                    ],
                    'users.*.role' => [
                        'required_with:users.*.user_id',
                        Rule::in([
                            'ADMIN', 'VICE_ADMIN', 'MODERATOR', 'USER',
                        ]),
                    ],

                    /*'users_id' => ['required', 'array'],
                    'users_id.*' => ['required', 'exists:users,id'],*/
                ], [], array_merge(Lang::get('groupColumns') ?? [], [
            'users.*.user_id' => 'العضو',
            'users.*.role' => 'الصفة',
        ])];
    }
}
