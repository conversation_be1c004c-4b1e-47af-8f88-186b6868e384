<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\BankTransactionImportTemplateExport;

class TemplateDownloadController extends Controller
{
    /**
     * Download bank transaction import template
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function bankImportTemplate()
    {
        return Excel::download(
            new BankTransactionImportTemplateExport(),
            'bank-transaction-import-template.xlsx'
        );
    }
}
