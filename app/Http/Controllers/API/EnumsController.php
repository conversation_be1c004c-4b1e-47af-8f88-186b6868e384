<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;

class EnumsController extends Controller
{
    /**
     * @OA\Get(
     *  path="/enums/consultation-types",
     *  summary="list all consultation-types enums",
     *  tags={"enums"},
     *  @OA\Response(
     *    response="default",
     *    description="an ""unexpected"" error"
     *  )
     * )
     */
    public function ConsultationTypes()
    {
        $data = [];
        foreach(\App\Enums\ConsultationType::all() as $type) {
            $data[] = [
                'en' => $type,
                'ar' => __('consultations.sub.type.' . $type)
            ];
        }
        return $this->apiSuccess('Enums: consultation-types', $data);
    }
}
