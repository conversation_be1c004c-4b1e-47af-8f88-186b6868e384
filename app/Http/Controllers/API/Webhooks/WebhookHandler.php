<?php

namespace App\Http\Controllers\API\Webhooks;

use Storage;
use Exception;
use Throwable;
use Carbon\Carbon;
use App\Models\User;
use App\Models\TallyForm;
use App\Models\StravaUser;
use Illuminate\Http\Request;
use App\Jobs\TaqnyatWebhookJob;
use App\Jobs\UpdatePollOptions;
use App\Jobs\WahaAckWebhookJob;
use App\Broadcasting\WahaChannel;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\BulkNotificationItem;
use Illuminate\Support\Facades\Redis;
use App\Jobs\StravaActivitiesAnalyze;
use App\Models\BroadcastChannelSender;
use App\Notifications\SMSNotification;
use App\Http\Integrations\Strava\StravaConnector;
use App\Http\Integrations\Strava\Requests\ActivityRequest;

class WebhookHandler extends Controller
{
    function taqnyat(Request $request)
    {
        foreach (($request->post('results') ?: []) as $result) {
            if (isset($result['msgId']) && isset($result['to']) && isset($result['status']))
                TaqnyatWebhookJob::dispatch($result)->onQueue('low');
            else {
                $fileName = time() . '-' . uniqid();
                $filePath = "webhook/taqnyat/{$fileName}.json";
                try {
                    if (!empty($request->all()))
                        Storage::put($filePath, json_encode($request->all()));
                } catch (\Exception $exception) {
                    \Log::error($exception);
                    $rawFilePath = "webhook/taqnyat/{$fileName}.txt";
                    if (!empty($request->getContent()))
                        Storage::put($rawFilePath, $request->getContent());
                }
            }
        }
        return 'done';
    }

    function campaign(Request $request)
    {
        abort_if($request->key !== 'ABCDEFGH', 403);

        $request->validate([
            'phone' => 'required',
        ]);
        $phone = phone_format($request->input('phone'), ['SA', 'AUTO'], false);
        if (!empty($phone)) {
            $user = \App\Models\User::where('phone', $phone)->first();
            if ($user) {
                // Check when the last SMS was sent globally (across all users)
                $globalLastSentKey = 'global_last_sms_sent_at';
                $lastSentAt = Redis::get($globalLastSentKey);
                $now = now();
                $minimumGap = 20; // 20 seconds between each SMS system-wide

                $user->data->campaign_response_at = now();
                $user->save();

                // Calculate the proper delay time
                if ($lastSentAt) {
                    $lastSentTime = Carbon::parse($lastSentAt);
                    $secondsSinceLastSent = $now->diffInSeconds($lastSentTime);

                    if ($secondsSinceLastSent < $minimumGap) {
                        // Need to wait more time
                        $delaySeconds = $minimumGap - $secondsSinceLastSent;
                        $delay = $now->copy()->addSeconds($delaySeconds);

                        // Update Redis with when this message will be sent
                        Redis::set($globalLastSentKey, $delay->toDateTimeString());
                    } else {
                        // Enough time has passed, can send now
                        $delay = $now;
                        Redis::set($globalLastSentKey, $now->toDateTimeString());
                    }
                } else {
                    // First message, no delay needed
                    $delay = $now;
                    Redis::set($globalLastSentKey, $now->toDateTimeString());
                }

                $message = "السلام عليكم ورحمة الله وبركاته،\n"
                    . "شكرًا لتواصلك معنا في صندوق عائلة التويجري.\n"
                    . "\n"
                    . "للاشتراك في عضوية \"منتمي\" ودعم مشروع وقف العائلة المبارك، يُرجى زيارة الرابط التالي:\n"
                    . "https://altuwaijri.sa/members\n"
                    . "\n"
                    . "نسأل الله أن يجعل هذا العمل في ميزان حسناتكم، وأن يبارك في جهودكم.\n"
                    . "ونحن على استعداد للإجابة عن أي استفسارات لديك.\n"
                    . "\n"
                    . "وكل عام وأنتم بخير، وعيدكم مبارك.";
                if ($user)
                    $user->notify(
                        (new \App\Notifications\SMSNotification(
                            $message,
                            sender: \App\Models\BroadcastChannelSender::find(5),
                            replay: true,
                        ))
                            ->onQueue('low')
                            ->delay($delay)
                    );
            }
        }
    }

    function waha(Request $request)
    {
        try {
            if (!Storage::exists('webhook/waha'))
                Storage::createDirectory('webhook/waha');
            $time = time();
            $filePath = "webhook/waha/{$time}.json";
            $data = $request->all() ?: [];
            Storage::put($filePath, json_encode($data));
            $data = $request->all();
            switch ($data['event']) {
                case 'session.status':
                    $payload = $data['payload'];
                    $payload['status'] = strtoupper($payload['status']);
                    try {
                        $sender = BroadcastChannelSender::where('sender_name', $data['session'])
                            ->whereRelation('channel', 'class', WahaChannel::class)
                            ->first();
                        if ($sender) {
                            $sender->update([
                                'metadata->me' => $data['me'],
                                'metadata->status' => $payload['status'],
                            ]);
                            /*switch ($payload['status']) {
                                case 'WORKING':
                                case 'STARTING':
                                case 'STOPPED':
                            }*/
                        }
                    } catch (\Exception $exception) {
                        Log::error($exception, $payload);
                    }
                    break;
                case 'poll.vote':
                    $payload = $data['payload'];
                    try {
                        $vote = $payload['vote'];
                        $poll = $payload['poll'];
                        $messageId = (string)\Arr::last(explode('_', $poll['id']));
                        $options = $vote['selectedOptions'];
                        $item = BulkNotificationItem::query()
                            ->where('poll_message_id', $messageId)
                            ->first();
                        if ($item) {
                            if (empty($item->notification->poll_expire_at) ||
                                $item->notification->poll_expire_at->endOfDay()->isFuture()) {
                                UpdatePollOptions::dispatch($item, $options)->onQueue('low');
                                $message = empty($options) ? 'تم حذف إجابتكم، بإنتظار المشاركة مرة أخرى' : 'تم تسجيل مشاركتك بنجاح';
                            } else {
                                $message = 'عفوا انتهى وقت المشاركة';
                                if (is_array($item->poll_options) && !empty($item->poll_options))
                                    $message = $message . "\n" .
                                        'مشاركتك الأخيرة: ' . implode('، ', $item->poll_options);
                            }
                            $item->user->notify(new SMSNotification(
                                $message,
                                item: $item,
                                sender: $item->notification->sender,
                                replay: true,
                            ));
                            $at = now()->format('Y-m-d H:i:s');
                            $item->update([
                                'replies' => match ($item->getQuery()->driver()) {
                                    'mariadb', 'mysql' => \DB::raw("CONCAT(IFNULL(`replies`, ''), '$at: $message', '\n')"),
                                    'pgsql' => \DB::raw("CONCAT(COALESCE(replies, ''), '$at: $message', E'\\n')"),
                                },
                            ]);
                        }
                    } catch (\Exception $exception) {
                        Log::error($exception, $payload);
                    }
                    break;
                case 'message.ack':
                    $payload = $data['payload'];
                    WahaAckWebhookJob::dispatch($payload)->onQueue('low');
                    break;
            }
        } catch (\Exception $exception) {
            Log::error($exception);
        }
        return 'done';
    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    function tally(Request $request)
    {
        $time = time();
        $filePath = "webhook/tally/{$time}.json";
        $data = $request->all() ?: [];
        Storage::put($filePath, json_encode($data));
        switch (@$data['eventType']) {
            case 'FORM_RESPONSE':
                $tallyForm = TallyForm::query()
                    ->firstOrCreate([
                        'form_id' => $data['data']['formId'],
                    ], [
                        'form_name' => $data['data']['formName'],
                    ]);
                $userId = null;
                try {
                    $userHash = collect($data['data']['fields'])
                                    ->where('label', 'hashedUserId')
                                    ->first()['value'];
                    if (!empty($userHash))
                        $userId = User::find(\Crypt::decryptString($userHash))->id;
                } catch (\Exception $exception) {
                    \Log::error($exception);
                }
                $tallyForm->submissions()->create([
                    'fields' => $data['data']['fields'],
                    'user_id' => $userId,
                ]);
                $tallyForm->update([
                    'last_submission' => now(),
                ]);
                break;
        }
        return response()->json(['success' => true]);
    }

    function strava_verify(Request $request)
    {
        $hubChallenge = $request->get('hub_challenge');
        if (!empty($hubChallenge))
            return response()->json([
                'hub.challenge' => $hubChallenge,
            ]);
        return response()->json([]);
    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    function strava(Request $request)
    {
        $time = time();
        $filePath = "webhook/strava/{$time}.json";
        $data = $request->all();
        Storage::put($filePath, json_encode($data));
        if (@$data['object_type'] === 'activity') {
            if (@$data['aspect_type'] === 'create') {
                $stravaUser = StravaUser::where('athlete_id', $data['owner_id'])->first();
                if ($stravaUser) {
                    $connector = new StravaConnector();
                    $stravaUser->getAuthenticator($connector);
                    $request = new ActivityRequest($data['object_id']);
                    $res = $connector->send($request)->json();
                    if (!$stravaUser->activities()->where('strava_id', $res['id'])->exists()) {
                        $activity = $stravaUser->activities()->save($stravaUser->responseToModel($res));
                        if ($activity->start_latlng)
                            \App\Jobs\StravaActivityLocation::dispatch($activity)->onQueue('low');
                        if ($activity->map)
                            \App\Jobs\StravaActivityMap::dispatch($activity)->onQueue('low');
                        StravaActivitiesAnalyze::dispatch($stravaUser)->onQueue('default');
                    }
                }
            }
        }
        return response()->json(['success' => true]);
    }
}
