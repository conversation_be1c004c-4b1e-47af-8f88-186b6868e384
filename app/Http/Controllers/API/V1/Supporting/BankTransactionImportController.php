<?php

namespace App\Http\Controllers\API\V1\Supporting;

use Storage;
use Exception;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Helpers\SmartDateParser;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Http\Controllers\Controller;
use App\Models\BankTransactionImport;
use App\Models\BankAccountTransaction;
use Illuminate\Contracts\View\Factory;
use Yajra\DataTables\Facades\DataTables;
use App\Models\BankTransactionImportItem;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Container\ContainerExceptionInterface;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Routing\Controllers\HasMiddleware;
use App\Http\Requests\StoreBankTransactionImportRequest;
use App\Http\Requests\UpdateBankTransactionImportRequest;
use App\Http\Resources\API\BankTransactionImportResource;
use App\Services\BankTransactionImportService;
use App\Services\BankTransactionConfirmationService;

class BankTransactionImportController extends Controller implements HasMiddleware
{
    protected BankTransactionImportService $importService;
    protected BankTransactionConfirmationService $confirmationService;

    public function __construct(
        BankTransactionImportService $importService,
        BankTransactionConfirmationService $confirmationService
    ) {
        $this->importService = $importService;
        $this->confirmationService = $confirmationService;
    }

    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(BankTransactionImport::class),
        ];
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return Application|Factory|View|JsonResponse
     * @throws Exception
     */
    public function index(Request $request)
    {
        return DataTables::eloquent(
            BankTransactionImport::query()
                ->whereNotNull('completed_at')
                ->orWhere(function ($q) {
                    $q->where('user_id', \Auth::id())->whereNull('completed_at');
                })->with(['user'])->withCount(['items'])->withSum('items', 'amount')
                ->latest('created_at')
        )
            ->addIndexColumn()
            ->addColumn('user_name', fn($bTI) => optional($bTI->user)->full_name)
            ->addColumn('bank_account_title', fn($bTI) => optional($bTI->bank_account)->title)
            ->editColumn('created_at', fn($bTI) => $bTI->created_at ? $bTI->created_at->timezone(app("timezone"))->toDateTimeString() : null)
            ->editColumn('completed_at', fn($bTI) => $bTI->completed_at ? $bTI->completed_at->timezone(app("timezone"))->toDateTimeString() : null)
            ->setRowClass(fn($bTI) => is_null($bTI->completed_at) ? 'pending-file' : '')
            ->only([
                'uuid', 'items_count', 'user_name', 'bank_account_title', 'created_at', 'completed_at',
            ])->make();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreBankTransactionImportRequest $request
     *
     * @return JsonResponse
     */
    public function store(StoreBankTransactionImportRequest $request)
    {
        $result = $this->importService->processFileUpload(
            $request->file('bank_file'),
            $request->post('bank_account_id'),
            \Auth::id()
        );

        if (!$result['success']) {
            $errorKey = match($result['error_code']) {
                'DUPLICATE_FILE' => 'الملف مرفوع مسبقاً .',
                'NO_TRANSACTIONS' => 'لا يوجد عمليات .',
                default => 'الملف غير صالح .'
            };

            return response()->json([
                'success' => false,
                'errors' => [
                    'bank_file' => [$errorKey],
                ],
            ], 400);
        }

        return response()->json($result['import']);
    }

    /**
     * Display the specified resource.
     *
     * @param BankTransactionImport $bankTransactionImport
     *
     * @return BankTransactionImportResource
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function show(BankTransactionImport $bankTransactionImport)
    {
        if (request()->get('action') === 'edit')
            abort_if(!empty($bankTransactionImport->completed_at), 404);
        $bankTransactionImport->loadSum('items', 'amount');
        $bankTransactionImport->loadCount('items');
        $bankTransactionImport->load([
            'items' => function ($q) {
                $q->with('user')->orderBy('created_at');
            }, 'bank_account', 'user',
        ]);
        return new BankTransactionImportResource($bankTransactionImport);
    }

    /**
     * Handle import actions (confirm/cancel)
     *
     * @param Request $request
     * @param BankTransactionImport $bankTransactionImport
     *
     * @return JsonResponse
     */
    public function actions(Request $request, BankTransactionImport $bankTransactionImport)
    {
        $action = $request->get('action');

        if ($action === 'confirm') {
            $result = $this->confirmationService->confirmImport($bankTransactionImport);
        } elseif ($action === 'cancel') {
            $result = $this->confirmationService->cancelImport($bankTransactionImport);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'إجراء غير صالح'
            ], 400);
        }

        $statusCode = $result['success'] ? 200 : 400;
        return response()->json($result, $statusCode);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateBankTransactionImportRequest $request
     * @param BankTransactionImport $bankTransactionImport
     *
     * @return JsonResponse|BankTransactionImportResource
     */
    public function update(UpdateBankTransactionImportRequest $request, BankTransactionImport $bankTransactionImport)
    {
        $data = $request->validated();

        $result = $this->confirmationService->updateImportItems(
            $bankTransactionImport,
            $data['items'] ?? []
        );

        if (!$result['success']) {
            return response()->json($result, 400);
        }

        // Reload the import with updated data
        $bankTransactionImport->loadSum('items', 'amount');
        $bankTransactionImport->loadCount('items');
        $bankTransactionImport->load([
            'items' => function ($q) {
                $q->with('user')->orderBy('created_at');
            }, 'bank_account', 'user',
        ]);

        return new BankTransactionImportResource($bankTransactionImport);
    }
}
