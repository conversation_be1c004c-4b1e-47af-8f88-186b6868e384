<?php

namespace App\Http\Controllers\API\V1\HelpForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\HelpFormShareRequest;
use App\Http\Resources\API\UserFilterResource;
use App\Models\HelpForm;
use App\Models\HelpFormShare;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Validation\Rule;

class HelpFormShareController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            (new Middleware('can:update,help_form'))->except(['index']),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(HelpForm $helpForm)
    {
        return response()->json(
            $helpForm->shares()->with(['user.father.father.father'])
                ->get()
                ->map(function (HelpFormShare $helpFormShare) {
                    return [
                        'id' => $helpFormShare->id,
                        'roles' => $helpFormShare->roles ?? [],
                        'user' => new UserFilterResource($helpFormShare->user),
                    ];
                })
        );
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(HelpFormShareRequest $request, HelpForm $helpForm)
    {
        $data = $request->validated();
        $helpForm->shares()->create($data);
        return $this->apiSuccess();
    }

    /**
     * Display the specified resource.
     */
    public function show(HelpForm $helpForm, HelpFormShare $helpFormShare)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(HelpFormShareRequest $request, HelpForm $helpForm, HelpFormShare $helpFormShare)
    {
        $data = $request->validated();
        $helpForm->shares()->create($data);
        return $this->apiSuccess();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HelpForm $helpForm, HelpFormShare $helpFormShare)
    {
        $helpFormShare->delete();
        return $this->apiSuccess();
    }
}
