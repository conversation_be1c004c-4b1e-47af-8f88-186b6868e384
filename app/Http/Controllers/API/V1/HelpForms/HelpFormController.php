<?php

namespace App\Http\Controllers\API\V1\HelpForms;

use App\Utils\HelpFormPdfUtil;
use App\Enums\FamilyRelationType;
use App\Enums\Gender;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\HelpFormStoreRequest;
use App\Http\Resources\API\HelpForm\HelpFormEditResource;
use App\Models\HelpForm;
use App\Models\HelpFormFamilyMember;
use App\Models\HelpFormInvoice;
use App\Models\HelpFormItem;
use App\Models\HelpFormTemplate;
use App\Models\User;
use App\Permissions\HelpFormPermissions;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use niklasravnsborg\LaravelPdf\Facades\Pdf;
use Webklex\PDFMerger\Facades\PDFMergerFacade as PDFMerger;

class HelpFormController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(HelpForm::class),
            new Middleware('can:restore,help_form', only: ['restore']),
            new Middleware('can:pdf,help_form', only: ['pdf']),
        ];
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(HelpFormStoreRequest $request)
    {
        $data = $request->validated();
        $template = HelpFormTemplate::find($data['help_form_template_id']);
        $draft = HelpForm::query()->whereNull('completed_at')->where([
            'user_id' => $data['user_id'],
            'created_by_id' => Auth::id(),
            'help_form_template_id' => $template->id,
        ])->first();
        $limit = help_form_limit_allowed($template, $data['user_id']);
        $overAllowedSupport = help_form_supporting_value_limit_allowed($template, $data['user_id']);
        if ($overAllowedSupport) {
            throw ValidationException::withMessages([
                'user_id' => ['المستفيد تخطى الدعم المسموح .'],
            ]);
        } else if ($limit) {
            throw ValidationException::withMessages([
                'user_id' => [sprintf('المستفيد تخطى عدد نماذج "%s" المسموح بها .', $template->title)],
            ]);
        } else if (!is_null($draft)) {
            throw ValidationException::withMessages([
                'user_id' => ['يوجد نموذج مفتوح لنفس المستفيد '],
            ]);
        }
        $helpForm = HelpForm::create([
            'created_by_id' => Auth::id(),
            'user_id' => $data['user_id'],
            'help_form_template_id' => $data['help_form_template_id'],
            'user_region_id' => User::find($data['user_id'])?->user_region_id,
        ]);
        if (user()->hasPermissionTo(HelpFormPermissions::createForOther) && !is_null($data['responsible_user_id']))
            $helpForm->update([
                'responsible_user_id' => $data['responsible_user_id'],
            ]);
        return response()->json([
            'success' => true,
            'uuid' => $helpForm->uuid,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public
    function show(HelpForm $helpForm)
    {
        $helpForm->load(['template', 'user.father.father.father']);
        if (request('action') === 'view') {
            $helpForm->load([
                'family_members' => fn($q) => $q->where('is_active', true),
                'family_members.member',
                'items' => fn($q) => $q->whereNotNull('submitted_at'),
                'items.template_section',
                'items.template_item',
                'old_comments.user.father.father.father', 'reasons.user.father.father.father', 'user_region', 'breadwinner', 'responsible_user.father.father.father', 'created_by.father.father.father'
            ]);
        } else {
            if ($helpForm->user->gender === Gender::Male) {
                $helpForm->user->load(['children', 'active_wives', 'active_wives.wife']);
            } else {
                $helpForm->user->load(['mother_children', 'active_husband', 'active_husband.husband']);
            }
            $helpForm->load([
                'family_members',
                'template.sections',
            ]);
        }
        return new HelpFormEditResource($helpForm);
    }

    /**
     * Update the specified resource in storage.
     */
    public
    function update(Request $request, HelpForm $helpForm)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public
    function destroy(HelpForm $helpForm)
    {
        $helpForm->delete();
    }

    /**
     * Remove the specified resource from storage.
     */
    public
    function drafts()
    {
        return response()->json(
            HelpForm::query()
                ->with(['user.father.father.father'])
                ->whereNull('completed_at')
                ->where('created_by_id', Auth::id())
                ->get()
                ->map(fn($helpForm) => [
                    'uuid' => $helpForm->uuid,
                    'user' => $helpForm->user->only(['id', 'family_user_id', 'name', 'full_name']),
                    'template' => $helpForm->template->only(['id', 'title']),
                ])
        );
    }

    /**
     * Remove the specified resource from storage.
     */
    public
    function templates()
    {
        return response()->json(HelpFormTemplate::OnlyActive()->orderBy('id')->get());
    }

    /**
     * restore the specified resource from storage.
     */
    public
    function restore(HelpForm $helpForm)
    {
        $helpForm->restore();
        return response()->json(['success' => true]);
    }

    /**
     * Permanently Remove the specified resource from storage.
     */
    public
    function permanently_delete(HelpForm $helpForm)
    {
        $helpForm->forceDelete();
        return response()->json(['success' => true]);
    }

    /**
     * update payment status and save receipt.
     */
    public
    function payment(Request $request, HelpForm $helpForm)
    {
        abort_if(!is_null($helpForm->payment_at), 403);
        $data = $request->validate([
            'payment_at' => ['required', 'date'],
            'payment_receipt' => ['required', 'file', 'mimes:png,jpeg,bmp,pdf'],
        ]);
        $file = $request->file('payment_receipt');

        $isPdf = $file->getMimeType() === 'application/pdf';
        $dir = $isPdf ? 'pdf' : 'images';
        $fileName = basename($file->store("public/help-forms/$dir"));
        $filePath = "help-forms/$dir/$fileName";

        $helpForm->update([
            'payment_at' => $data['payment_at'],
            'payment_receipt_path' => $filePath
        ]);
    }

    /**
     * @param Request $request
     * @param HelpForm $helpForm
     *
     * @return \Symfony\Component\HttpFoundation\Response|void
     */
    public function pdf(Request $request, HelpForm $helpForm, HelpFormPdfUtil $pdfUtil)
    {
        $pdf = $pdfUtil->generatePdf($helpForm);
        $response = match (get_class($pdf)) {
            \niklasravnsborg\LaravelPdf\Pdf::class => $pdf->output(),
            \Webklex\PDFMerger\PDFMerger::class => $pdf->output(),
        };

        return response($response, 200, [
            'Content-Transfer-Encoding' => 'binary',
            'Content-Description' => 'File Transfer',
            'Content-Disposition' => 'attachment; filename="' . ('helpForm-' . $helpForm->form_id . '.pdf') . '"',
            'Content-length' => strlen($response),
            'Content-Type' => 'application/pdf',
            'Pragma' => 'no-cache',
        ]);
    }


    /**
     * @param Request $request
     *
     * @return JsonResponse
     * @throws \Throwable
     */
    public function duplicate(Request $request)
    {
        try {
            $data = $request->validate([
                'help_form_uuid' => ['required', Rule::exists(HelpForm::class, 'uuid')->whereNotNull('completed_at')],
                'help_form_template_id' => ['required', Rule::exists(HelpFormTemplate::class, 'id')],
            ]);
            /** @var HelpForm $helpForm */
            $helpForm = HelpForm::whereNotNull('completed_at')->findOrFail($data['help_form_uuid']);
            /** @var HelpFormTemplate $targetHelpFormTemplate */
            $targetHelpFormTemplate = HelpFormTemplate::findOrFail($data['help_form_template_id']);
            if (HelpForm::query()->whereNull('completed_at')->where([
                'user_id' => $helpForm->user_id,
                'created_by_id' => Auth::id(),
                'help_form_template_id' => $data['help_form_template_id'],
            ])->exists())
                throw ValidationException::withMessages([
                    'help_form_template_id' => ['يوجد نموذج مفتوح لنفس المستفيد !']
                ]);
            $limit = help_form_limit_allowed($targetHelpFormTemplate, $helpForm->user_id);
            $overAllowedSupport = help_form_supporting_value_limit_allowed($targetHelpFormTemplate, $helpForm->user_id);
            if ($overAllowedSupport)
                throw ValidationException::withMessages([
                    'help_form_template_id' => ['المستفيد تخطى الدعم المسموح .']
                ]);
            else if ($limit)
                throw ValidationException::withMessages([
                    'help_form_template_id' => [sprintf('المستفيد تخطى عدد نماذج "%s" المسموح بها.', $helpForm->template->title)]
                ]);
            DB::beginTransaction();
            $duplicatedHelpForm = HelpForm::create(array_merge($helpForm->only([
                'user_id',
                'gender', 'marital_status',
                'data', 'max_income',
                'is_employee', 'salary',
                'has_social_support', 'social_support',
                'has_citizen_account', 'citizen_account',
                'has_spouses_salary', 'spouses_salary',
                'expenses',
                'address', 'notes',
                'dowry',
                'spouses_count',
                'underage_children_count',
                'adult_children_count',
                'children_count',
            ]), [
                'help_form_template_id' => $data['help_form_template_id'],
                'name' => $helpForm->user->name,
                'age' => age($helpForm->user->dob),
                'user_region_id' => $helpForm->user_region_id,
                'created_by_id' => Auth::id(),
                'responsible_user_id' => Auth::id(),
            ]));
            if ($targetHelpFormTemplate->required_fields->residence === true)
                $duplicatedHelpForm->update($helpForm->only([
                    'residence', 'residence_type', 'residence_status', 'residence_size', 'residence_rent_cost', 'residence_services',
                ]));
            if ($targetHelpFormTemplate->family_details_required)
                $duplicatedHelpForm->family_members()->saveMany(
                    $helpForm->family_members()
                        ->withWhereHas('member')
                        ->get()
                        ->map(fn($member) => new HelpFormFamilyMember(array_merge([
                            'help_form_template_id' => $data['help_form_template_id'],
                            'is_active' => !$member->member->is_user_dead,
                            'first_name' => $member->member->name,
                            'name' => $member->member->full_name,
                            'dob' => $member->member->dob,
                            'age' => age($member->member->dob),
                            'health_status' => $member->member->health_status,
                            'educational_status' => $member->member->educational_status,
                            'gender' => $member->member->gender,
                        ], $member->only([
                            'member_id', 'relation',
                        ]))))
                        ->all()
                );
            /*if ($targetHelpFormTemplate->has_invoices)
                $duplicatedHelpForm->invoices()->saveMany(
                    $helpForm->invoices
                        ->map(fn($invoice) => new HelpFormInvoice($invoice->only([
                            'type', 'no', 'value', 'due_at'
                        ])))
                        ->all()
                );*/
            if ($helpForm->help_form_template_id === $targetHelpFormTemplate->id)
                $duplicatedHelpForm->items()->saveMany(
                    $helpForm->items
                        ->map(fn($item) => new HelpFormItem($item->only([
                            'help_form_template_item_id',
                            'type', 'submitted_at', 'data',
                        ])))
                        ->all()
                );
            DB::commit();
            return response()->json([
                'success' => true,
                'uuid' => $duplicatedHelpForm->uuid,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            if ($exception instanceof ValidationException)
                throw $exception;
            \Log::error($exception);
            throw ValidationException::withMessages([
                'help_form_template_id' => ['حدث خطأ ما !']
            ]);
        }
    }
}
