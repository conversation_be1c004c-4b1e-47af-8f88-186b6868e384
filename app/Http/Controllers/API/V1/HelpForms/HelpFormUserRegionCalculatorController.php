<?php

namespace App\Http\Controllers\API\V1\HelpForms;

use App\Http\Controllers\Controller;
use App\Models\UserRegion;
use App\Models\UserRegionCalculator;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;

class HelpFormUserRegionCalculatorController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            //new Middleware('can:restore,help_form', only: ['restore']),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(UserRegion $userRegion)
    {
        $calculator = UserRegionCalculator::query()
            ->firstOrCreate([
                'user_region_id' => $userRegion->id,
            ], [
                'restricted_values' => collect(range(1, 20))->mapWithKeys(fn($i) => ["$i" => 0])->toArray(),
            ]);
        if ($calculator->wasRecentlyCreated)
            $calculator->refresh();
        return response()->json($calculator->toArray());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserRegion $userRegion, Request $request)
    {
        $calculator = UserRegionCalculator::query()
            ->firstOrCreate([
                'user_region_id' => $userRegion->id,
            ], [
                'restricted_values' => collect(range(1, 20))->mapWithKeys(fn($i) => ["$i" => 0])->toArray(),
            ]);
        $data = $request->validate([
            'per_case' => ['required', 'numeric', 'gte:0', 'lte:100000'],
            'male_children' => ['required', 'numeric', 'gte:0', 'lte:100000'],
            'female_children' => ['required', 'numeric', 'gte:0', 'lte:100000'],
            'wives' => ['required', 'numeric', 'gte:0', 'lte:100000'],
            'family_husband' => ['required', 'numeric', 'gte:0', 'lte:100000'],
            'non_family_husband' => ['required', 'numeric', 'gte:0', 'lte:100000'],
            'debit' => ['required', 'numeric', 'gte:0', 'lte:100000'],
            'rent' => ['required', 'numeric', 'gte:0', 'lte:100000'],
            'restricted_values' => ['required', 'array', 'min:20', 'max:20'],
            'restricted_values.*' => ['required', 'numeric', 'gt:0', 'lte:100000'],
        ]);
        $calculator->update($data);
        return $this->apiSuccess();
    }
}
