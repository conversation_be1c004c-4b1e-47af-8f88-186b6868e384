<?php

namespace App\Http\Controllers\API\V1;

use App\Settings\AppSettings;
use App\Models\FamilyGraphShare;
use App\Settings\FeatureSettings;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\URL;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Routing\Controllers\HasMiddleware;
use App\Http\Requests\API\FamilyGraphShareRequest;
use App\Http\Resources\API\FamilyGraphShareResource;
use AshAllenDesign\ShortURL\Facades\ShortURL as ShortURLFacade;

class FamilyGraphShareController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(FamilyGraphShare::class),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return DataTables::eloquent(FamilyGraphShare::query()->orderByDesc('id')->with(['user.father.father.father']))
            ->addColumn('user_name', fn($familyGraph) => $familyGraph->user->full_name)
            ->addColumn('share_url', fn($familyGraph) => $familyGraph->share_url)
            ->addIndexColumn()
            ->only([
                'id', 'user_name', 'views', 'last_view_at', 'share_url',
                'expired_at', 'created_at', 'deleted_at', 'last_view_at',
            ])
            ->toJson();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FamilyGraphShareRequest $request, AppSettings $appSettings, FeatureSettings $featureSettings)
    {
        $tries = 0;
        do {
            $tries += 1;
            $slug = \Str::random($tries > 10 ? 8 : 6);
        } while (FamilyGraphShare::query()->where('slug', $slug)->exists());
        /** @var FamilyGraphShare $familyGraphShare */
        $familyGraphShare = FamilyGraphShare::query()->create(array_merge([
            'slug' => $slug,
            'created_by_id' => \Auth::id(),
        ], $request->only(['user_id', 'password', 'expired_at'])));
        $user = \App\Models\User::query()->with(['father'])->find($request->post('user_id'));
        if ($featureSettings->shortUrlEnabled) {
            $domain = "https://{$appSettings->appDomain}";
            URL::forceRootUrl($domain);
            $url = url("family-graph/{$slug}");
            $shortURL = ShortURLFacade::destinationUrl($url)
                ->trackVisits(true)
                ->make();
            $shortURL->update([
                'title' => 'مشجرة ' . $user->getName(1),
                'source' => 'FAMILY_GRAPH_SHARES',
            ]);
            $familyGraphShare->short_url()->associate($shortURL)->save();
        }
        return response()->json([
            'success' => true,
        ], Response::HTTP_CREATED);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FamilyGraphShareRequest $request, FamilyGraphShare $familyGraphShare, FeatureSettings $featureSettings)
    {
        $familyGraphShare->update($request->only(['password', 'expired_at']));
        if (is_null($familyGraphShare->short_url) && $featureSettings->shortUrlEnabled) {
            $user = $familyGraphShare->user->load(['father']);
            $shortURL = ShortURLFacade::destinationUrl(signedRoute('family-graph', $familyGraphShare))
                ->trackVisits(true)->make();
            $shortURL->update([
                'title' => 'مشجرة ' . $user->getName(1),
                'source' => 'FAMILY_GRAPH_SHARES',
            ]);
            $familyGraphShare->short_url()->associate($shortURL)->save();
        }
        return response()->json([
            'success' => true,
        ], Response::HTTP_OK);
    }

    /**
     * Display the specified resource.
     */
    public function show(FamilyGraphShare $familyGraphShare)
    {
        $familyGraphShare->load(['user']);
        return new FamilyGraphShareResource($familyGraphShare);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FamilyGraphShare $familyGraphShare)
    {
        $familyGraphShare->update(['deleted_by_id' => \user()->id]);
        if (!is_null($familyGraphShare->short_url))
            $familyGraphShare->short_url->delete();
        $familyGraphShare->delete();
        return response()->json([
            'success' => true,
        ], Response::HTTP_OK);
    }
}
