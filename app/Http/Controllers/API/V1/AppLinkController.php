<?php

namespace App\Http\Controllers\API\V1;

use Arr;
use App\Models\AppLink;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\AppLinkRequest;
use Intervention\Image\Laravel\Facades\Image;
use App\Http\Resources\API\AppLinkResource;
use Illuminate\Routing\Controllers\HasMiddleware;

class AppLinkController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(AppLink::class),
        ];
    }

    /**
     * Display the specified resource.
     */
    public function show(AppLink $appLink)
    {
        return new AppLinkResource($appLink);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, AppLink $appLink)
    {
        if ($request->get('forced', false) === true) {
            // todo captcha for confirm
            $this->permanently_delete($appLink);
        } else
            $appLink->delete();
        return response()->json(['success' => true]);
    }

    /**
     * Permanently Remove the specified resource from storage.
     */
    public function permanently_delete(AppLink $appLink)
    {
        $appLink->forceDelete();
        return response()->json(['success' => true]);
    }

    /**
     * Sorting resource in storage.
     */
    public function sorting(Request $request, AppLink $appLink)
    {
        $index = $request->post('index');
        if ($index === -1) {
            AppLink::withTrashed()
                ->where('index', '>', $appLink->index)
                ->decrement('index');
            $appLink->update(['index' => (AppLink::query()->max('index') ?: -1) + 1]);
        } else {
            if ($appLink->index > $index)
                AppLink::withTrashed()
                    ->whereBetween('index', [$index, $appLink->index - 1])
                    ->increment('index');
            else
                AppLink::withTrashed()
                    ->whereBetween('index', [$appLink->index + 1, $index])
                    ->decrement('index');
            $appLink->update(['index' => $index]);
        }
        return response()->json(['success' => true]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AppLinkRequest $request, AppLink $appLink)
    {
        $data = $request->validated();
        if (!empty($data['image'])) {
            $imageFile = $request->file('image');
            $fileName = $imageFile->hashName();
            if ($imageFile->getMimeType() === 'image/svg+xml') {
                \Storage::disk('public')
                    ->putFileAs("uploads", $imageFile, $fileName);
            } else {
                $image = Image::read($imageFile);
                $ratio = 300 / 1000;
                $width = min($image->width(), 1500);
                $height = $width * $ratio;
                $image->resize($width, $height)
                    ->save(\Storage::disk('public')->path("uploads/$fileName"));
            }
            $data['image'] = [
                'path' => "uploads/$fileName",
                'disk' => 'tenant',
            ];
        } else
            unset($data['image']);
        $appLink->update($data);
        return response()->json(['success' => true]);
    }

    /**
     * App a newly created resource in storage.
     */
    public function store(AppLinkRequest $request)
    {
        $data = $request->validated();
        if (!empty($data['image'])) {
            $imageFile = $request->file('image');
            $fileName = $imageFile->hashName();
            if ($imageFile->getMimeType() === 'image/svg+xml') {
                \Storage::disk('public')
                    ->putFileAs("uploads", $imageFile, $fileName);
            } else {
                $image = Image::read($imageFile);
                $ratio = 300 / 1000;
                $width = min($image->width(), 1500);
                $height = $width * $ratio;
                $image->resize($width, $height)
                    ->save(\Storage::disk('public')->path("uploads/$fileName"));
            }
            $data['image'] = [
                'path' => "uploads/$fileName",
                'disk' => 'tenant',
            ];
        } else
            unset($data['image']);
        $data['index'] = (AppLink::query()->max('index') ?? -1) + 1;
        AppLink::query()->create($data);
        return response()->json(['success' => true]);
    }

    /**
     * restore the specified resource from storage.
     */
    public function restore(AppLink $appLink)
    {
        $appLink->restore();
        return response()->json(['success' => true]);
    }

    public function statistics()
    {
        $all = AppLink::withTrashed()->count();
        $clicks = AppLink::withTrashed()->sum('clicks');

        return response()->json([
            [
                'title' => 'إجمالي الروابط',
                'value' => $all ?? 0,
                'xl' => 12,
                'lg' => 12,
                'md' => 12,
                'sm' => 12,
            ],
            [
                'title' => 'إجمالي الضغطات',
                'value' => $clicks ?? 0,
                'xl' => 12,
                'lg' => 12,
                'md' => 12,
                'sm' => 12,
            ],
        ]);
    }
}
