<?php

namespace App\Http\Controllers\API\V1;

use App\Models\User;
use App\Policies\ShortURLPolicy;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\ShortURLRequest;
use App\Models\ShortURL;
use Ash<PERSON><PERSON>Design\ShortURL\Classes\Builder;
use AshAllenDesign\ShortURL\Facades\ShortURL as ShortURLFacade;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Routing\Controllers\HasMiddleware;

class ShortURLController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(ShortURL::class),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return DataTables::eloquent(ShortURL::query()->withCount(['visits'])->orderByDesc('id'))
            ->addIndexColumn()
            ->only([
                'id', 'title', 'url_key', 'source', 'single_use',
                'default_short_url', 'visits_count', 'created_at',
            ])
            ->to<PERSON><PERSON>();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ShortURLRequest $request)
    {
        $shortURL = ShortURLFacade::destinationUrl($request->post('destination_url'))
            ->trackVisits(boolval($request->post('track_visits')))
            ->singleUse(boolval($request->post('single_use')))
            ->when(
                $request->post('url_key'),
                function (Builder $builder, string $urlKey): Builder {
                    return $builder->urlKey($urlKey);
                },
            )
            ->make();
        $shortURL->update($request->only(['title']));
        return response()->json([
            'success' => true,
        ], Response::HTTP_CREATED);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ShortURLRequest $request, ShortURL $shortURL)
    {
        $shortURL->update([
            'url_key' => $request->post('url_key'),
            'title' => $request->post('title'),
            'destination_url' => $request->post('destination_url'),
            'track_visits' => boolval($request->post('track_visits')),
            'single_use' => boolval($request->post('single_use')),
            'default_short_url' => $this->buildDefaultShortUrl($request->post('url_key')),
        ]);
        return response()->json([
            'success' => true,
        ], Response::HTTP_OK);
    }

    /**
     * Build and return the default short URL that will be stored in the
     * database.
     *
     * @param string $urlKey
     *
     * @return string
     */
    private function buildDefaultShortUrl(string $urlKey): string
    {
        $baseUrl = config('short-url.default_url') ?? 'https://t.hsb.sa';
        $baseUrl .= '/';

        if ($this->prefix() !== null) {
            $baseUrl .= $this->prefix() . '/';
        }

        return $baseUrl . urlencode($urlKey);
    }

    /**
     * Get the short URL route prefix.
     *
     * @return string|null
     */
    public function prefix(): ?string
    {
        $prefix = config('short-url.prefix');

        if ($prefix === null) {
            return null;
        }

        return trim($prefix, '/');
    }

    /**
     * Display the specified resource.
     */
    public function show(ShortURL $shortURL)
    {
        abort_if(
            request('action') === 'edit' &&
            !(!$shortURL->single_use && empty($shortURL->source) && can('update', $shortURL))
            , 403
        );
        /*$visits = $shortURL->visits()
            ->where('device_type', '!=', 'robot')
            ->select(['browser', 'operating_system', 'device_type', 'visited_at', 'created_at'])->get();
        $browserVisits = $visits
            ->groupBy(fn($v) => $v->browser)
            ->map(fn($v, $k) => ['name' => $k, 'value' => $v->count(), 'percent' => $v->count() / $visits->count() * 100])
            ->values();
        $osVisits = $visits
            ->groupBy(fn($v) => $v->operating_system)
            ->map(fn($v, $k) => ['name' => $k, 'value' => $v->count(), 'percent' => $v->count() / $visits->count() * 100])
            ->values();
        $dayVisits = $visits
            ->groupBy(fn($v) => $v->created_at->toDateString())
            ->map(fn($v, $k) => ['name' => $k, 'value' => $v->count(), 'percent' => $v->count() / $visits->count() * 100])
            ->values();*/
        return $shortURL;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ShortURL $shortURL)
    {
        $shortURL->delete();
        return response()->json([
            'success' => true,
        ], Response::HTTP_OK);
    }
}
