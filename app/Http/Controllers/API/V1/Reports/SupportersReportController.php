<?php

namespace App\Http\Controllers\API\V1\Reports;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\BankAccountTransaction;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Excel as ExcelFormat;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

trait SupportersReportController
{
    public function supporters(Request $request)
    {
        $transactions = BankAccountTransaction::query()
            ->whereHas('user')
            ->with(['user.father.father.father'])->get();
        $anonymousTransactions = BankAccountTransaction::query()
            ->whereNull("user_id")
            ->with(['supporter'])->get();
        $data = $transactions
            ->groupBy(['user_id', 'account'])
            ->map(function ($userTransactions) {
                return $userTransactions
                    ->map(function ($transactions) {
                        $transaction = $transactions->first();
                        $user = $transaction->user;
                        $account = $transaction->account;
                        return [
                            'family_user_id' => $user->family_user_id,
                            'full_name' => $user->full_name,
                            'phone' => $user->phone ? '="' . $user->phone . '"' : '',
                            'account' => $account ? '="' . $account . '"' : '',
                            'total' => $transactions->sum('amount'),
                            'count' => $transactions->count(),
                        ];
                    })
                    ->values();
            })
            ->collapse()
            ->values()
            ->toArray();

        $data = array_merge($data, $anonymousTransactions
            ->groupBy('account')
            ->map(function ($transactions) {
                $transaction = $transactions->first();
                $supporter = $transaction->supporter;
                $account = $transaction->account;
                return [
                    'family_user_id' => '',
                    'full_name' => $supporter ? $supporter->name : '',
                    'phone' => $supporter ? ($supporter->phone ? '="' . $supporter->phone . '"' : '') : '',
                    'account' => $account ? '="' . $account . '"' : 'غير معروف',
                    'total' => $transactions->sum('amount'),
                    'count' => $transactions->count(),
                ];
            })
            ->values()
            ->toArray());

        return Excel::download(new class($data) implements FromArray, WithHeadings, WithColumnFormatting, WithColumnWidths
        {
            protected $data;

            public function __construct(array $data)
            {
                $this->data = $data;
            }

            public function array(): array
            {
                return $this->data;
            }

            public function headings(): array
            {
                return [
                    'الرقم التعريفي',
                    'الاسم',
                    'رقم الجوال',
                    'رقم الحساب',
                    'الإجمالي',
                    'عدد الحوالات',
                ];
            }

            public function columnFormats(): array
            {
                return [
                    'A' => NumberFormat::FORMAT_TEXT,
                    'B' => NumberFormat::FORMAT_TEXT,
                    'C' => NumberFormat::FORMAT_TEXT,
                    'D' => NumberFormat::FORMAT_TEXT,
                    'E' => NumberFormat::FORMAT_NUMBER,
                    'F' => NumberFormat::FORMAT_NUMBER,
                ];
            }

            public function columnWidths(): array
            {
                return [
                    'A' => 10,
                    'B' => 40,
                    'C' => 8,
                    'D' => 20,
                    'E' => 20,
                    'F' => 20,
                    'H' => 15,
                    'L' => 15,
                ];
            }
        }, 'memberships.xlsx', ExcelFormat::XLSX);
    }
}
