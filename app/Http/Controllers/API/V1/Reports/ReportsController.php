<?php

namespace App\Http\Controllers\API\V1\Reports;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;
use App\Permissions\GeneralPermissions;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Routing\Controllers\HasMiddleware;

class ReportsController extends Controller implements HasMiddleware
{
    use DonorsReportController,
        ProductTransactionReportController,
        MembershipsReportController,
        SupportersReportController,
        HelpFormZakatRentReportController;


    public static function middleware(array $options = []): array
    {
        return [
            new Middleware('can:' . GeneralPermissions::REPORTS),
        ];
    }

    public function __invoke(Request $request)
    {
        $reports = [
            ['title' => 'العمليات لآخر 3 شهور', 'report_name' => 'transaction:3'],
            ['title' => 'العمليات لآخر 6 شهور', 'report_name' => 'transaction:6'],
            ['title' => 'العمليات لآخر 12 شهر', 'report_name' => 'transaction:12'],
            ['title' => 'العمليات لآخر 24 شهر', 'report_name' => 'transaction:24'],
            ['title' => 'العمليات لآخر 36 شهر', 'report_name' => 'transaction:36'],

            ['title' => 'متبرعون لآخر 3 شهور', 'report_name' => 'store_donors_3_months'],
            ['title' => 'متبرعون بدون عضويات', 'report_name' => 'report1'],
            ['title' => 'إحصائيات حالات المساعدة', 'report_name' => 'help_forms_statistics'],
            ['title' => 'المتبرعين بأكثر من/يساوي 5000 ريال', 'report_name' => 'donors:5000'],
            ['title' => 'العضويات النشطة', 'report_name' => 'memberships'],
            ['title' => 'الداعمون', 'report_name' => 'supporters'],
        ];
        if ($request->isMethod('POST')) {
            $request->validate([
                'report_name' => ['required', Rule::in(collect($reports)->pluck('report_name'))],
            ]);
            @[$reportName, $params] = explode(':', $request->post('report_name'));
            if ($reportName) {
                if (!method_exists($this, $reportName))
                    response()->json([
                        'success' => false,
                        'message' => 'التقرير غير موجود !',
                    ], 400);
                else {
                    if ($params) {
                        $params = explode(',', $params);
                        return $this->{$reportName}($request, ...$params);
                    }
                    return $this->{$reportName}($request);
                }
            }
        }
        return DataTables::collection($reports)
            ->addIndexColumn()
            ->only([
                'title', 'report_name',
            ])
            ->toJson();
    }
}
