<?php

namespace App\Http\Controllers\API\V1\Reports;

use App\Enums\Gender;
use App\Enums\ResidenceType;
use App\Enums\HelpFormStatus;
use App\Models\HelpForm;
use App\Models\HelpFormTemplate;
use App\Enums\UserMaritalStatus;
use App\Models\HelpFormFamilyMember;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\EventAttendancesExport;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Excel as ExcelFormat;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

trait HelpFormZakatRentReportController
{
    public function zakat_rent_1446(Request $request)
    {
        return $this->zakat_rent($request, 1446);
    }

    public function zakat_rent(Request $request, $year)
    {
        $forms = HelpForm::query()
            ->whereRelation('template', 'type', 'ZAKAT_SUPPORT')
            ->whereNotNull('completed_at')
            ->where('residence', 'RENT')
            ->where('hijri_year', $year)
            ->with(['user'])
            ->get();
        $data = $forms
            ->map(function (HelpForm $form) {
                $user = $form->user;
                return [
                    'form_id' => $form->form_id,
                    'family_user_id' => $user->family_user_id,
                    'full_name' => $user->full_name,
                    'age' => $form->age,
                    'marital_status' => $form->marital_status ? trans('userColumns.marital_status.' . ($user->gender ?: Gender::Male) . '.' . $form->marital_status) : null,
                    'status' => (isset($form->status) ? ($form->status === \App\Enums\CaseStatus::Approved ? 'حالة مقبولة' : 'حالة مرفوضة') : 'لم تدرس'),
                ];
            })->values()->toArray();

        return Excel::download(new class($data) implements FromArray, WithHeadings, WithColumnFormatting, WithColumnWidths
        {
            protected $data;

            public function __construct(array $data)
            {
                $this->data = $data;
            }

            public function array(): array
            {
                return $this->data;
            }

            public function headings(): array
            {
                return [
                    'رقم النموذج',
                    'الرقم التعريفي',
                    'اسم المستفيد',
                    'العمر',
                    'الحالة الإجتماعية',
                    'حالة الطلب',
                ];
            }

            public function columnFormats(): array
            {
                return [
                    'A' => NumberFormat::FORMAT_TEXT,
                    'C' => NumberFormat::FORMAT_TEXT,
                    'D' => NumberFormat::FORMAT_TEXT,
                    'E' => NumberFormat::FORMAT_TEXT,
                    'F' => NumberFormat::FORMAT_TEXT,
                    'G' => NumberFormat::FORMAT_TEXT,
                ];
            }

            public function columnWidths(): array
            {
                return [
                    'A' => 15,
                    'B' => 10,
                    'C' => 40,
                    'D' => 20,
                    'E' => 15,
                    'F' => 15,
                    'G' => 15,
                ];
            }
        }, 'transactions.xlsx', ExcelFormat::XLSX);
    }

    public function zakat_rent_1445(Request $request)
    {
        return $this->zakat_rent($request, 1445);
    }

    public function help_forms_statistics(Request $request)
    {
        $startYear = $request->input('start_year', 1445);
        $endYear = $request->input('end_year', 1446);
        $sheet1 = $this->help_forms_zakat($request);
        $sheet2 = $this->help_forms_cash($request);
        $sheet3 = $this->help_forms_marriage($request);

        return Excel::download(new class([
            $sheet1,
            $sheet2,
            $sheet3,
        ]) implements WithMultipleSheets
        {
            public function __construct(private readonly array $sheets)
            {
            }

            /**
             * @return array
             */
            public function sheets(): array
            {
                return $this->sheets;
            }
        }, 'help-forms-statistics.xlsx', ExcelFormat::XLSX);
    }

    public function help_forms_zakat(Request $request)
    {
        return new class([1445, 1446]) implements FromArray, WithHeadings, WithColumnFormatting, WithColumnWidths, WithTitle, WithEvents
        {
            private HelpFormTemplate $template;

            public function __construct(private readonly array $years)
            {
                $this->template = HelpFormTemplate::query()->where('type', 'ZAKAT_SUPPORT')->firstOrFail();
            }

            public function array(): array
            {
                return [];
            }

            public function headings(): array
            {
                $data = collect($this->years)->mapWithKeys(function ($year) {
                    $helpFormQuery = HelpForm::query()
                        ->whereNotNull('completed_at')
                        ->where('hijri_year', $year)
                        ->where('help_form_template_id', $this->template->id);
                    $data = [
                        'total' => $helpFormQuery->clone()->count(),
                        'approved' => $helpFormQuery->clone()->where('status', HelpFormStatus::Approved)->count(),
                        'declined' => $helpFormQuery->clone()->where('status', HelpFormStatus::Declined)->count(),
                        'families' => $helpFormQuery->clone()
                                ->where('status', HelpFormStatus::Approved)
                                ->pluck('user_id')->unique()->count() ?? 0,
                    ];
                    $data['users'] = $data['families'] + (
                            HelpFormFamilyMember::query()
                                ->where('is_active', true)
                                ->whereRelation('help_form', 'hijri_year', $year)
                                ->whereRelation('help_form', 'status', HelpFormStatus::Approved)
                                ->whereRelation('help_form', 'help_form_template_id', $this->template->id)
                                ->pluck('member_id')->unique()->count() ?? 0
                        );
                    $data['widower'] = (
                        count(array_unique(array_merge(
                            $helpFormQuery->clone()
                                ->where('marital_status', UserMaritalStatus::Widower)
                                ->pluck('user_id')->toArray(),
                            $helpFormQuery->clone()
                                ->whereHas('user', function ($q) {
                                    $q->where('gender', Gender::Male)
                                        ->where('is_dead', true)
                                        ->whereHas('active_wives');
                                })
                                ->pluck('user_id')->toArray(),
                        ))) ?? 0
                    );
                    $data['divorced'] = $helpFormQuery->clone()
                        ->where('marital_status', UserMaritalStatus::Divorced)
                        ->pluck('user_id')->unique()->count() ?? 0;
                    $data['orphans'] = HelpFormFamilyMember::query()
                        ->where('is_active', true)
                        ->whereRelation('help_form.user', 'is_dead', true)
                        ->whereRelation('help_form', 'hijri_year', $year)
                        ->whereRelation('help_form', 'status', HelpFormStatus::Approved)
                        ->whereRelation('help_form', 'help_form_template_id', $this->template->id)
                        ->pluck('member_id')->unique()->count() ?? 0;
                    $data['rent'] = $helpFormQuery->clone()
                        ->where('residence', ResidenceType::Rent)
                        ->pluck('user_id')->unique()->count() ?? 0;
                    $data['citizen'] = $helpFormQuery->clone()
                        ->where('citizen_account', '>', 0)
                        ->pluck('user_id')->unique()->count() ?? 0;
                    $data['social'] = $helpFormQuery->clone()
                        ->where('social_support', '>', 0)
                        ->pluck('user_id')->unique()->count() ?? 0;

                    $data['helping_value'] = $helpFormQuery->clone()
                        ->where('status', HelpFormStatus::Approved)
                        ->sum('helping_value') ?? 0;
                    return ["$year" => $data];
                });

                $dataTitles = [
                    'total' => 'جميع الحالات',
                    'approved' => 'الحالات المقبولة',
                    'declined' => 'الحالات المرفوضة',
                    'families' => 'عدد الأسر  (المقبولة)',
                    'users' => 'عدد الأفراد (المقبولين)',
                    'widower' => 'عدد الأرامل',
                    'divorced' => 'عدد المطلقات',
                    'orphans' => 'عدد الأيتام (أبناء مستفيد متوفى)',
                    'rent' => 'عدد المستأجرين',
                    'citizen' => 'عدد المسجلبن بالضمان الاجتماعي',
                    'social' => 'عدد المسجلين بحساب المواطن',
                    'helping_value' => 'اجمالي مبالغ الدعم المصروفة',
                ];
                $sheetData = [
                    [$this->title()],
                    collect($this->years)->map(fn($year) => [$year, ''])->collapse()->toArray(),
                    ...(collect($dataTitles)->map(function ($title, $key) use ($data) {
                        $row = [];
                        foreach ($this->years as $year) {
                            $value = @$data->get("$year")[$key];
                            $row[] = $title;
                            $row[] = ($value && $value > 0) ? $value : '0';
                        }
                        return $row;
                    })->values()->toArray()),
                ];
                return $sheetData;
            }

            public function columnFormats(): array
            {
                $columns = [];
                for ($i = 1; $i <= count($this->years); $i++)
                    $columns[Coordinate::stringFromColumnIndex($i * 2)] = NumberFormat::FORMAT_TEXT;
                return $columns;
            }

            public function columnWidths(): array
            {
                $columns = [];
                for ($i = 1; $i <= count($this->years); $i++) {
                    $columns[Coordinate::stringFromColumnIndex($i * 2 - 1)] = 40;
                    $columns[Coordinate::stringFromColumnIndex($i * 2)] = 15;
                }
                return $columns;
            }

            /**
             * @return string
             */
            public function title(): string
            {
                return 'المساعدة النقدية (الزكاة)';
            }

            public function registerEvents(): array
            {
                return [
                    AfterSheet::class => function (AfterSheet $event) {
                        $event->sheet->setRightToLeft(true);
                        $col = Coordinate::stringFromColumnIndex(count($this->years) > 1 ? (count($this->years) * 2) : 2);
                        $event->sheet->mergeCells("A1:{$col}1"); // Merge first row
                        $event->sheet->getStyle("A1:{$col}1")->getAlignment()
                            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                        for ($i = 1; $i <= count($this->years); $i++) {
                            $colStart = Coordinate::stringFromColumnIndex(($i * 2) - 1);
                            $colEnd = Coordinate::stringFromColumnIndex($i * 2);
                            $event->sheet->mergeCells("{$colStart}2:{$colEnd}2"); // Merge first row
                            $event->sheet->getStyle("{$colStart}2:{$colEnd}2")->getAlignment()
                                ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                            $event->sheet->getStyle("{$colEnd}3:{$colEnd}14")->getAlignment()
                                ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                        }
                    },
                ];
            }
        };
    }

    public function help_forms_cash(Request $request)
    {
        return new class([1445, 1446]) implements FromArray, WithHeadings, WithColumnFormatting, WithColumnWidths, WithTitle, WithEvents
        {
            private HelpFormTemplate $template;

            public function __construct(private readonly array $years)
            {
                $this->template = HelpFormTemplate::query()->where('type', 'CASH_SUPPORT')->firstOrFail();
            }

            public function array(): array
            {
                return [];
            }

            public function headings(): array
            {
                $data = collect($this->years)->mapWithKeys(function ($year) {
                    $helpFormQuery = HelpForm::query()
                        ->whereNotNull('completed_at')
                        ->where('hijri_year', $year)
                        ->where('help_form_template_id', $this->template->id);
                    $data = [
                        'total' => $helpFormQuery->clone()->count(),
                        'approved' => $helpFormQuery->clone()->where('status', HelpFormStatus::Approved)->count(),
                        'declined' => $helpFormQuery->clone()->where('status', HelpFormStatus::Declined)->count(),
                        'families' => $helpFormQuery->clone()
                                ->where('status', HelpFormStatus::Approved)
                                ->pluck('user_id')->unique()->count() ?? 0,
                    ];
                    $data['users'] = $data['families'] + (
                            HelpFormFamilyMember::query()
                                ->where('is_active', true)
                                ->whereRelation('help_form', 'hijri_year', $year)
                                ->whereRelation('help_form', 'status', HelpFormStatus::Approved)
                                ->whereRelation('help_form', 'help_form_template_id', $this->template->id)
                                ->pluck('member_id')->unique()->count() ?? 0
                        );
                    $data['helping_value'] = $helpFormQuery->clone()
                        ->where('status', HelpFormStatus::Approved)
                        ->sum('helping_value') ?? 0;
                    return ["$year" => $data];
                });

                $dataTitles = [
                    'total' => 'جميع الحالات',
                    'approved' => 'الحالات المقبولة',
                    'declined' => 'الحالات المرفوضة',
                    'families' => 'عدد الأسر  (المقبولة)',
                    'users' => 'عدد الأفراد (المقبولين)',
                    'helping_value' => 'اجمالي مبالغ الدعم المصروفة',
                ];
                $sheetData = [
                    [$this->title()],
                    collect($this->years)->map(fn($year) => [$year, ''])->collapse()->toArray(),
                    ...(collect($dataTitles)->map(function ($title, $key) use ($data) {
                        $row = [];
                        foreach ($this->years as $year) {
                            $value = @$data->get("$year")[$key];
                            $row[] = $title;
                            $row[] = ($value && $value > 0) ? $value : '0';
                        }
                        return $row;
                    })->values()->toArray()),
                ];
                return $sheetData;
            }

            public function columnFormats(): array
            {
                $columns = [];
                for ($i = 1; $i <= count($this->years); $i++)
                    $columns[Coordinate::stringFromColumnIndex($i * 2)] = NumberFormat::FORMAT_TEXT;
                return $columns;
            }

            public function columnWidths(): array
            {
                $columns = [];
                for ($i = 1; $i <= count($this->years); $i++) {
                    $columns[Coordinate::stringFromColumnIndex($i * 2 - 1)] = 40;
                    $columns[Coordinate::stringFromColumnIndex($i * 2)] = 15;
                }
                return $columns;
            }

            /**
             * @return string
             */
            public function title(): string
            {
                return 'الدعم العيني';
            }

            public function registerEvents(): array
            {
                return [
                    AfterSheet::class => function (AfterSheet $event) {
                        $event->sheet->setRightToLeft(true);
                        $col = Coordinate::stringFromColumnIndex(count($this->years) > 1 ? (count($this->years) * 2) : 2);
                        $event->sheet->mergeCells("A1:{$col}1"); // Merge first row
                        $event->sheet->getStyle("A1:{$col}1")->getAlignment()
                            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                        for ($i = 1; $i <= count($this->years); $i++) {
                            $colStart = Coordinate::stringFromColumnIndex(($i * 2) - 1);
                            $colEnd = Coordinate::stringFromColumnIndex($i * 2);
                            $event->sheet->mergeCells("{$colStart}2:{$colEnd}2"); // Merge first row
                            $event->sheet->getStyle("{$colStart}2:{$colEnd}2")->getAlignment()
                                ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                            $event->sheet->getStyle("{$colEnd}3:{$colEnd}8")->getAlignment()
                                ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                        }
                    },
                ];
            }
        };
    }

    public function help_forms_marriage(Request $request)
    {
        return new class([1445, 1446]) implements FromArray, WithHeadings, WithColumnFormatting, WithColumnWidths, WithTitle, WithEvents
        {
            private HelpFormTemplate $template;

            public function __construct(private readonly array $years)
            {
                $this->template = HelpFormTemplate::query()->where('type', 'MARRIAGE_SUPPORT')->firstOrFail();
            }

            public function array(): array
            {
                return [];
            }

            public function headings(): array
            {
                $data = collect($this->years)->mapWithKeys(function ($year) {
                    $helpFormQuery = HelpForm::query()
                        ->whereNotNull('completed_at')
                        ->where('hijri_year', $year)
                        ->where('help_form_template_id', $this->template->id);
                    $data = [
                        'total' => $helpFormQuery->clone()->count(),
                        'approved' => $helpFormQuery->clone()->where('status', HelpFormStatus::Approved)->count(),
                        'declined' => $helpFormQuery->clone()->where('status', HelpFormStatus::Declined)->count(),
                        'helping_value' => $helpFormQuery->clone()
                                ->where('status', HelpFormStatus::Approved)
                                ->sum('helping_value') ?? 0
                    ];
                    return ["$year" => $data];
                });

                $dataTitles = [
                    'total' => 'جميع الحالات',
                    'approved' => 'الحالات المقبولة',
                    'declined' => 'الحالات المرفوضة',
                    'helping_value' => 'اجمالي مبالغ الدعم المصروفة',
                ];
                $sheetData = [
                    [$this->title()],
                    collect($this->years)->map(fn($year) => [$year, ''])->collapse()->toArray(),
                    ...(collect($dataTitles)->map(function ($title, $key) use ($data) {
                        $row = [];
                        foreach ($this->years as $year) {
                            $value = @$data->get("$year")[$key];
                            $row[] = $title;
                            $row[] = ($value && $value > 0) ? $value : '0';
                        }
                        return $row;
                    })->values()->toArray()),
                ];
                return $sheetData;
            }

            public function columnFormats(): array
            {
                $columns = [];
                for ($i = 1; $i <= count($this->years); $i++)
                    $columns[Coordinate::stringFromColumnIndex($i * 2)] = NumberFormat::FORMAT_TEXT;
                return $columns;
            }

            public function columnWidths(): array
            {
                $columns = [];
                for ($i = 1; $i <= count($this->years); $i++) {
                    $columns[Coordinate::stringFromColumnIndex($i * 2 - 1)] = 40;
                    $columns[Coordinate::stringFromColumnIndex($i * 2)] = 15;
                }
                return $columns;
            }

            /**
             * @return string
             */
            public function title(): string
            {
                return 'إعانة الزواج';
            }

            public function registerEvents(): array
            {
                return [
                    AfterSheet::class => function (AfterSheet $event) {
                        $event->sheet->setRightToLeft(true);
                        $col = Coordinate::stringFromColumnIndex(count($this->years) > 1 ? (count($this->years) * 2) : 2);
                        $event->sheet->mergeCells("A1:{$col}1"); // Merge first row
                        $event->sheet->getStyle("A1:{$col}1")->getAlignment()
                            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                        for ($i = 1; $i <= count($this->years); $i++) {
                            $colStart = Coordinate::stringFromColumnIndex(($i * 2) - 1);
                            $colEnd = Coordinate::stringFromColumnIndex($i * 2);
                            $event->sheet->mergeCells("{$colStart}2:{$colEnd}2"); // Merge first row
                            $event->sheet->getStyle("{$colStart}2:{$colEnd}2")->getAlignment()
                                ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                            $event->sheet->getStyle("{$colEnd}3:{$colEnd}6")->getAlignment()
                                ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                        }
                    },
                ];
            }
        };
    }
}
