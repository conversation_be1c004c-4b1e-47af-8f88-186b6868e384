<?php

namespace App\Http\Controllers\API\V1\Groups;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\GroupRequest;
use App\Http\Resources\API\GroupResource;
use App\Models\Group;
use App\Permissions\GroupPermissions;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Routing\Controllers\HasMiddleware;

class GroupController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(Group::class),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = \Auth::user();
        return DataTables::eloquent(
            Group::withTrashed()
                ->when((
                    $user->hasPermissionTo(GroupPermissions::viewAssigned) &&
                    !$user->hasPermissionTo(GroupPermissions::view)
                ), fn($q) => $q->whereHas('users', fn($q) => $q->where('user_id', $user->id)))
                ->withCount(['users'])
                ->orderByDesc('id')
        )
            ->addColumn('user', fn(Group $group) => $group->user?->only(['id', 'family_user_id', 'name']))
            ->only([
                'id', 'name', 'user', 'users_count',
                'created_at', 'updated_at', 'deleted_at'
            ])
            ->toJson();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(GroupRequest $request)
    {
        Group::create($request->validated());
        return response()->json([
            'success' => true
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Group $group)
    {
        $group->statistics = [
            'reports_count' => $group->group_dates()->whereHas('reports')->count(),
            'users_count' => $group->users()->count(),
            'last_date_at' => $group->group_dates()->withWhereHas('report')
                ->get()
                ->max(fn($d) => $d->report->created_at),
        ];
        return new GroupResource($group);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(GroupRequest $request, Group $group)
    {
        $group->update($request->validated());
        return response()->json([
            'success' => true
        ], Response::HTTP_OK);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Group $group)
    {
        $group->delete();
        return response()->json([
            'success' => true
        ], Response::HTTP_OK);
    }
}
