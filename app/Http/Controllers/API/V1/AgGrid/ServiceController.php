<?php

namespace App\Http\Controllers\API\V1\AgGrid;

use App\Models\Service;
use App\Http\Resources\AgGrid;
use App\Http\Controllers\Controller;
use <PERSON>shamFouda\AgGrid\AgGridQueryBuilder;
use Illuminate\Routing\Controllers\Middleware;
use App\Permissions\HallReservationPermissions;
use HeshamFouda\AgGrid\Requests\AgGridGetRowsRequest;

class ServiceController extends Controller
{
    public static function middleware(array $options = []): array
    {
        return [
            new Middleware('can:' . HallReservationPermissions::serviceIndex),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function __invoke(AgGridGetRowsRequest $request)
    {
        $query = Service::withTrashed()->withCount('hall_reservations');

        return AgGridQueryBuilder::forRequest($request, $query)
            ->addIndexColumn()
            ->resource(AgGrid\ServiceResource::class);
    }
}
