<?php

namespace App\Http\Controllers\API\V1\AgGrid;

use App\Models\HelpForm;
use App\Http\Resources\AgGrid;
use App\Http\Controllers\Controller;
use App\Models\HelpFormUserRegion;
use App\Permissions\HelpFormPermissions;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>shamFouda\AgGrid\AgGridQueryBuilder;
use HeshamFouda\AgGrid\Requests\AgGridGetRowsRequest;
use Illuminate\Support\Facades\Auth;

class HelpFormHistoryController extends Controller
{
    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(HelpForm::class),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function __invoke(AgGridGetRowsRequest $request, HelpForm $helpForm)
    {
        $id = Auth::id();
        $query = HelpForm::query()
            ->where('user_id', $helpForm->user_id)
            ->where('uuid', '!=', $helpForm->uuid)
            ->with([
                'shares', 'template', 'user_region', 'user.father.father', 'responsible_user.father.father', 'created_by.father.father',
            ]);
        match ($query->driver()) {
            'mariadb', 'mysql' => $query->orderByRaw('ISNULL(completed_at) DESC')->orderByDesc('completed_at'),
            'pgsql' => $query->orderByRaw('(completed_at IS NULL) DESC')->orderByDesc('completed_at'),
        };

        return AgGridQueryBuilder::forRequest($request, $query)
            ->addIndexColumn()
            ->resource(AgGrid\HelpFormHistoryResource::class);
    }
}
