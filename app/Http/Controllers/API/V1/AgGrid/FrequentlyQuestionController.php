<?php

namespace App\Http\Controllers\API\V1\AgGrid;

use App\Http\Resources\AgGrid;
use App\Models\FrequentlyQuestion;
use App\Http\Controllers\Controller;
use <PERSON>shamFouda\AgGrid\AgGridQueryBuilder;
use App\Permissions\FrequentlyQuestionPermissions;
use <PERSON>shamFouda\AgGrid\Requests\AgGridGetRowsRequest;

class FrequentlyQuestionController extends Controller
{
    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(FrequentlyQuestion::class),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function __invoke(AgGridGetRowsRequest $request)
    {
        $query = FrequentlyQuestion::query()->orderBy('index')
            ->when(user()->hasPermissionTo(FrequentlyQuestionPermissions::restore), fn($q) => $q->withTrashed());

        return AgGridQueryBuilder::forRequest($request, $query)
            ->addIndexColumn()
            ->resource(AgGrid\FrequentlyQuestionResource::class);
    }
}
