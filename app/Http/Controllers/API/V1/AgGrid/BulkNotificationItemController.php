<?php

namespace App\Http\Controllers\API\V1\AgGrid;

use App\Http\Controllers\Controller;
use App\Http\Resources\AgGrid;
use App\Models\BulkNotification;
use App\Models\BulkNotificationItem;
use App\Permissions\GeneralPermissions;
use Exception;
use <PERSON><PERSON><PERSON>oud<PERSON>\AgGrid\AgGridQueryBuilder;
use He<PERSON><PERSON>ouda\AgGrid\Requests\AgGridGetRowsRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class BulkNotificationItemController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            new Middleware('can:' . GeneralPermissions::BULK_NOTIFICATIONS),
        ];
    }

    /**
     * Display a listing of the resource.
     *
     * @throws Exception
     */
    public function __invoke(AgGridGetRowsRequest $request, BulkNotification $bulkNotification)
    {
        $query = BulkNotificationItem::query()
            ->where('bulk_notification_id', $bulkNotification->id)
            ->with(['user', 'user.father.father.father'])
            ->orderBy('id');

        return AgGridQueryBuilder::forRequest($request, $query)
            ->filterColumn('poll_options', function ($builder, Builder $subject, $filter, $operator = 'and') {
                $values = $filter['values'] ?? [];
                if (!empty($values)) {
                    $builder->where(function (Builder $query) use ($values) {
                        for ($i = 0; $i < count($values); $i++) {
                            if ($values[$i] === 'none')
                                $query->where(
                                    fn($q) => $q
                                        ->whereNull('poll_options')
                                        ->orWhereJsonLength('poll_options', 0),
                                    boolean: $i === 0 ? 'and' : 'or');
                            else
                                $query->whereJsonContains('poll_options', $values[$i], $i === 0 ? 'and' : 'or');
                        }
                    });
                }
            })
            ->filterColumn('family_user_id', function ($builder, Builder $subject, $filter, $operator = 'and') {
                $value = $filter['filter'] ?? null;
                if (!empty($value))
                    $subject->whereHas('user', fn($query) => ag_grid_full_name_filter($builder, $query, $filter, $operator));
            })
            ->filterColumn('full_name', function ($builder, Builder $subject, $filter, $operator = 'and') {
                $value = $filter['filter'] ?? null;
                if (!empty($value)) {
                    if (valid_phone_format($value, ['SA', 'AUTO']))
                        $subject->where('phone', phone_format($value, ['SA', 'AUTO']));
                    else
                        $subject->whereHas('user', fn($query) => ag_grid_full_name_filter($builder, $query, $filter, $operator));
                }
            })
            ->addIndexColumn()
            ->resource(AgGrid\BulkNotificationItemResource::class);
    }
}
