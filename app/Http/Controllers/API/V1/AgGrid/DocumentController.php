<?php

namespace App\Http\Controllers\API\V1\AgGrid;

use App\Models\Document;
use App\Http\Resources\AgGrid;
use App\Http\Controllers\Controller;
use App\Permissions\DocumentPermissions;
use <PERSON><PERSON>Fouda\AgGrid\AgGridQueryBuilder;
use <PERSON><PERSON>Fouda\AgGrid\Requests\AgGridGetRowsRequest;

class DocumentController extends Controller
{
    public static function middleware(array $options = []): array
    {
        return [
            ...self::customAuthorizeResource(Document::class),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function __invoke(AgGridGetRowsRequest $request)
    {
        $query = Document::query()
            ->when(user()->hasPermissionTo(DocumentPermissions::restore), fn($q) => $q->withTrashed());

        return AgGridQueryBuilder::forRequest($request, $query)
            ->addIndexColumn()
            ->resource(AgGrid\DocumentResource::class);
    }
}
