<?php

namespace App\Http\Controllers\API\V1\Activities;

use App\Models\Activity;
use App\Models\ActivityUser;
use Illuminate\Http\Request;
use App\Models\ActivityRequest;
use Illuminate\Validation\Rule;
use App\Models\ActivityMemberRole;
use App\Enums\ActivityRequestStatus;
use App\Http\Controllers\Controller;
use App\Permissions\ActivityPermissions;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Routing\Controllers\HasMiddleware;
use App\Http\Resources\API\ActivityRequestResource;

class ActivityRequestController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            new Middleware('can:' . ActivityPermissions::requests),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Activity $activity)
    {
        return DataTables::eloquent($activity->requests()
            ->where(function ($q) {
                $q->validRequests();
            })
            ->with(['user', 'transaction', 'activity_member_role']))
            ->editColumn('created_at', fn(ActivityRequest $activityRequest) => tz($activityRequest->created_at)?->toDayDateTimeString())
            ->editColumn('user', fn(ActivityRequest $activityRequest) => $activityRequest->user ? [
                'id' => $activityRequest->user->id,
                'name' => $activityRequest->user->getFullName(3, true, true),
                'gender' => $activityRequest->user->gender,
                'family_user_id' => $activityRequest->user->family_user_id,
            ] : null)
            ->addColumn('role', fn(ActivityRequest $activityRequest) => $activityRequest->activity_member_role?->only(['id', 'title']))
            ->addColumn('transaction', fn(ActivityRequest $activityRequest) => $activityRequest->transaction?->only([
                'amount', 'pdf_url',
            ]))
            ->filterColumn('gender', function (Builder $query, $keyword) {
                $query->whereRelation('user', 'gender', $keyword);
            })
            ->addIndexColumn()
            ->only([
                'uuid', 'activity_id', 'is_paid', 'user', 'role', 'status', 'created_at',
                'transaction',
            ])
            ->toJson();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Activity $activity)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Activity $activity, ActivityRequest $activityRequest)
    {
        $activityRequest->load(['user']);
        return new ActivityRequestResource($activityRequest);
    }

    /**
     * Update the specified resource in storage.
     */
    public function bulkUpdate(Request $request, Activity $activity)
    {
        $request->validate([
            'uuid' => ['required', 'array', 'min:1'],
            'uuid.*' => [Rule::exists(ActivityRequest::class, 'uuid')->where('activity_id', $activity->id)],
            'status' => [
                'required',
                Rule::in(ActivityRequestStatus::all()),
            ],
            'activity_member_role_id' => [
                'nullable',
                Rule::requiredIf($request->post('status') === ActivityRequestStatus::Approved),
                Rule::exists(ActivityMemberRole::class, 'id'),
            ],
        ]);

        $activityRequests = $activity->requests()->whereIn('uuid', $request->post('uuid'))->get();

        /*if ($activityRequests->pluck('status')->unique()->filter()->contains(fn($i) => in_array($i, [
            ActivityRequestStatus::Cancelled,
            ActivityRequestStatus::Approved,
            ActivityRequestStatus::Cancelled,
        ])))
            return response()->json(['success' => false]);*/
        $data = ['status'];
        if ($request->post('status') === ActivityRequestStatus::Approved)
            $data[] = 'activity_member_role_id';
        $activityRequests->each(function ($activityRequest) use ($request, $activity, $data) {
            if ($activityRequest->status === ActivityRequestStatus::Approved &&
                $request->post('status') !== ActivityRequestStatus::Approved) {
                ActivityUser::where($activityRequest->only([
                    'user_id',
                    'activity_id',
                ]))->delete();
                $activityRequest->update(['activity_member_role_id' => null]);
            }
            $activityRequest->update($request->only($data));
            if ($activityRequest->status === ActivityRequestStatus::Approved)
                ActivityUser::updateOrCreate($activityRequest->only([
                    'user_id',
                    'activity_id',
                ]), $request->only(['activity_member_role_id']));
        });
        return response()->json(['success' => true]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Activity $activity, ActivityRequest $activityRequest)
    {
        /*if (in_array($activityRequest->status, [
            ActivityRequestStatus::Cancelled,
            ActivityRequestStatus::Approved,
            ActivityRequestStatus::Cancelled,
        ]))
            return response()->json(['success' => false]);*/
        $request->validate([
            'status' => [
                'required',
                Rule::in(ActivityRequestStatus::all()),
            ],
            'activity_member_role_id' => [
                'nullable',
                Rule::requiredIf($request->post('status') === ActivityRequestStatus::Approved),
                Rule::exists(ActivityMemberRole::class, 'id'),
            ],
        ]);

        $data = ['status'];
        if ($request->post('status') === ActivityRequestStatus::Approved)
            $data[] = 'activity_member_role_id';
        else if ($activityRequest->status === ActivityRequestStatus::Approved) {
            ActivityUser::where($activityRequest->only([
                'user_id',
                'activity_id',
            ]))->delete();
            $activityRequest->update(['activity_member_role_id' => null]);
        }
        $activityRequest->update($request->only($data));
        if ($activityRequest->status === ActivityRequestStatus::Approved)
            ActivityUser::updateOrCreate($activityRequest->only([
                'user_id',
                'activity_id',
            ]), $request->only(['activity_member_role_id']));
        return response()->json(['success' => true]);
    }
}
