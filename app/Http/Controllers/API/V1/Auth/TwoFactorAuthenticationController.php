<?php

namespace App\Http\Controllers\API\V1\Auth;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Lara<PERSON>\Fortify\Contracts\TwoFactorAuthenticationProvider;

class TwoFactorAuthenticationController extends AuthBaseController
{
    /**
     * @return JsonResponse
     */
    function __invoke(Request $request)
    {
        $user = null;
        $validator = Validator::make($request->all(), [
            'token' => [
                'required', function ($attribute, $val, $fail) use (&$user) {
                    $id = decrypt($val);
                    $user = User::query()->where(compact('id'))->first();
                    if (is_null($user))
                        $fail('خطأ حاول مرة أخرى !');
                },
            ],
            'otp' => ['required'],
        ], [], []);

        try {
            $validator->validate();
            if (app(TwoFactorAuthenticationProvider::class)->verify(decrypt($user->two_factor_secret), $request->post('otp'))) {
                $refererHostDomain = 'Portal-API';
                try {
                    $refererHostDomain = $refererHostDomain . '|' . get_domain($request->headers->get('referer'));
                } catch (\Exception) {
                }
                /** @var User $user */
                $tokenResult = $user->createToken($refererHostDomain, ['portal']);
                $token = $tokenResult->token;
                $token->save();
                return response()->json([
                    'access_token' => $tokenResult->accessToken,
                    'token_type' => 'Bearer',
                    'expires_at' => Carbon::parse($tokenResult->token->expires_at)->toDateTimeString(),
                ]);
            }
            return response()->json([
                'success' => false,
                'errors' => [
                    'otp' => ['رمز الدخول غير صحيح !'],
                ],
            ], 400);
        } catch (ValidationException $exception) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'error' => $exception->getMessage(),
            ], 400);
        } catch (\Exception $exception) {
            \Log::error($exception);
            return response()->json([
                'success' => false,
                'message' => 'خطأ غير متوقع !',
                'error' => $exception->getMessage(),
            ], 400);
        }
    }
}
