<?php

namespace App\Http\Controllers\API\V1\Auth;

use Carbon\Carbon;
use App\Models\User;
use App\Models\UserOTP;
use App\Http\Controllers\Controller;
use App\Notifications\OTPNotification;
use App\Notifications\OTPMailNotification;

class AuthBaseController extends Controller
{
    public function onlyMethods(User $user)
    {
        $isFake = request()->post('fake') === '312fa765-8e53-4217-88fc-a180540f5360';
        return array_values(array_filter([
            (!empty($user->phone) && is_waha_working()) ? 'whatsapp' : null,
            (!empty($user->phone) && ($isFake || is_sa_phone($user->phone))) ? 'sms' : null,
            !empty($user->email) ? 'email' : null,
            $user->hasEnabledTwoFactorAuthentication() ? '2fa' : null,
        ]));
    }

    function createOtp(User $user, $method): UserOTP
    {
        $otp = app()->environment(['production']) ? rand(1009, 9999) : '1436';
        return $user->otp()->create([
            'method' => $method,
            'code' => $otp,
            'expired_at' => Carbon::now()->addMinutes(config('otp.timeout') ?? 3),
        ]);
    }

    function notify(User $user, UserOTP $userOTP, $method)
    {
        switch ($method) {
            case 'sms':
                $user->notify(new OTPNotification($userOTP->code, domain: get_domain(request()->headers->get('referer'))));
                break;
            case 'whatsapp':
                $user->notify(new OTPNotification($userOTP->code, domain: get_domain(request()->headers->get('referer')), via: 'waha'));
                break;
            case 'email':
                $user->notify(new OTPMailNotification($userOTP->code));
                break;
        }
    }
}
