<?php

namespace App\Http\Controllers\API\V1\QuranChallenges;

use App\Exports\QuranChallengeUsersExport;
use App\Http\Controllers\Controller;
use Illuminate\Routing\Controllers\Middleware;
use App\Http\Requests\API\QuranChallengeRequest;
use App\Models\QuranChallenge;
use App\Models\QuranCompetition;
use App\Permissions\QuranCompetitionPermissions;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Routing\Controllers\HasMiddleware;

class QuranChallengeController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            new Middleware('can:' . QuranCompetitionPermissions::view, only: ['index']),
            new Middleware('can:' . QuranCompetitionPermissions::update, except: ['index']),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(QuranCompetition $quranCompetition)
    {
        return DataTables::eloquent($quranCompetition->challenges()->latest()->withCount(['accepted_users', 'requests']))
            ->addIndexColumn()
            ->only([
                'id', 'title', 'accepted_users_count', 'requests_count',
                'chapters', 'created_at', 'updated_at',
            ])
            ->toJson();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(QuranChallengeRequest $request, QuranCompetition $quranCompetition)
    {
        $data = $request->validated();
        $quranCompetition->challenges()->create($data);
        return response()->json([
            'success' => true,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(QuranCompetition $quranCompetition, QuranChallenge $quranChallenge)
    {
        return $quranChallenge;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(QuranChallengeRequest $request, QuranCompetition $quranCompetition, QuranChallenge $quranChallenge)
    {
        $data = $request->validated();
        $quranChallenge->update($data);
        return response()->json([
            'success' => true,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(QuranCompetition $quranCompetition, QuranChallenge $quranChallenge)
    {
        return response()->json([
            'success' => true,
        ]);
    }

    /**
     * Download All attendances data.
     */
    public function xlsx(QuranCompetition $quranCompetition, QuranChallenge $quranChallenge)
    {
        $users = $quranChallenge->accepted_users()
            ->whereHas('user')
            ->with([
                'user',
                'user.father',
                'user.father.father',
                'user.father.father.father',
                'user.father.father.father.father',
            ])
            ->get()->unique('user_id')
            ->map(fn($quranUser) => [
                'user' => $quranUser->user->getFullName(4, true, true),
                'family_user_id' => $quranUser->user->family_user_id,
                'gender' => __('userColumns.gender.' . $quranUser->user->gender),
                'phone' => $quranUser->user->phone ? "+{$quranUser->user->phone}" : null,
                'age' => age($quranUser->user->dob),
            ]);
        return Excel::download(new QuranChallengeUsersExport($users), ($quranChallenge->title) . '.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }
}
