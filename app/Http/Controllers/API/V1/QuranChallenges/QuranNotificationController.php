<?php

namespace App\Http\Controllers\API\V1\QuranChallenges;

use App\Enums\Gender;
use App\Enums\QuranResultEnum;
use App\Enums\StatusEnum;
use App\Http\Controllers\Controller;
use App\Jobs\BulkNotificationHandle;
use App\Models\BulkNotification;
use App\Models\QuranCompetition;
use App\Permissions\GeneralPermissions;
use Illuminate\Routing\Controllers\Middleware;
use App\Permissions\QuranCompetitionPermissions;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Routing\Controllers\HasMiddleware;

class QuranNotificationController extends Controller implements HasMiddleware
{
    public static function middleware(array $options = []): array
    {
        return [
            new Middleware('can:' . GeneralPermissions::BULK_NOTIFICATIONS . ',' . QuranCompetitionPermissions::view),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(QuranCompetition $quranCompetition)
    {
        return DataTables::eloquent($quranCompetition->notifications()->withCount('items')->latest('id'))
            ->addIndexColumn()
            ->addColumn('status', function ($bulkNotification) {
                $sent = $bulkNotification->items()->whereNotNull('sent_at')->whereNull('delivered_at')->count();
                $delivered = $bulkNotification->items()->whereNotNull('delivered_at')->count();
                $pending = $bulkNotification->items()->whereNull('sent_at')->whereNull('delivered_at')->count();
                $failed = $bulkNotification->items()->whereNotNull('failed_at')->count();
                return compact(['sent', 'delivered', 'pending', 'failed']);
            })
            ->editColumn('user', fn($notification) => $notification->user?->only(['id', 'short_full_name', 'family_user_id']))
            ->editColumn('items_count', fn($notification) => $notification->items_count ?: count($notification->user_ids ?: []))
            ->only([
                'id', 'content', 'user', 'status', 'items_count', 'sent_at', 'delivered_at', 'created_at',
            ])
            ->toJson();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(QuranCompetition $quranCompetition, Request $request)
    {
        $data = $request->validate([
            'content' => ['required', 'max:1024'],
            'pending' => ['required', 'boolean'],
            'declined' => ['required', 'boolean'],
            'approved' => ['required', 'array'],
            'approved.passed' => ['required', 'boolean'],
            'approved.failed' => ['required', 'boolean'],
            'approved.no_result' => ['required', 'boolean'],
            'males' => ['required', 'boolean'],
            'females' => ['required', 'boolean'],
        ]);
        $usersQuery = $quranCompetition->users();
        if ($data['males'] && !$data['females'])
            $usersQuery = $usersQuery->whereHas('user', function ($q) {
                $q->where('gender', Gender::Male)
                    ->where(function ($_q) {
                        $_q->whereNotNull('phone')->orWhereHas('father', fn($__q) => $__q->whereNotNull('phone'));
                    });
            });
        elseif (!$data['males'] && $data['females'])
            $usersQuery = $usersQuery->whereHas('user', function ($q) {
                $q->where('gender', Gender::Female)
                    ->where(function ($_q) {
                        $_q->whereNotNull('phone')->orWhereHas('father', fn($__q) => $__q->whereNotNull('phone'));
                    });
            });
        elseif ($data['males'] && $data['females'])
            $usersQuery = $usersQuery->whereHas('user', function ($q) {
                $q->whereNotNull('phone')->orWhereHas('father', fn($__q) => $__q->whereNotNull('phone'));
            });
        if (
            (
                !$data['pending'] &&
                !$data['declined'] &&
                !$data['approved']['passed'] &&
                !$data['approved']['failed'] &&
                !$data['approved']['no_result']
            ) || (!$data['males'] && !$data['females'])
        )
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد مستخدمين لإرسال الرسالة لهم !',
                /*'errors' => [
                    'males' => ['لا يوجد مستخدمين لإرسال الرسالة لهم !'],
                    'females' => ['لا يوجد مستخدمين لإرسال الرسالة لهم !'],
                ]*/
            ], 400);

        if (!$data['pending'] ||
            !$data['declined'] ||
            !$data['approved']['passed'] ||
            !$data['approved']['failed'] ||
            !$data['approved']['no_result']) {
            $usersQuery = $usersQuery->where(function (Builder $q) use ($data) {
                if ($data['pending'])
                    $q->whereNull('status', boolean: 'or');
                if ($data['declined'])
                    $q->where('status', StatusEnum::Declined, boolean: 'or');
                if ($data['approved']['passed'] || $data['approved']['failed'] || $data['approved']['no_result']) {
                    $q->where(function (Builder $q) use ($data) {
                        $q->where('status', StatusEnum::Approved);
                        $q->where(function (Builder $q) use ($data) {
                            if ($data['approved']['passed'])
                                $q->where('result', QuranResultEnum::Passed, boolean: 'or');
                            if ($data['approved']['failed'])
                                $q->where('result', QuranResultEnum::Failed, boolean: 'or');
                            if ($data['approved']['no_result'])
                                $q->whereNull('result', boolean: 'or');
                        });
                    }, boolean: 'or');
                }
            });
        }
        $users = $usersQuery
            ->whereHas('user')
            ->with(['user', 'user.father'])
            ->get()
            ->unique('user_id')
            ->map(
                fn($quranUser) => !is_null($quranUser->user->phone) ? $quranUser->user->id
                    : (!is_null($quranUser->user->father?->phone) ? $quranUser->user->father->id : null)
            )
            ->filter()
            ->values()
            ->toArray();
        if (count($users) === 0)
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد مستخدمين لإرسال الرسالة لهم !',
            ], 400);
        $bulkNotification = new BulkNotification([
            'user_id' => $request->user()->id,
            'user_ids' => $users,
            'content' => $data['content']
        ]);
        $bulkNotification->owner()->associate($quranCompetition);
        $bulkNotification->save();
        BulkNotificationHandle::dispatch($bulkNotification)->onQueue('high');
        return response()->json([
            'success' => true
        ], Response::HTTP_OK);
    }
}
