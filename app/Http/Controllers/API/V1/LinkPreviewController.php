<?php

namespace App\Http\Controllers\API\V1;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use DOMDocument;
use Exception;

class LinkPreviewController extends Controller
{
    public function __invoke(Request $request)
    {
        try {
            $request->validate([
                'url' => 'required|url',
            ]);

            $url = $request->query('url');

            // Check cache first
            $cacheKey = 'link_preview_' . md5($url);
            if (Cache::has($cacheKey)) {
                return response()->json([
                    'success' => 1,
                    'link' => $url,
                    'meta' => Cache::get($cacheKey),
                ]);
            }

            // Fetch URL content
            $response = Http::timeout(5)
                ->withHeaders([
                    'Accept-Charset' => 'UTF-8',
                    'Accept-Language' => 'ar,en;q=0.9'  // Prioritize Arabic content
                ])
                ->get($url);
            $html = $response->body();

            // Convert HTML encoding to UTF-8 if it's not already
            $encoding = mb_detect_encoding($html, ['UTF-8', 'ISO-8859-1', 'ASCII', 'Windows-1252']);
            if ($encoding && $encoding !== 'UTF-8') {
                $html = mb_convert_encoding($html, 'UTF-8', $encoding);
            }

            // Ensure proper UTF-8 handling in DOMDocument
            $doc = new DOMDocument('1.0', 'UTF-8');
            $doc->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_NOWARNING | LIBXML_NOERROR);
            $metas = $doc->getElementsByTagName('meta');
            $title = $doc->getElementsByTagName('title');

            $metadata = [
                'title' => '',
                'description' => '',
                'image' => ['url' => ''],
            ];

            // Extract title
            if ($title->length > 0) {
                $metadata['title'] = $title->item(0)->textContent;
            }

            // Extract meta information
            foreach ($metas as $meta) {
                $property = $meta->getAttribute('property');
                $name = $meta->getAttribute('name');
                $content = $meta->getAttribute('content');

                switch (true) {
                    case $property === 'og:title' && empty($metadata['title']):
                        $metadata['title'] = $content;
                        break;
                    case $property === 'og:description' || $name === 'description':
                        $metadata['description'] = $content;
                        break;
                    case $property === 'og:image' || $name === 'twitter:image':
                        $metadata['image']['url'] = $content;
                        break;
                }
            }

            // Extract domain
            $domain = parse_url($url, PHP_URL_HOST);
            $domain = preg_replace('/^www\./', '', $domain);

            // Cache the results
            Cache::put($cacheKey, $metadata, now()->addHours(24));

            return response()->json([
                'success' => 1,
                'meta' => $metadata,
                'link' => $url,
                'domain' => $domain,
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => 0,
                'message' => 'Failed to fetch link preview: ' . $e->getMessage(),
            ], 500);
        }
    }
}
