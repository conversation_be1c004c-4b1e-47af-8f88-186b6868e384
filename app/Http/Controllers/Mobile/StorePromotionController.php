<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\StorePromotionIndexResource;
use App\Http\Resources\Mobile\StorePromotionResource;
use App\Models\StorePromotion;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class StorePromotionController extends Controller
{
    /**
     * @OA\Get(
     *  path="/store-promotions",
     *  summary="list all store-promotions",
     *  tags={"store-promotions"},
     *  @OA\Parameter(
     *     name="discount_type",
     *     in="query",
     *     description="Filter by discount type",
     *     required=false,
     *     @OA\Schema(type="string", enum={"percentage", "fixed"})
     *  ),
     *  @OA\Parameter(
     *     name="search",
     *     in="query",
     *     description="Search in title and description",
     *     required=false,
     *     @OA\Schema(type="string")
     *  ),
     *  @OA\Parameter(
     *     name="has_location",
     *     in="query",
     *     description="Filter promotions with location data",
     *     required=false,
     *     @OA\Schema(type="boolean")
     *  ),
     *  @OA\Parameter(
     *     name="active_only",
     *     in="query",
     *     description="Show only active (non-expired) promotions",
     *     required=false,
     *     @OA\Schema(type="boolean", default=true)
     *  ),
     *  @OA\Parameter(
     *     name="sort_by",
     *     in="query",
     *     description="Sort results by field",
     *     required=false,
     *     @OA\Schema(type="string", enum={"created_at", "discount", "valid_until", "title"}, default="created_at")
     *  ),
     *  @OA\Parameter(
     *     name="sort_direction",
     *     in="query",
     *     description="Sort direction",
     *     required=false,
     *     @OA\Schema(type="string", enum={"asc", "desc"}, default="desc")
     *  ),
     *  @OA\Response(
     *    response=200,
     *    description="A list with all store-promotions",
     *    @OA\JsonContent(
     *     @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/StorePromotionIndexResource")),
     *     @OA\Property(
     *      property="pagination",
     *      @OA\Property(property="total", type="integer", example="16"),
     *      @OA\Property(property="next_url", type="string"),
     *     ),
     *     @OA\Property(
     *      property="filters",
     *      @OA\Property(property="active_count", type="integer", example="12"),
     *      @OA\Property(property="expired_count", type="integer", example="4"),
     *      @OA\Property(property="percentage_count", type="integer", example="8"),
     *      @OA\Property(property="fixed_count", type="integer", example="4"),
     *     ),
     *    )
     *  ),
     *  @OA\Response(
     *    response="default",
     *    description="an ""unexpected"" error"
     *  )
     * )
     *
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $query = StorePromotion::query();

        // Active only filter - always applied to match original behavior
        $query->where(function ($q) {
            $q->whereDate('valid_until', '>=', now())
              ->orWhereNull('valid_until');
        });

        // Discount type filter
        if ($request->has('discount_type')) {
            $query->where('discount_type', $request->discount_type);
        }

        // Search filter
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('coupon', 'like', "%{$search}%");
            });
        }

        // Location filter
        if ($request->boolean('has_location')) {
            $query->whereNotNull('lat')->whereNotNull('lng');
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'id');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // Get filter counts for UI
        $baseQuery = StorePromotion::query();
        $filterCounts = [
            'active_count' => $baseQuery->clone()
                ->where(function ($q) {
                    $q->whereDate('valid_until', '>=', now())
                      ->orWhereNull('valid_until');
                })->count(),
            'expired_count' => $baseQuery->clone()
                ->whereDate('valid_until', '<', now())
                ->count(),
            'percentage_count' => $baseQuery->clone()
                ->where('discount_type', 'percentage')
                ->count(),
            'fixed_count' => $baseQuery->clone()
                ->where('discount_type', 'fixed')
                ->count(),
        ];

        $storePromotions = $query->clone()->cursorPaginate(20);

        return response()->json([
            'data' => StorePromotionIndexResource::collection($storePromotions->items()),
            'pagination' => [
                'total' => $query->clone()->count(),
                'items' => $storePromotions->count(),
                'next_url' => $storePromotions->nextPageUrl(),
            ],
            'filters' => $filterCounts,
        ]);
    }

    /**
     * @OA\Get(
     *  path="/store-promotions/{id}",
     *  summary="get store-promotions with id",
     *  tags={"store-promotions"},
     *  @OA\Parameter(
     *     name="id",
     *     in="path",
     *     required=true
     *  ),
     *  @OA\Response(
     *    response=200,
     *     description="resource response",
     *    @OA\JsonContent(ref="#/components/schemas/StorePromotionResource")
     *  ),
     *  @OA\Response(
     *    response=404,
     *    description="not found error"
     *  ),
     *  @OA\Response(
     *    response="default",
     *    description="an ""unexpected"" error"
     *  )
     * )
     *
     *
     * Display the specified resource.
     *
     * @param Request $request
     * @param StorePromotion $storePromotion
     * @return StorePromotionResource
     */
    public function show(Request $request, StorePromotion $storePromotion)
    {
        $user = $request->user('api');
        if ($user && !$storePromotion->interests()->where(['user_id' => $user->id])->exists())
            $storePromotion->interests()->create(['user_id' => $user->id]);
        return new StorePromotionResource($storePromotion);
    }
}
