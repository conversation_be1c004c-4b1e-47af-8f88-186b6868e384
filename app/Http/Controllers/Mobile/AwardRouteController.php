<?php

namespace App\Http\Controllers\Mobile;

use App\Models\AwardRoute;
use App\Http\Controllers\Controller;

class AwardRouteController extends Controller
{
    /**
     * Display the specified resource.
     */
    public function show(AwardRoute $awardRoute)
    {
        $awardRoute->load(['fields' => fn($q) => $q->where('enabled', true)]);
        return response()->json(array_merge($awardRoute->toArray(), [
            'terms' => json_encode($awardRoute->terms),
        ]));
    }
}
