<?php

namespace App\Http\Controllers\Mobile;

use App\Enums\QuranResultEnum;
use App\Enums\StatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\QuranCompetitionResource;
use App\Models\QuranChallenge;
use App\Models\QuranCompetition;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class QuranCompetitionController extends Controller
{
    /**
     * @OA\Get(
     *  path="/quran-competitions",
     *  summary="list all quran-competitions",
     *  tags={"quran-competitions"},
     *  @OA\Response(
     *    response=200,
     *    description="A list with all quran-competitions",
     *    @OA\JsonContent(
     *     @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/QuranCompetitionResource")),
     *     @OA\Property(
     *      property="pagination",
     *      @OA\Property(property="total", type="integer", example="16"),
     *      @OA\Property(property="next_url", type="string"),
     *     ),
     *    )
     *  ),
     *  @OA\Response(
     *    response="default",
     *    description="an ""unexpected"" error"
     *  )
     * )
     *
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $query = QuranCompetition::query()
            ->where('invisible', false)
            ->where('accessible', true)
            ->orderByDesc('id');
        $quranCompetitions = $query->clone()->cursorPaginate();
        return response()->json([
            'data' => QuranCompetitionResource::collection($quranCompetitions->items()),
            'pagination' => [
                'total' => $query->clone()->count(),
                'items' => $quranCompetitions->count(),
                'next_url' => $quranCompetitions->nextPageUrl(),
            ],
        ]);
    }

    /**
     * @OA\Get(
     *  path="/companies/{id}",
     *  summary="get companies with id",
     *  tags={"companies"},
     *  @OA\Parameter(
     *     name="id",
     *     in="path",
     *     required=true
     *  ),
     *  @OA\Response(
     *    response=200,
     *     description="resource response",
     *    @OA\JsonContent(ref="#/components/schemas/QuranCompetitionResource")
     *  ),
     *  @OA\Response(
     *    response=404,
     *    description="not found error"
     *  ),
     *  @OA\Response(
     *    response="default",
     *    description="an ""unexpected"" error"
     *  )
     * )
     *
     *
     * Display the specified resource.
     *
     * @param Request $request
     * @param QuranCompetition $quranCompetition
     * @return QuranCompetitionResource
     */
    public function show(Request $request, QuranCompetition $quranCompetition)
    {
        abort_if(!$quranCompetition->accessible, 403);
        $user = $request->user();
        $userRequest = $quranCompetition->users()->where('user_id', $user->id)->latest()->first();
        $action = $request->get('action');
        if ($action === 'apply')
            $quranCompetition->load(['challenges']);
        $passesChapters = QuranChallenge::query()
            ->whereHas('accepted_users', function ($q) use ($user) {
                $q->where('user_id', $user->id)
                    ->where('status', StatusEnum::Approved)
                    ->where('result', QuranResultEnum::Passed);
            })
            ->max('chapters');
        $quranCompetition->user_reach_max = $passesChapters >= 30;
        $quranCompetition->user_has_request = !is_null($userRequest);
        $quranCompetition->user_request_status = $userRequest?->status;
        return new QuranCompetitionResource($quranCompetition);
    }

    /**
     * @OA\Post(
     *  path="/quran-competitions/{id}/requests",
     *  summary="apply quranCompetition request with id",
     *  tags={"quran-competitions"},
     *  @OA\Parameter(
     *     name="id",
     *     in="path",
     *     required=true
     *  ),
     *  @OA\Response(
     *    response=200,
     *     description="resource response",
     *    @OA\JsonContent(ref="#/components/schemas/QuranCompetitionResource")
     *  ),
     *  @OA\Response(
     *    response=404,
     *    description="not found error"
     *  ),
     *  @OA\Response(
     *    response="default",
     *    description="an ""unexpected"" error"
     *  )
     * )
     *
     * Display the specified resource.
     *
     * @param Request $request
     * @param QuranCompetition $quranCompetition
     * @return JsonResponse
     * @throws Exception
     */
    public function apply_request(Request $request, QuranCompetition $quranCompetition)
    {
        $user = $request->user();
        $current = $quranCompetition->users()->where('user_id', $user->id)->latest()->first();
        if (!empty($current?->status))
            return response()->json(['success' => false]);
        $request->validate([
            'challenge_id' => Rule::exists('quran_challenges', 'id')->where('quran_competition_id', $quranCompetition->id)
        ]);
        $challenge = $quranCompetition->challenges()->findOrFail($request->post('challenge_id'));
        $passesChapters = QuranChallenge::query()
            ->whereHas('accepted_users', function ($q) use ($user) {
                $q->where('user_id', $user->id)
                    ->where('status', StatusEnum::Approved)
                    ->where('result', QuranResultEnum::Passed);
            })
            ->max('chapters');
        if ($passesChapters >= 30)
            return response()->json([
                'success' => false,
                'error' => 'لقد اجتزت 30 جزء لا يمكنك التسجيل مرة أخرى.',
            ]);
        if ($passesChapters >= $challenge->chapters)
            return response()->json([
                'success' => false,
                'error' => sprintf('لقد اجتزت %s %s بالفعل يجب التسجيل بعدد أجزاء أكبر.', $passesChapters, ($passesChapters < 11 ? 'أجزاء' : 'جزء')),
            ]);
        if ($current)
            $current->update([
                'quran_challenge_id' => $challenge->id,
            ]);
        else
            $quranCompetition->users()->create([
                'quran_challenge_id' => $challenge->id,
                'user_id' => $user->id,
            ]);
        return response()->json(['success' => true]);
    }
}
