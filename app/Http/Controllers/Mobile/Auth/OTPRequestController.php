<?php

namespace App\Http\Controllers\Mobile\Auth;

use App\Exceptions\OTPException;
use App\Models\User;
use App\Models\UserOTP;
use App\Services\OTPService;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Random\RandomException;

class OTPRequestController extends AuthBaseController
{
    private const MAX_ATTEMPTS = 3;
    private const RATE_LIMIT_KEY_PREFIX = 'otp-request:';
    private const FAKE_TOKEN = '312fa765-8e53-4217-88fc-a180540f5360';

    public function __construct(protected OTPService $otpService)
    {
    }

    /**
     * Handle OTP request and send verification code
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        try {
            [$user, $authMethod, $uuid] = $this->validateAndGetUser($request);
            $method = $this->validateDeliveryMethod($request, $user);

            $this->checkRateLimit($user, $method);

            $metadata = $this->collectMetadata($request);
            $userOTP = $this->createOTPRecord($user, $method, $authMethod, $metadata);

            return $this->handleOTPDelivery($user, $userOTP, $method, $request);
        } catch (ThrottleRequestsException $e) {
            return $this->errorResponse('Too many attempts. Please try again later.', 429);
        } catch (ValidationException $e) {
            return $this->errorResponse(null, 400, ['errors' => $e->errors()]);
        } catch (OTPException $e) {
            return $this->errorResponse($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            Log::error('OTP Request Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('An unexpected error occurred', 500);
        }
    }

    /**
     * Validate token and retrieve user
     *
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    private function validateAndGetUser(Request $request): array
    {
        $user = null;
        $validator = Validator::make($request->all(), [
            'token' => ['required', function ($attribute, $value, $fail) {
                try {
                    [$id, $authMethod, $uuid] = explode('|', decrypt($value));
                    $user = User::find($id);

                    if (!$user) {
                        $fail('Invalid user token.');
                    }

                    return $user;
                } catch (\Exception $e) {
                    $fail('Invalid token format.');
                }
            }],
        ]);
        $data = $validator->validate();
        [$id, $authMethod, $uuid] = explode('|', decrypt($data['token']));
        return [User::findOrFail($id), $authMethod, $uuid];
    }

    /**
     * Validate OTP delivery method
     *
     * @param Request $request
     * @param User $user
     * @return string
     * @throws ValidationException
     */
    private function validateDeliveryMethod(Request $request, User $user): string
    {
        $availableMethods = Arr::except($this->onlyMethods($user), ['2fa']);

        $validator = Validator::make($request->all(), [
            'method' => ['required', 'string', Rule::in($availableMethods)],
        ]);

        $validator->validate();
        return $request->input('method');
    }

    /**
     * Check rate limiting for OTP requests
     *
     * @param User $user
     * @param string $method
     * @throws ThrottleRequestsException
     */
    private function checkRateLimit(User $user, string $method): void
    {
        $key = self::RATE_LIMIT_KEY_PREFIX . $user->id;

        if (\RateLimiter::tooManyAttempts($key, self::MAX_ATTEMPTS)) {
            Log::channel('slack')->error(sprintf('محاولة طلب كود مؤقت أكثر من مرة رقم التعريفي: #%s الطريقة: %s', $user->family_user_id, $method));
            throw new ThrottleRequestsException();
        }

        \RateLimiter::increment($key);
    }

    /**
     * Collect request metadata
     *
     * @param Request $request
     * @return array
     */
    public function collectMetadata(Request $request): array
    {
        return array_filter([
            'app' => 'family-social',
            'referer' => $request->headers->get('referer'),
            'user_agent' => $request->headers->get('user-agent'),
            'X-APP-ID' => $request->headers->get('X-APP-ID'),
            'X-APP-BUILD' => $request->headers->get('X-APP-BUILD'),
            'X-APP-VERSION' => $request->headers->get('X-APP-VERSION'),
        ]);
    }

    /**
     * Create OTP record
     *
     * @param User $user
     * @param string $method
     * @param string $authMethod
     * @param array $metadata
     * @return UserOTP
     * @throws RandomException
     */
    private function createOTPRecord(User $user, string $method, string $authMethod, array $metadata): UserOTP
    {
        return $this->otpService->create($user, $method, $authMethod, $metadata);
    }

    /**
     * Handle OTP delivery and response
     *
     * @param User $user
     * @param UserOTP $userOTP
     * @param string $method
     * @param Request $request
     * @return JsonResponse
     * @throws OTPException
     */
    private function handleOTPDelivery(User $user, UserOTP $userOTP, string $method, Request $request): JsonResponse
    {
        $isFake = $request->input('fake') === self::FAKE_TOKEN;

        if (!$isFake) {
            try {
                $this->notify($user, $userOTP, $method);
            } catch (\Exception $e) {
                if (Str::lower($e->getMessage()) === 'your balance is 0') {
                    throw new OTPException('4203 راجع الإدارة .', 400);
                }
                throw new OTPException('Failed to send OTP', 400);
            }
        }

        return response()->json([
            'success' => true,
            'uuid' => $userOTP->uuid,
            ...($isFake ? ['code' => Str::reverse($userOTP->code)] : []),
        ]);
    }

    /**
     * Generate error response
     *
     * @param string|null $message
     * @param int $status
     * @param array $additional
     * @return JsonResponse
     */
    private function errorResponse(?string $message = null, int $status = 400, array $additional = []): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message ?? 'خطأ غير متوقع !',
            ...$additional
        ], $status);
    }
}
