<?php

namespace App\Http\Controllers\Mobile\Auth;

use App\Models\User;
use App\Services\OTPService;
use App\Settings\AppSettings;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class OTPMethodController extends AuthBaseController
{
    private const MAX_IP_ATTEMPTS = 3;
    private const MAX_USER_ATTEMPTS = 2;
    private const FAKE_TOKEN = '312fa765-8e53-4217-88fc-a180540f5360';

    protected OTPService $otpService;

    public function __construct(OTPService $otpService)
    {
        $this->otpService = $otpService;
    }

    /**
     * Handle OTP methods request
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $user = $this->validateAndGetUser($request);
            $this->checkRateLimits($request, $user);

            if ($user) {
                return $this->handleExistingUser($user, $request);
            }

            return $this->defaultResponse();
        } catch (ValidationException $exception) {
            return $this->validationErrorResponse($exception->validator);
        } catch (ThrottleRequestsException $exception) {
            return $this->errorResponse('Too many attempts. Please try again later.', 429);
        } catch (\Exception $exception) {
            Log::error('OTP Method Error', [
                'message' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);
            return $this->errorResponse('خطأ غير متوقع !');
        }
    }

    /**
     * Validate request and get user
     *
     * @param Request $request
     *
     * @return User|null
     * @throws ValidationException
     */
    public function validateAndGetUser(Request $request): ?User
    {
        $user = null;
        $validator = Validator::make($request->all(), [
            'phone' => [
                'nullable',
                Rule::phone()->country(['AUTO'])->mobile(),
                function ($attribute, $value, $fail) use (&$user) {
                    $phone = phone_format($value, ['SA', 'AUTO'], false) ?: $value;
                    $user = User::where(compact('phone'))->first();
                },
            ],
            'family_user_id' => [
                'nullable',
                'numeric',
                function ($attribute, $value, $fail) use (&$user) {
                    $user = User::where('family_user_id', $value)->first();
                },
            ],
            'email' => [
                'nullable',
                'email',
                function ($attribute, $value, $fail) use (&$user) {
                    $user = User::where('email', $value)->first();
                },
            ],
        ]);

        $validator->validate();
        return $user;
    }

    /**
     * Check rate limits for various identifiers
     *
     * @param Request $request
     * @param User|null $user
     *
     * @throws ThrottleRequestsException
     */
    private function checkRateLimits(Request $request, ?User $user): void
    {
        $message = sprintf('محاولة تسجيل الدخول أكثر من مرة من خلال أي بي: %s', $request->ip());
        // Check IP rate limit
        $this->checkRateLimit('ip:' . $request->ip(), self::MAX_IP_ATTEMPTS, $message);

        // Check user identifiers rate limits
        $data = $request->only(['email', 'phone', 'family_user_id']);
        foreach ($data as $key => $value) {
            if (!empty($value)) {
                $message = sprintf('محاولة تسجيل الدخول أكثر من مرة من خلال %s: %s', $key, $value);
                $this->checkRateLimit("otp-methods:$value", self::MAX_USER_ATTEMPTS, $message);
            }
        }
    }

    /**
     * Check individual rate limit
     *
     * @param string $key
     * @param int $maxAttempts
     * @param string $message
     *
     * @throws ThrottleRequestsException
     */
    private function checkRateLimit(string $key, int $maxAttempts, string $message): void
    {
        if (\RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            Log::channel('slack')->error($message);
            throw new ThrottleRequestsException();
        }
        \RateLimiter::increment($key);
    }

    /**
     * Handle existing user logic
     *
     * @param User $user
     * @param Request $request
     *
     * @return JsonResponse
     */
    private function handleExistingUser(User $user, Request $request): JsonResponse
    {
        $methods = $this->onlyMethods($user);
        $authMethod = $this->determineAuthMethod($request);

        if (count($methods) === 1 && $methods[0] === 'sms') {
            return $this->handleSMSMethod($user, $request);
        }

        if (count($methods) >= 1) {
            return $this->handleMultipleMethods($user, $methods, $authMethod);
        }

        return $this->defaultResponse();
    }

    /**
     * Determine authentication method from request
     *
     * @param Request $request
     *
     * @return string
     */
    private function determineAuthMethod(Request $request): string
    {
        return match (true) {
            !empty($request->input('phone')) => 'phone',
            !empty($request->input('email')) => 'email',
            !empty($request->input('family_user_id')) => 'family_user_id',
            default => 'null',
        };
    }

    /**
     * Handle SMS method
     *
     * @param User $user
     * @param Request $request
     *
     * @return JsonResponse
     */
    private function handleSMSMethod(User $user, Request $request): JsonResponse
    {
        $isFake = $request->input('fake') === self::FAKE_TOKEN;
        $inReview = $user->is_apple_reviewer && app(AppSettings::class)->reviewMode;

        try {
            $userOTP = $this->createOtp($user, 'sms', $this->determineAuthMethod($request), $this->collectMetadata($request));

            if (!$isFake && !$inReview)
                $this->notify($user, $userOTP, 'sms');

            return response()->json([
                'success' => true,
                'uuid' => $userOTP->uuid,
                'method' => 'sms',
                ...($isFake ? ['code' => Str::reverse($userOTP->code)] : []),
            ]);
        } catch (\Exception $exception) {
            if (Str::lower($exception->getMessage()) === 'your balance is 0') {
                return $this->errorResponse('4203 راجع الإدارة .');
            }
            return $this->errorResponse('خطأ غير متوقع !');
        }
    }

    /**
     * Handle multiple methods
     *
     * @param User $user
     * @param array $methods
     * @param string $authMethod
     *
     * @return JsonResponse
     */
    private function handleMultipleMethods(User $user, array $methods, string $authMethod): JsonResponse
    {
        return response()->json([
            'success' => true,
            'token' => encrypt($user->id . "|{$authMethod}|" . Str::uuid()),
            'methods' => $methods,
        ]);
    }

    /**
     * Collect request metadata
     *
     * @param Request $request
     *
     * @return array
     */
    public function collectMetadata(Request $request): array
    {
        return array_filter([
            'app' => 'family-social',
            'referer' => $request->headers->get('referer'),
            'user_agent' => $request->headers->get('user-agent'),
            'X-APP-ID' => $request->headers->get('X-APP-ID'),
            'X-APP-BUILD' => $request->headers->get('X-APP-BUILD'),
            'X-APP-VERSION' => $request->headers->get('X-APP-VERSION'),
        ]);
    }

    /**
     * Default response when no user found
     *
     * @return JsonResponse
     */
    private function defaultResponse(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'uuid' => Str::uuid(),
            'method' => 'sms',
        ]);
    }

    /**
     * Generate error response
     *
     * @param string $message
     * @param int $status
     *
     * @return JsonResponse
     */
    private function errorResponse(string $message, int $status = 400): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ], $status);
    }

    /**
     * Generate validation error response
     *
     * @param \Illuminate\Validation\Validator $validator
     *
     * @return JsonResponse
     */
    private function validationErrorResponse($validator): JsonResponse
    {
        return response()->json([
            'success' => false,
            'errors' => $validator->errors(),
        ], 400);
    }
}
