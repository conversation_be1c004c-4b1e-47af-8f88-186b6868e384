<?php

namespace App\Http\Controllers\Mobile\Auth;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LogoutController extends AuthBaseController
{
    /**
     * @OA\Get(
     *  path="/auth/logout",
     *  summary="logout",
     *  tags={"auth"},
     *  @OA\Response(
     *    response=200,
     *    description="default",
     *    @OA\JsonContent(
     *       @OA\Property(property="success", type="bool", example=true),
     *    )
     *  ),
     *  security={{"bearer": {}}},
     * )
     *
     * @return JsonResponse
     */
    function __invoke(Request $request)
    {
        $request->user()->token()->revoke();
        return response()->json([
            'success' => true,
        ]);
    }
}
