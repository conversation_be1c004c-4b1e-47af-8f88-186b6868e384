<?php

namespace App\Http\Controllers\Mobile;

use App\Models\News;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\NewsResource;
use App\Http\Resources\NewsIndexResource;

class NewsController extends Controller
{
    /**
     * @OA\Get(
     *  path="/news",
     *  summary="list all news",
     *  tags={"news"},
     *  @OA\Response(
     *    response=200,
     *    description="A list with published news",
     *    @OA\JsonContent(
     *     @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/NewsIndexResource")),
     *     @OA\Property(
     *      property="pagination",
     *      @OA\Property(property="total", type="integer", example="16"),
     *      @OA\Property(property="next_url", type="string"),
     *     ),
     *    )
     *  ),
     *  @OA\Response(
     *    response="default",
     *    description="an ""unexpected"" error"
     *  )
     * )
     *
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index()
    {
        $query = News::query()
            ->where(function ($builder) {
                $builder->whereNull('publish_at')->orWhere('publish_at', '<=', now());
            })
            ->where('visibility', true)
            ->orderBy('auto_index');

        $news = $query->clone()->with(['default_image'])->cursorPaginate();
        return response()->json([
            'data' => NewsIndexResource::collection($news->items()),
            'pagination' => [
                'total' => $query->clone()->count(),
                'items' => $news->count(),
                'next_url' => $news->nextPageUrl(),
            ],
        ]);
    }

    /**
     * @OA\Get(
     *  path="/news/{id}",
     *  summary="get news with id",
     *  tags={"news"},
     *  @OA\Parameter(
     *     name="id",
     *     in="path",
     *     required=true
     *  ),
     *  @OA\Response(
     *    response=200,
     *    description="A list with published news",
     *    @OA\JsonContent(ref="#/components/schemas/NewsResource")
     *  ),
     *  @OA\Response(
     *    response=404,
     *    description="not found error"
     *  ),
     *  @OA\Response(
     *    response="default",
     *    description="an ""unexpected"" error"
     *  )
     * )
     *
     *
     * Display the specified resource.
     *
     * @param News $news
     *
     * @return NewsResource
     */
    public function show(News $news): NewsResource
    {
        $news->views()->create([
            'user_id' => auth()->id(),
        ]);
        return new NewsResource($news);
    }
}
