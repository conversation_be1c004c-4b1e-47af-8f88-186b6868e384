<?php

namespace App\Http\Controllers\Mobile;

use App\Models\AwardUser;
use App\Models\AwardCategory;
use App\Enums\AwardStatusEnum;
use App\Settings\AwardSettings;
use App\Http\Controllers\Controller;

class AwardCategoryController extends Controller
{
    public function __construct(private readonly AwardSettings $awardSettings)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = AwardCategory::whereHas('routes', function ($query) {
            $query->where('enabled', true);
        })->get();
        $year = $this->awardSettings->year;
        $auth = auth('api');
        $active = $this->awardSettings->active;
        $canSubmit = $auth->user() && !AwardUser::query()
                ->where('user_id', $auth->id())
                ->where('year', $year)
                ->exists();
        return response()->json([
            'year' => $year,
            'active' => $active,
            'can_submit' => $active && $canSubmit,
            'data' => $categories,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(AwardCategory $awardCategory)
    {
        $auth = auth('api');
        $awardCategory->load(['routes' => fn($q) => $q->where('enabled', true)->orderBy('id')]);
        $year = $this->awardSettings->year;
        $canSubmit = $auth->user() && !AwardUser::query()
                ->where('user_id', $auth->id())
                ->where('year', $year)
                ->exists();
        return response()->json(array_merge($awardCategory->toArray(), [
            'routes' => $awardCategory->routes->map(function ($route) use ($auth, $canSubmit) {
                return [
                    'id' => $route->id,
                    'title' => $route->title,
                    'description' => $route->description,
                    'can_submit' => $canSubmit && $auth->user() && !AwardUser::query()
                            ->where('user_id', $auth->id())
                            ->where('status', AwardStatusEnum::Approved)
                            ->where('award_route_id', $route->id)
                            ->exists(),
                ];
            }),
        ]));
    }
}
