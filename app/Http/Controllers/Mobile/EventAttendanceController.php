<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\EventAttendance;
use App\Models\User;
use App\Permissions\EventPermissions;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EventAttendanceController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Event $event)
    {
        $user = $request->user();
        $via = $request->post('via');
        switch ($via) {
            case 'GPS':
                abort_if(!$event->is_running, Response::HTTP_UNPROCESSABLE_ENTITY, 'لا يمكنك تسجيل الحضور في الوقت الحالي .');
                $event->applied = $event->attendances()->where('user_id', $user->id)->exists();
                if (point2point_distance($event->lat, $event->lng, $request->post('latitude'), $request->post('longitude')) > $event->range)
                    abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'يجب أن تكون في نطاق المكان لتسجيل الحضور !');
                if ($event->self_checkin)
                    $event->can_apply = $event->is_running && !$event->applied && $event->isUserAllowable($user);
                abort_if($event->applied, Response::HTTP_UNPROCESSABLE_ENTITY, 'أنت مسجل بالفعل !');
                abort_if(!$event->can_apply, Response::HTTP_UNPROCESSABLE_ENTITY, 'لا يمكنك تسجيل حضورك !');
                $eventInvitation = $event->invitations()->where('user_id', $user->id)->first();
                $allowable = $event->getAllowableForUser($user);
                abort_if(is_null($allowable), Response::HTTP_UNPROCESSABLE_ENTITY, 'الدعوة غير صالحة !');
                $attendance = $event->attendances()->create([
                    'event_allowable_id' => $allowable->id,
                    'user_id' => $user->id,
                    'via' => $via,
                    'data' => $request->only(['latitude', 'longitude']),
                ]);
                if ($eventInvitation) {
                    $attendance->update(['event_invitation_uuid' => $eventInvitation->uuid]);
                    $eventInvitation->update([
                        'attended_at' => now(),
                        'event_attendance_id' => $attendance->id,
                    ]);
                }
                $this->attendanceCountNotification($event);
                break;
            case 'MANUAL':
                abort_if((
                !in_array($user->id, $event->responsible_users ?: []) /*||
                    !$user->hasPermissionTo(EventPermissions::attendance)*/
                ), Response::HTTP_FORBIDDEN);
                $data = $request->validate([
                    'family_user_id' => ['required', 'numeric'],
                ]);
                $invitedUser = $this->applyViaManual($event, $data['family_user_id']);
                $this->attendanceCountNotification($event);
                return response()->json([
                    'success' => true,
                    'user' => $invitedUser?->only(['id', 'name']),
                ]);
                break;
            case 'QRCODE':
                abort_if((
                !in_array($user->id, $event->responsible_users ?: []) /*||
                    !$user->hasPermissionTo(EventPermissions::attendance)*/
                ), Response::HTTP_FORBIDDEN);
                $request->validate([
                    'via' => ['required', 'string'],
                    'qr_code' => ['required', 'string'],
                ]);
                $qrCode = $request->post('qr_code');
                if (is_numeric($qrCode)) {
                    $invitedUser = $this->applyViaUserQrCode($event, $qrCode);
                } else if (\Str::contains($qrCode, '|')) {
                    $qrData = explode('|', $qrCode);
                    abort_if(count($qrData) !== 2, Response::HTTP_UNPROCESSABLE_ENTITY, 'رمز إستجابة غير صالح !');
                    switch ($qrData[0]) {
                        case 'EventInvitation':
                            $invitedUser = $this->applyViaInvitation($event, $qrData[1]);
                            break;
                        case 'USER':
                        case 'User':
                        case 'user':
                            $invitedUser = $this->applyViaUserQrCode($event, $qrData[1]);
                            break;
                        default:
                            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'رمز إستجابة غير صالح !');
                    }
                } else {
                    abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'إجراء غير صحيح !');
                }
                $this->attendanceCountNotification($event);
                return response()->json([
                    'success' => true,
                    'user' => $invitedUser?->only(['id', 'name']),
                ]);
        }
        return response()->json(['success' => true]);
    }

    public function attendanceCountNotification(Event $event)
    {
        partykit_notify("event-attendances-{$event->id}", [
            'count' => $event->attendances()->count(),
        ]);
    }

    public function applyViaUserQrCode(Event $event, $family_user_id)
    {
        $user = request()->user();
        $invitedUser = User::where('family_user_id', $family_user_id)->first();
        abort_if(!$invitedUser, Response::HTTP_UNPROCESSABLE_ENTITY, 'رقم تعريفي / كود غير صحيح !');
        abort_if($event->attendances()->where('user_id', $invitedUser->id)->exists(), Response::HTTP_UNPROCESSABLE_ENTITY, 'هذا العضو تم تسجيله من قبل !');
        abort_if(!$event->isUserAllowable($invitedUser) && !$event->isUserInvited($invitedUser), Response::HTTP_UNPROCESSABLE_ENTITY, 'لا يوجد دعوة !');
        $allowable = $event->getAllowableForUser($invitedUser);
        abort_if(is_null($allowable), Response::HTTP_UNPROCESSABLE_ENTITY, 'الدعوة غير صالحة !');
        $attendance = $event->attendances()->create([
            'event_allowable_id' => $allowable->id,
            'user_id' => $invitedUser->id,
            'created_by' => $user->id,
            'via' => 'QRCODE',
        ]);
        $eventInvitation = $event->invitations()->where('user_id', $invitedUser->id)->first();
        if ($eventInvitation) {
            $attendance->update(['event_invitation_uuid' => $eventInvitation->uuid]);
            $eventInvitation->update([
                'attended_at' => now(),
                'event_attendance_id' => $attendance->id,
            ]);
        }
        return $invitedUser;
    }

    public function applyViaManual(Event $event, $family_user_id)
    {
        $user = request()->user();
        $invitedUser = User::query()
            ->when(is_null(phone_format($family_user_id)), fn($q) => $q->where('family_user_id', $family_user_id))
            ->when(!is_null(phone_format($family_user_id)), fn($q) => $q->where('phone', phone_format($family_user_id)))
            ->first();
        abort_if(!$invitedUser, Response::HTTP_UNPROCESSABLE_ENTITY, 'رقم تعريفي / رقم جوال صحيح !');
        abort_if($event->attendances()->where('user_id', $invitedUser->id)->exists(), Response::HTTP_UNPROCESSABLE_ENTITY, 'هذا العضو تم تسجيله من قبل !');
        abort_if(!$event->isUserAllowable($invitedUser) && !$event->isUserInvited($invitedUser), Response::HTTP_UNPROCESSABLE_ENTITY, 'لا يوجد دعوة !');
        $allowable = $event->getAllowableForUser($invitedUser);
        abort_if(is_null($allowable), Response::HTTP_UNPROCESSABLE_ENTITY, 'الدعوة غير صالحة !');
        $attendance = $event->attendances()->create([
            'event_allowable_id' => $allowable->id,
            'user_id' => $invitedUser->id,
            'created_by' => $user->id,
            'via' => 'QRCODE',
        ]);
        $eventInvitation = $event->invitations()->where('user_id', $invitedUser->id)->first();
        if ($eventInvitation) {
            $attendance->update(['event_invitation_uuid' => $eventInvitation->uuid]);
            $eventInvitation->update([
                'attended_at' => now(),
                'event_attendance_id' => $attendance->id,
            ]);
        }
        return $invitedUser;
    }

    public function applyViaInvitation(Event $event, $uuid)
    {
        $user = request()->user();
        $eventInvitation = $event->invitations()->with(['user', 'host_user'])->where('uuid', $uuid)->first();
        abort_if(!$eventInvitation, Response::HTTP_UNPROCESSABLE_ENTITY, 'الدعوة غير صالحة !');
        abort_if($event->attendances()
            ->where(
                fn($q) => $q
                    ->where('event_invitation_uuid', $eventInvitation->uuid)
                    ->when($eventInvitation->user_id, fn($query) => $query->orWhere('user_id', $eventInvitation->user_id))
            )
            ->exists(), Response::HTTP_UNPROCESSABLE_ENTITY, 'تم تسجيل الحضور لهذا الضيف من قبل !');
        $invitedUser = $eventInvitation->host_user ?? $eventInvitation->user;
        $data = [];
        if ($invitedUser) {
            $allowable = $event->getAllowableForUser($invitedUser);
            abort_if(is_null($allowable), Response::HTTP_UNPROCESSABLE_ENTITY, 'الدعوة غير صالحة !');
            $data = ['user_id' => $eventInvitation->user?->id];
        }
        $attendance = $event->attendances()->create([
            ...$data,
            'event_invitation_uuid' => $eventInvitation->uuid,
            'event_allowable_id' => $allowable->id,
            'created_by' => $user->id,
            'via' => 'APPLE_PASS',
        ]);
        $eventInvitation->update([
            'attended_at' => now(),
            'event_attendance_id' => $attendance->id,
        ]);
        return $eventInvitation->user ?: new User(['id' => -1, 'name' => $eventInvitation->guest_name]);
    }
}
