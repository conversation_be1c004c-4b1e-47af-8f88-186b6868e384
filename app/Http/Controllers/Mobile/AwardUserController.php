<?php

namespace App\Http\Controllers\Mobile;

use App\Models\AwardUser;
use App\Enums\AwardStatusEnum;
use App\Settings\AwardSettings;
use App\Models\AwardRouteField;
use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\AwardUserStoreRequest;

class AwardUserController extends Controller
{
    public function __construct(private readonly AwardSettings $awardSettings)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $requests = AwardUser::query()
            ->with(['award_route', 'award_route.award_category'])
            ->where('user_id', auth('api')->id())
            ->latest('id')
            ->get();
        return response()->json(
            $requests->map(function ($request) {
                return [
                    'id' => $request->id,
                    'year' => $request->year,
                    'status' => $request->status,
                    'reject_notes' => $request->reject_notes,
                    'status_locale' => $request->status_locale,
                    'award_route' => $request->award_route?->title,
                    'award_category' => $request->award_route?->award_category?->title,
                    'created_at' => $request->created_at,
                ];
            })
        );
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AwardUserStoreRequest $request)
    {
        if (!$this->awardSettings->active)
            return response()->json(['success' => false, 'message' => 'لا يمكن إستقبال الطلبات في الوقت الحالي'], 403);
        $year = $this->awardSettings->year;
        $auth = auth('api');
        if (AwardUser::where('user_id', $auth->id())
            ->where('year', $year)->exists()) {
            return response()->json(['success' => false, 'message' => 'يوجد طلب لهذه السنة، لا يمكن تنفيذ طلبك'], 403);
        }
        $data = $request->validated();
        if (AwardUser::where('user_id', $auth->id())
            ->where('status', AwardStatusEnum::Approved)
            ->where('award_route_id', $data['award_route_id'])->exists()) {
            return response()->json(['success' => false, 'message' => 'يوجد طلب قديم مقبول علي نفس المسار، لا يمكن تنفيذ طلبك'], 403);
        }
        $awardUser = AwardUser::create([
            'user_id' => $auth->id(),
            'award_route_id' => $data['award_route_id'],
            'year' => $year,
            'data' => [],
        ]);
        $awardUserData = [
            'fields' => [],
        ];
        for ($i = 0; $i < count($data['fields']); $i++) {
            $id = $data['fields'][$i]['id'];
            $field = AwardRouteField::find($id);
            if ($field->type === 'FILE') {
                $file = $request->file("fields.{$i}.value");
                if ($file) {
                    $isPdf = $file->getClientMimeType() === 'application/pdf';
                    $fileName = basename($file->storePublicly('public/uploads'));
                    $path = "uploads/{$fileName}";
                    $awardUserData['fields'][$i] = [
                        'id' => $id,
                        'title' => $field->title,
                        'type' => $field->type,
                        'value' => tenant_asset($path),
                        'file_type' => $isPdf ? 'PDF' : 'IMAGE',
                    ];
                }
            } else {
                $awardUserData['fields'][$i] = [
                    'id' => $id,
                    'type' => $field->type,
                    'value' => $data['fields'][$i]['value'],
                ];
            }
        }
        $awardUser->update([
            'data' => $awardUserData,
        ]);
        return response()->json(['message' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AwardUser $awardUser)
    {
        if (
            $awardUser->status !== AwardStatusEnum::New ||
            $awardUser->user_id !== auth('api')->id()
        ) {
            return response()->json(['success' => false, 'message' => 'لا يمكن حذف الطلب'], 403);
        }
        $awardUser->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف الطلب بنجاح']);
    }
}
