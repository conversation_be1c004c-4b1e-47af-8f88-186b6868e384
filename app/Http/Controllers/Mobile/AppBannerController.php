<?php

namespace App\Http\Controllers\Mobile;

use App\Models\AppBanner;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\AppBannerResource;

class AppBannerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $user = $request->user('api');
        $query = AppBanner::query()
            ->when(is_null($user), fn($q) => $q->where('guest', true))
            ->when(!is_null($user), fn($q) => $q->where('authed', true));

        match ($query->driver()) {
            'mariadb', 'mysql' => $query->orderByRaw('ISNULL(index)')->orderBy('index'),
            'pgsql' => $query->orderByRaw('index IS NULL')->orderBy('index'),
        };

        $appBanners = $query->get();
        return response()->json([
            'data' => AppBannerResource::collection($appBanners),
        ]);
    }

    /**
     * increment clicks.
     *
     * @param AppBanner $appBanner
     *
     * @return JsonResponse
     */
    public function click(AppBanner $appBanner)
    {
        $appBanner->increment('clicks');
        return response()->json(['success' => true]);
    }
}
