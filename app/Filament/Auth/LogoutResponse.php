<?php

namespace App\Filament\Auth;

use Filament\Facades\Filament;
use Filament\Http\Responses\Auth\LogoutResponse as BaseLogoutResponse;
use Illuminate\Http\RedirectResponse;

class LogoutResponse extends BaseLogoutResponse
{
    public function toResponse($request): RedirectResponse
    {
        // Get the actual HTTP request from the container
        $httpRequest = request();

        if ($httpRequest->hasHeader('X-Forwarded-Host')) {
            $forwardedHost = $httpRequest->header('X-Forwarded-Host');

            // Clean the host - remove protocol if present
            $host = $this->cleanHost($forwardedHost);

            // Get the intended URL from Filament
            $intendedUrl = Filament::hasLogin() ? Filament::getLoginUrl() : Filament::getUrl();

            // Extract just the path from the intended URL
            $intendedPath = $this->extractPath($intendedUrl);

            return redirect()->to('https://' . $host . $intendedPath);
        }

        return parent::toResponse($request);
    }

    private function cleanHost(string $host): string
    {
        // Remove protocol if present
        $host = preg_replace('/^https?:\/\//', '', $host);

        // Take only the first host if multiple are present (comma-separated)
        $host = explode(',', $host)[0];

        // Trim whitespace
        $host = trim($host);

        // Remove trailing slash if present
        $host = rtrim($host, '/');

        return $host;
    }

    private function extractPath(string $url): string
    {
        // If it's already just a path, return it
        if (str_starts_with($url, '/')) {
            return $url;
        }

        // If it's a full URL, extract just the path
        $parsedUrl = parse_url($url);

        // Get the path, default to /admin if no path
        $path = $parsedUrl['path'] ?? '/admin';

        // Add query string if present
        if (isset($parsedUrl['query'])) {
            $path .= '?' . $parsedUrl['query'];
        }

        // Add fragment if present
        if (isset($parsedUrl['fragment'])) {
            $path .= '#' . $parsedUrl['fragment'];
        }

        // Ensure path starts with /
        if (!str_starts_with($path, '/')) {
            $path = '/' . $path;
        }

        return $path;
    }
}
