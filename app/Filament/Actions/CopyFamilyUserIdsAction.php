<?php

namespace App\Filament\Actions;

use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;

class CopyFamilyUserIdsAction extends BulkAction
{
    protected string $fieldName = 'family_user_id';

    public static function getDefaultName(): ?string
    {
        return 'copy_family_user_ids';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('نسخ الأرقام التعريفية'))
            ->icon('heroicon-o-clipboard')
            ->action(function (Collection $records, $livewire) {
                $this->copyFieldValues($records, $livewire);
            })
            ->deselectRecordsAfterCompletion();
    }

    /**
     * Set the field name to copy from records
     */
    public function field(string $fieldName): static
    {
        $this->fieldName = $fieldName;
        return $this;
    }

    /**
     * Core logic for copying field values to clipboard - single source of truth
     */
    protected function copyFieldValues(Collection $records, $livewire): void
    {
        $values = $records->pluck($this->fieldName)
            ->filter()
            ->unique()
            ->sort()
            ->values()
            ->implode("\n");

        $max = 500;
        if ($records->count() > $max) {
            $livewire->js("
                new window.FilamentNotification()
                    .title('تعذر النسخ')
                    .danger()
                    .body('لايمكن نسخ أكثر من {$max} رقم تعريفي في وقت واحد')
                    .send();
            ");
            return;
        }

        // Create JavaScript to copy to clipboard
        $livewire->js("
        setTimeout(()=>{
            navigator.clipboard.writeText(`{$values}`).then(()=>{
            new window.FilamentNotification()
                .title('تم نسخ الأرقام التعريفية')
                .success()
                .body('تم نسخ الأرقام التعريفية إلى الحافظة')
                .send();
                }).catch(()=>{
                    new window.FilamentNotification()
                        .title('تعذر النسخ')
                        .danger()
                        .body('لايمكن نسخ الأرقام التعريفية')
                        .send();
                });
        }, 0);
        ");
    }

    /**
     * Create the action with optional custom field name
     */
    public static function make(?string $name = null): static
    {
        $static = app(static::class, ['name' => $name ?? static::getDefaultName()]);
        $static->configure();
        return $static;
    }
}
