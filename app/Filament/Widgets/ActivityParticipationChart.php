<?php

namespace App\Filament\Widgets;

use App\Models\Activity;
use App\Permissions\PagePermissions;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class ActivityParticipationChart extends ChartWidget
{
    protected static ?string $heading = 'المشاركة في الأنشطة';
    protected static ?int $sort = 5;
    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        $topActivities = Activity::withCount('members')
            ->orderBy('members_count', 'desc')
            ->limit(10)
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'عدد المشاركين',
                    'data' => $topActivities->pluck('members_count')->toArray(),
                    'backgroundColor' => 'rgba(251, 146, 60, 0.5)',
                    'borderColor' => 'rgb(251, 146, 60)',
                ],
            ],
            'labels' => $topActivities->pluck('title')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                ],
            ],
            'indexAxis' => 'y',
        ];
    }
    public static function canView(): bool
    {
        return auth()->user()->hasPermissionTo(PagePermissions::reports);
    }
}
