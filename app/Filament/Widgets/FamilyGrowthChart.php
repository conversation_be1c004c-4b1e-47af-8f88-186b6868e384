<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Permissions\PagePermissions;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class FamilyGrowthChart extends ChartWidget
{
    protected static ?string $heading = 'نمو العائلة';

    protected static ?int $sort = 1;

    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        $months = collect(range(11, 0))->map(function ($monthsAgo) {
            return Carbon::now()->subMonths($monthsAgo);
        });

        $data = $months->map(function ($date) {
            return User::where('created_at', '<=', $date->endOfMonth())
                ->count();
        });

        return [
            'datasets' => [
                [
                    'label' => 'عدد الأعضاء',
                    'data' => $data->toArray(),
                    'borderColor' => 'rgb(251, 146, 60)',
                    'backgroundColor' => 'rgba(251, 146, 60, 0.1)',
                ],
            ],
            'labels' => $months->map(fn ($date) => $date->format('M Y'))->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    public static function canView(): bool
    {
        return auth()->user()->hasPermissionTo(PagePermissions::reports);
    }
}
