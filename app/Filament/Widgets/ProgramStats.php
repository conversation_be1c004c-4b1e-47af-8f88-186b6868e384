<?php

namespace App\Filament\Widgets;

use App\Models\Activity;
use App\Models\ActivityUser;
use App\Models\User;
use App\Models\UserVoluntary;
use App\Permissions\PagePermissions;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ProgramStats extends BaseWidget
{
    protected ?string $heading = 'البرامج والأنشطة';
    protected static ?int $sort = 5;

    protected function getStats(): array
    {
        // Fix for beneficiaries count
        $beneficiariesCount = ActivityUser::query()
            ->selectRaw('count(DISTINCT user_id) as count')
            ->first();

        $beneficiariesTotal = $beneficiariesCount ? number_format($beneficiariesCount->count) : '0';

        return [
            Stat::make('الأنشطة', number_format(Activity::count()) . ' نشاط')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('primary')
                ->chart([3, 5, 8, 12, 16, 18, 22]),

            Stat::make('ساعات التطوع', number_format(floatval(UserVoluntary::sum('total')), 0) . ' ساعة')
                ->descriptionIcon('heroicon-m-clock')
                ->color('emerald') // Using emerald for volunteer hours
                ->chart([10, 25, 45, 60, 75, 90, 120]),

            Stat::make('المتطوعون', number_format(User::query()->whereHas('voluntaries')->count()) . ' متطوع')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('amber') // Using amber for volunteers
                ->chart([5, 8, 12, 15, 18, 22, 28]),

            Stat::make('المستفيدون بالبرامج التنموية', $beneficiariesTotal . ' مستفيد')
                ->descriptionIcon('heroicon-m-users')
                ->color('pink') // Using pink for beneficiaries
                ->chart([8, 15, 25, 35, 45, 55, 68]),
        ];
    }

    public static function canView(): bool
    {
        return auth()->user()->hasPermissionTo(PagePermissions::reports);
    }
}
