<?php

namespace App\Filament\Widgets;

use App\Models\BankAccountTransaction;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class AnonymousTransactionsChart extends ChartWidget
{
    protected static ?string $heading = 'المعاملات المجهولة - آخر 30 يوم';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $data = [];
        $labels = [];

        // Get data for the last 30 days
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $labels[] = $date->format('M d');
            
            $count = BankAccountTransaction::where('anonymous', true)
                ->where('is_verified', false)
                ->whereDate('created_at', $date)
                ->count();
                
            $data[] = $count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'المعاملات المجهولة',
                    'data' => $data,
                    'borderColor' => '#3b82f6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                ],
            ],
        ];
    }
}
