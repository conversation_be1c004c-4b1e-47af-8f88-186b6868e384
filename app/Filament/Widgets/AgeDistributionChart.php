<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Permissions\PagePermissions;
use Filament\Widgets\ChartWidget;

class AgeDistributionChart extends ChartWidget
{
    protected static ?string $heading = 'التوزيع العمري';

    protected static ?int $sort = 3;

    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        $ageGroups = [
            '0-10' => User::whereNotNull('dob')
                ->whereRaw('EXTRACT(YEAR FROM AGE(dob)) BETWEEN 0 AND 10')
                ->count(),
            '11-20' => User::whereNotNull('dob')
                ->whereRaw('EXTRACT(YEAR FROM AGE(dob)) BETWEEN 11 AND 20')
                ->count(),
            '21-30' => User::whereNotNull('dob')
                ->whereRaw('EXTRACT(YEAR FROM AGE(dob)) BETWEEN 21 AND 30')
                ->count(),
            '31-40' => User::whereNotNull('dob')
                ->whereRaw('EXTRACT(YEAR FROM AGE(dob)) BETWEEN 31 AND 40')
                ->count(),
            '41-50' => User::whereNotNull('dob')
                ->whereRaw('EXTRACT(YEAR FROM AGE(dob)) BETWEEN 41 AND 50')
                ->count(),
            '51-60' => User::whereNotNull('dob')
                ->whereRaw('EXTRACT(YEAR FROM AGE(dob)) BETWEEN 51 AND 60')
                ->count(),
            '60+' => User::whereNotNull('dob')
                ->whereRaw('EXTRACT(YEAR FROM AGE(dob)) > 60')
                ->count(),
        ];

        return [
            'datasets' => [
                [
                    'label' => 'عدد الأعضاء',
                    'data' => array_values($ageGroups),
                    'backgroundColor' => [
                        'rgba(255, 99, 132, 0.5)',
                        'rgba(54, 162, 235, 0.5)',
                        'rgba(255, 206, 86, 0.5)',
                        'rgba(75, 192, 192, 0.5)',
                        'rgba(153, 102, 255, 0.5)',
                        'rgba(255, 159, 64, 0.5)',
                        'rgba(199, 199, 199, 0.5)',
                    ],
                ],
            ],
            'labels' => array_keys($ageGroups),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
    public static function canView(): bool
    {
        return auth()->user()->hasPermissionTo(PagePermissions::reports);
    }
}
