<?php

namespace App\Filament\Widgets;

use App\Models\BankAccountTransaction;
use App\Models\BankTransactionImport;
use App\Models\BankAccount;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SupportingStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        // Anonymous transactions stats
        $anonymousTransactionsCount = BankAccountTransaction::where('anonymous', true)
            ->where('is_verified', false)
            ->count();

        $anonymousTransactionsAmount = BankAccountTransaction::where('anonymous', true)
            ->where('is_verified', false)
            ->sum('amount');

        $recentAnonymousTransactions = BankAccountTransaction::where('anonymous', true)
            ->where('is_verified', false)
            ->where('created_at', '>=', now()->subDays(7))
            ->count();

        // Bank transaction imports stats
        $pendingImports = BankTransactionImport::whereNull('completed_at')->count();
        $completedImports = BankTransactionImport::whereNotNull('completed_at')->count();
        $totalImportItems = BankTransactionImport::withCount('items')->get()->sum('items_count');

        // Refunds stats
        $refundsCount = BankAccountTransaction::where('anonymous', true)
            ->where('is_refund', true)
            ->count();

        $refundsAmount = BankAccountTransaction::where('anonymous', true)
            ->where('is_refund', true)
            ->sum('amount');

        return [
            Stat::make('المعاملات المجهولة', $anonymousTransactionsCount)
                ->description('المعاملات غير المحققة')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('primary')
                ->chart([7, 12, 8, 15, 10, 18, $recentAnonymousTransactions]),

            Stat::make('إجمالي المبالغ المجهولة', number_format($anonymousTransactionsAmount, 2) . ' ريال')
                ->description('المبلغ الإجمالي للمعاملات المجهولة')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            Stat::make('الاستيرادات المعلقة', $pendingImports)
                ->description('استيرادات في انتظار التأكيد')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('الاستيرادات المكتملة', $completedImports)
                ->description('استيرادات تم تأكيدها')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('عناصر الاستيراد', $totalImportItems)
                ->description('إجمالي العناصر المستوردة')
                ->descriptionIcon('heroicon-m-list-bullet')
                ->color('info'),

            Stat::make('المبالغ المستردة', number_format($refundsAmount, 2) . ' ريال')
                ->description($refundsCount . ' عملية استرداد')
                ->descriptionIcon('heroicon-m-arrow-uturn-left')
                ->color('danger'),
        ];
    }

    protected function getColumns(): int
    {
        return 3;
    }
}
