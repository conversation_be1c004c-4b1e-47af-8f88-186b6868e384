<?php

namespace App\Filament\Widgets;

use App\Models\BankTransactionImport;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class BankTransactionImportsWidget extends BaseWidget
{
    protected static ?string $heading = 'آخر الاستيرادات';

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                BankTransactionImport::query()
                    ->with(['bank_account', 'user'])
                    ->latest()
                    ->limit(5)
            )
            ->columns([
                Tables\Columns\TextColumn::make('file_name')
                    ->label('اسم الملف')
                    ->searchable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('bank_account.title')
                    ->label('الحساب البنكي')
                    ->badge(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('المستخدم'),

                Tables\Columns\TextColumn::make('items_count')
                    ->label('العناصر')
                    ->numeric()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('items_sum_amount')
                    ->label('المبلغ')
                    ->riyal(),

                Tables\Columns\TextColumn::make('completed_at')
                    ->label('الحالة')
                    ->formatStateUsing(fn ($state) => $state ? 'مكتمل' : 'معلق')
                    ->badge()
                    ->color(fn ($state) => $state ? 'success' : 'warning'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->since()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('عرض')
                    ->icon('heroicon-o-eye')
                    ->url(fn (BankTransactionImport $record): string =>
                        \App\Filament\Resources\BankTransactionImportResource::getUrl('view', ['record' => $record])
                    ),
            ]);
    }
}
