<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\SupportingStatsWidget;
use App\Filament\Widgets\AnonymousTransactionsChart;
use App\Filament\Widgets\BankTransactionImportsWidget;
use Filament\Pages\Page;

class SupportingDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-heart';

    protected static string $view = 'filament.pages.supporting-dashboard';

    protected static ?string $title = 'لوحة تحكم الدعم';

    protected static ?string $navigationLabel = 'لوحة تحكم الدعم';

    protected static ?string $navigationGroup = 'الدعم';

    protected static ?int $navigationSort = 0;

    protected function getHeaderWidgets(): array
    {
        return [
            SupportingStatsWidget::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            AnonymousTransactionsChart::class,
            BankTransactionImportsWidget::class,
        ];
    }

    public function getHeaderWidgetsColumns(): int | array
    {
        return 3;
    }

    public function getFooterWidgetsColumns(): int | array
    {
        return 2;
    }

    public static function canAccess(): bool
    {
        return auth()->user()->can('supporters.index') ||
               auth()->user()->can('bank-transaction-import.index');
    }
}
