<?php

namespace App\Filament\Exports;

use App\Enums\UserMaritalStatus as EnumsUserMaritalStatus;
use App\Models\User;
use App\Permissions\UserPermissions;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;

class UserExporter extends Exporter
{
    protected static ?string $model = User::class;

    public static function getColumns(): array
    {
        $columns = [
            ExportColumn::make('family_user_id')
                ->label(__('userColumns.family_user_id')),
            ExportColumn::make('full_name')
                ->label(__('userColumns.full_name')),
            ExportColumn::make('gender')
                ->label(__('userColumns.gender'))
                ->formatStateUsing(fn($state) => $state === 'MALE' ? __('ذكر') : __('أنثى')),
            ExportColumn::make('dob')
                ->label(__('userColumns.dob'))
                ->formatStateUsing(fn($state) => $state ? $state->format('Y-m-d') : ''),
            ExportColumn::make('marital_status')
                ->label(__('userColumns.marital_status'))
                ->formatStateUsing(function ($state, $record) {
                    if (! $state) {
                        return null;
                    }
                    $options = EnumsUserMaritalStatus::options($record->gender);

                    return $options[$state] ?? $state;
                }),
            ExportColumn::make('educational_status')
                ->label(__('userColumns.educational_status')),
            ExportColumn::make('health_status')
                ->label(__('userColumns.health_status')),
            ExportColumn::make('user_region.title_ar')
                ->label(__('userColumns.user_region_id')),
            ExportColumn::make('created_at')
                ->label(__('created_at'))
                ->formatStateUsing(fn($state) => $state ? $state->format('Y-m-d') : ''),
        ];

        // Add phone number column if user has permission
        if (auth()->user() && auth()->user()->hasPermissionTo(UserPermissions::viewPhone)) {
            $columns[] = ExportColumn::make('phone')
                ->label(__('userColumns.phone'));
        }

        // Add email column if user has permission
        if (auth()->user() && auth()->user()->hasPermissionTo(UserPermissions::viewPhone)) {
            $columns[] = ExportColumn::make('email')
                ->label(__('userColumns.email'));
        }

        return $columns;
    }

    public function getFileName(Export $export): string
    {
        return 'users-' . date('Y-m-d');
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        return __('بيانات المستخدمين جاهزة للتحميل');
    }

    public static function modifyQuery(Builder $query): Builder
    {
        return $query->with(['user_region']);
    }
}
