<?php

namespace App\Filament\Resources;

use Illuminate\Support\Facades\Vite;
use App\Filament\Resources\BankAccountTransactionResource\Pages\ViewBankAccountTransaction;
use App\Filament\Actions\CopyFamilyUserIdsAction;
use App\Filament\Resources\BankAccountTransactionUserResource\Pages;
use App\Filament\Resources\BankAccountTransactionUserResource\RelationManagers\BankAccountTransactionsRelationManager;
use App\Models\BankAccount;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Fieldset;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use App\Models\BankAccountTransaction;
use App\Filament\Resources\BankAccountTransactionUserResource\Pages as BankAccountTransactionUserPages;


class BankAccountTransactionUserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationLabel = 'الداعمون';
    protected static ?string $navigationGroup = 'المالية';
    protected static ?string $modelLabel = 'داعم';
    protected static ?string $pluralModelLabel = 'الداعمون';
    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('الاسم')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('profile_photo_url')
                    ->withFallbackAvatar()
                    ->label('')
                    ->circular()
                    ->size(40),
                Tables\Columns\TextColumn::make('shortFullName')
                    ->label('الاسم')
                    ->url(fn(User $record) => UserResource::getUrl('view', ['record' => $record->id]))
                    ->color('primary')
                    ->searchable(),

                Tables\Columns\TextColumn::make('transactions_count')
                    ->numeric(locale: 'en-US')
                    ->label('عدد الحوالات')
                    ->counts('transactions')
                    ->sortable(),

                Tables\Columns\TextColumn::make('transactions_sum')
                    ->label('إجمالي المبالغ')
                    ->riyal()
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy(
                            BankAccountTransaction::query()
                                ->whereColumn('user_id', 'users.id')
                                ->selectRaw('SUM(amount)'),
                            $direction
                        );
                    })
                    ->state(function (User $record): float {
                        return $record->transactions()->sum('amount');
                    })
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->label('عرض'),
                Action::make('createTransaction')
                    ->label('إضافة حوالة')
                    ->icon('heroicon-o-plus-circle')
                    ->form([
                        TextInput::make('amount')
                            ->label('المبلغ')
                            ->numeric()
                            ->required(),
                        Forms\Components\TextInput::make('reference')
                            ->label('الرقم المرجعي'),
                        Forms\Components\Select::make('bank_account_id')
                            ->model(BankAccountTransaction::class)
                            ->relationship('bank_account', 'title') // Corrected relationship name
                            ->label('محولة على حساب')
                            ->required(),
                        Forms\Components\Select::make('account')
                            ->options(function ($record) {
                                return BankAccountTransaction::query()
                                    ->where('user_id', $record->id)
                                    ->whereNotNull('account')
                                    ->distinct()
                                    ->pluck('account', 'account')
                                    ->toArray();
                            })
                            ->searchable()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('account')
                                    ->label('رقم حساب الداعم')
                                    ->maxLength(191)
                                    ->required()
                            ])
                            ->createOptionUsing(function (array $data) {
                                return $data['account'];
                            })
                            ->label('رقم الحساب البنكي'),
                        Forms\Components\Toggle::make('is_verified')
                            ->label('تم التحقق')
                            ->hidden(true)
                            ->default(true),
                    ])
                    ->action(function (array $data, User $record): void {
                        $record->transactions()->create($data);
                        Notification::make()
                            ->title('تم إضافة التحويل بنجاح')
                            ->success()
                            ->send();
                    }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()->label('حذف المحدد'),
                ]),
                CopyFamilyUserIdsAction::make()->field('family_user_id'),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make(3)
                    ->schema([
                        // Main content (span 2 columns)
                        Section::make('بيانات المستخدم')
                            ->schema([
                                TextEntry::make('shortFullName')
                                    ->label('الاسم')
                                    ->url(fn(User $record) => UserResource::getUrl('view', ['record' => $record->id]))
                                    ->weight(FontWeight::Bold),

                                Grid::make(2)
                                    ->schema([]),
                            ])
                            ->columnSpan(2),

                        // Sidebar content (span 1 column)
                        Grid::make(1)
                            ->schema([
                                Section::make('ملخص الحوالات')
                                    ->schema([
                                        TextEntry::make('transactions_count')
                                            ->label('عدد الحوالات')
                                            ->state(function (User $record): int {
                                                return $record->transactions()->count();
                                            })
                                            ->icon('heroicon-o-calculator'),

                                        TextEntry::make('transactions_sum')
                                            ->label('إجمالي المبالغ')
                                            ->numeric(locale: 'en-US')
                                            ->state(function (User $record): float {
                                                return $record->transactions()->sum('amount');
                                            })
                                            ->color(fn($state) => $state > 0 ? 'success' : ($state < 0 ? 'danger' : 'gray'))
                                            ->icon('heroicon-o-currency-dollar'),

                                        TextEntry::make('last_transaction')
                                            ->label('آخر حوالة')
                                            ->state(function (User $record) {
                                                $lastTransaction = $record->transactions()->latest('created_at')->first();
                                                return $lastTransaction ? $lastTransaction->created_at : null;
                                            })
                                            ->date()
                                            ->placeholder('لا توجد حوالات')
                                            ->icon('heroicon-o-calendar'),

                                        Fieldset::make('معلومات النظام')
                                            ->schema([
                                                TextEntry::make('created_at')
                                                    ->label('تاريخ الإنشاء')
                                                    ->date()
                                                    ->dateTimeTooltip(),

                                                TextEntry::make('updated_at')
                                                    ->label('تاريخ التحديث')
                                                    ->date()
                                                    ->dateTimeTooltip(),
                                            ]),
                                    ]),
                            ])
                            ->columnSpan(1),
                    ])
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('transactions')
            ->withCount('transactions')
            ->withSum('transactions', 'amount');
    }

    public static function getRelations(): array
    {
        return [
            BankAccountTransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankAccountTransactionUsers::route('/'),
            'view' => Pages\ViewBankAccountTransactionUser::route('/{record}'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }
}
