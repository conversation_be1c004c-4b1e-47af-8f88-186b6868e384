<?php

namespace App\Filament\Resources\LiteratureCategoryResource\Pages;

use App\Filament\Resources\LiteratureCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateLiteratureCategory extends CreateRecord
{
    protected static string $resource = LiteratureCategoryResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return __('literatureCategory.messages.created');
    }
}