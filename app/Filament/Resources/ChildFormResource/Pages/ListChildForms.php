<?php

namespace App\Filament\Resources\ChildFormResource\Pages;

use App\Filament\Resources\ChildFormResource;
use Archilex\AdvancedTables\AdvancedTables;
use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\CreateAction;

class ListChildForms extends ListRecords
{
    use AdvancedTables;
    protected static string $resource = ChildFormResource::class;

    protected static ?string $navigationLabel = 'طلبات الإنضمام';

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function getSubNavigation(): array
    {
        return UserResource::getRecordSubNavigation($this);
    }
}
