<?php

namespace App\Filament\Resources\ChildFormResource\Pages;

use App\Enums\StatusEnum;
use App\Filament\Resources\ChildFormResource;
use App\Filament\Traits\RedirectsToViewRecord;
use App\Models\User;
use DB;
use Exception;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Validation\ValidationException;

class EditChildForm extends EditRecord
{
    use RedirectsToViewRecord;
    protected static string $resource = ChildFormResource::class;

    public function afterSave(): void
    {
        $childForm = $this->record;

        if ($childForm->user_id) {
            $this->redirect($this->getResource()::getUrl('index'));
        }

        try {
            DB::beginTransaction();
            $user = User::create($childForm->only([
                'name',
                'gender',
                'father_id',
                'mother_id',
                'dob',
                'national_id',
                'phone',
                'marital_status',
                'health_status',
                'educational_status',
                'user_region_id',
            ]));
            $childForm->update([
                'user_id' => $user->id,
                'status' => StatusEnum::Approved,
            ]);
            DB::commit();
            $this->redirect($this->getResource()::getUrl('index'));
        } catch (UniqueConstraintViolationException $exception) {
            DB::rollBack();
            throw ValidationException::withMessages([
                'name' => [$exception->getMessage()],
            ]);
        } catch (Exception $exception) {
            DB::rollBack();
            throw ValidationException::withMessages([
                'name' => [$exception->getMessage()],
            ]);
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getSaveFormAction(): Actions\Action
    {
        return Actions\Action::make('save')
            ->label('قبول الطلب')
            ->submit('save')
            ->keyBindings(['mod+s']);
    }
}
