<?php

namespace App\Filament\Resources\ServiceResource\Widgets;

use App\Enums\HallReservationStatus;
use App\Models\HallReservation;
use App\Models\Service;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class ServiceStatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        // Total services count
        $totalServices = Service::count();

        // Total accepted service requests (confirmed + completed reservations with services)
        $acceptedServiceRequests = HallReservation::whereIn('status', [
            HallReservationStatus::Confirmed,
            HallReservationStatus::Completed
        ])
            ->whereHas('services')
            ->count();

        // Most requested accepted service
        $mostRequestedService = Service::select('services.id', 'services.title', DB::raw('COUNT(hall_reservation_service.service_id) as request_count'))
            ->join('hall_reservation_service', 'services.id', '=', 'hall_reservation_service.service_id')
            ->join('hall_reservations', 'hall_reservation_service.hall_reservation_id', '=', 'hall_reservations.id')
            ->whereIn('hall_reservations.status', [
                HallReservationStatus::Confirmed,
                HallReservationStatus::Completed
            ])
            ->groupBy('services.id', 'services.title')
            ->orderByDesc('request_count')
            ->first();

        return [
            Stat::make('إجمالي الخدمات', $totalServices)
                ->description('عدد الخدمات المتاحة')
                ->descriptionIcon('heroicon-m-wrench-screwdriver', IconPosition::Before)
                ->color('info'),

            Stat::make('إجمالي طلبات الخدمات المقبولة', $acceptedServiceRequests)
                ->description('الحجوزات المؤكدة والمكتملة التي تحتوي على خدمات')
                ->descriptionIcon('heroicon-m-check-circle', IconPosition::Before)
                ->color('success'),

            Stat::make('أكثر الخدمات طلباً المقبولة', $mostRequestedService ? $mostRequestedService->title : 'لا يوجد')
                ->description($mostRequestedService ? "تم طلبها {$mostRequestedService->request_count} مرة" : 'لا توجد خدمات مطلوبة بعد')
                ->descriptionIcon('heroicon-m-trophy', IconPosition::Before)
                ->color($mostRequestedService ? 'warning' : 'gray'),
        ];
    }
}
