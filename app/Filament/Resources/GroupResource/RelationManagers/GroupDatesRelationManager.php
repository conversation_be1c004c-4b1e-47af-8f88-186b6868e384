<?php

namespace App\Filament\Resources\GroupResource\RelationManagers;

use App\Models\GroupDate;
use Archilex\AdvancedTables\AdvancedTables;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Archilex\AdvancedTables\Filters\DateFilter;
use Archilex\AdvancedTables\Filters\NumericFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use niklasravnsborg\LaravelPdf\Facades\Pdf;

class GroupDatesRelationManager extends RelationManager
{
    use AdvancedTables;
    protected static string $relationship = 'group_dates';

    protected static ?string $title = 'مواعيد الاجتماعات';

    protected static ?string $modelLabel = 'اجتماع';

    protected static ?string $pluralModelLabel = 'الاجتماعات';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('معلومات الاجتماع'))
                    ->schema([
                        Forms\Components\DatePicker::make('schedule_at')
                            ->label(__('موعد الاجتماع'))
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->native(false),
                    ]),

                Forms\Components\Section::make(__('التذكير'))
                    ->schema([
                        Forms\Components\Textarea::make('remind_txt')
                            ->label(__('نص الرسالة التذكيرية'))
                            ->rows(3)
                            ->maxLength(1000)
                            ->helperText(__('سيتم إرسال هذه الرسالة للأعضاء كتذكير بالاجتماع')),

                        Forms\Components\DateTimePicker::make('remind_at')
                            ->label(__('موعد إرسال التذكير'))
                            ->native(false)
                            ->helperText(__('متى يجب إرسال التذكير للأعضاء')),
                    ])
                    ->collapsible()
                    ->columns(1),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('schedule_at')
            ->columns([
                Tables\Columns\TextColumn::make('schedule_at')
                    ->label(__('موعد اللجنة'))
                    ->date('d/m/Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('attendance_count')
                    ->label(__('عدد الحضور'))
                    ->state(fn(GroupDate $record) => is_array($record->attendance_id) ? count($record->attendance_id) : 0)
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('تاريخ الإضافة'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('done_at')
                    ->label(__('تسجيل الحضور'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('report.name')
                    ->label(__('رقم المحضر')),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'schedule_at',
                    'attendance_count',
                    'created_at',
                    'done_at',
                ])->filters([
                    Tables\Filters\Filter::make('upcoming')
                        ->label(__('الاجتماعات القادمة'))
                        ->query(fn(Builder $query): Builder => $query->where('schedule_at', '>', now())),

                    DateFilter::make('schedule_at')
                        ->label(__('موعد الاجتماع')),

                    DateFilter::make('done_at')
                        ->label(__('تاريخ تسجيل الحضور')),
                ])->defaultFilters([]),
            ])
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label(__('إضافة اجتماع'))
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['user_id'] = Auth::id();

                        return $data;
                    }),
            ])
            ->actions([

                Tables\Actions\Action::make('create_report')
                    ->label(__('إنشاء تقرير'))
                    ->icon('heroicon-o-document-text')
                    ->color('primary')
                    ->visible(fn(GroupDate $record) => !is_null($record->done_at) && !$record->reports()->exists())
                    ->form($this->getReportFormSchema())
                    ->action(function (GroupDate $record, array $data): void {

                        // Create the report
                        $record->group->reports()->create([
                            'group_date_id' => $record->id,
                            'title' => $data['title'],
                            'location' => $data['location'],
                            'discussions' => $data['discussions'],
                            'decisions' => $data['decisions'],
                            'user_id' => Auth::id(),
                        ]);

                        // Show success notification
                        \Filament\Notifications\Notification::make()
                            ->title(__('تم إنشاء التقرير بنجاح'))
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('edit_report')
                    ->label(__('تعديل التقرير'))
                    ->icon('heroicon-o-pencil-square')
                    ->color('warning')
                    ->visible(fn(GroupDate $record) => $record->reports()->exists())
                    ->form($this->getReportFormSchema())
                    ->fillForm(function (GroupDate $record): array {
                        $report = $record->report()->first();
                        if (!$report) {
                            return [];
                        }
                        return [
                            'title' => $report->title,
                            'location' => $report->location,
                            'discussions' => $report->discussions,
                            'decisions' => $report->decisions,
                        ];
                    })
                    ->action(function (GroupDate $record, array $data): void {
                        $report = $record->reports()->first();
                        if (!$report) {
                            \Filament\Notifications\Notification::make()
                                ->title(__('التقرير غير موجود'))
                                ->danger()
                                ->send();
                            return;
                        }

                        // Update the report
                        $report->update([
                            'title' => $data['title'],
                            'location' => $data['location'],
                            'discussions' => $data['discussions'],
                            'decisions' => $data['decisions'],
                        ]);

                        // Show success notification
                        \Filament\Notifications\Notification::make()
                            ->title(__('تم تحديث التقرير بنجاح'))
                            ->success()
                            ->send();
                    }),


                Tables\Actions\Action::make('report')
                    ->label(__('التقرير'))
                    ->icon('heroicon-o-document-text')
                    ->color('primary')
                    ->action(function (GroupDate $record) {
                        $groupReport = $record->report()->first();
                        if (!$groupReport) {
                            Notification::make()
                                ->title(__('لا يوجد تقرير لهذا الاجتماع'))
                                ->danger()
                                ->send();
                            return;
                        }
                        $group = $record->group;
                        $groupReport->load([
                            'group_date',
                            'group_date.attendance',
                            'group_date.attendance.father',
                            'group_date.attendance.father.father',
                        ]);
                        $height = 150;
                        $response = (Pdf::loadView('group.report.pdf', compact('groupReport', 'group', 'height')));
                        //return view('group.report.pdf', compact('groupReport', 'group', 'height'))->render();
                        $pdf = $response->output();
                        return response()->streamDownload(function () use ($pdf) {
                            echo $pdf;
                        }, 'محضر إجتماع ' . $group->name . '.pdf');
                    })
                    ->visible(fn(GroupDate $record) => $record->reports()->exists()),

                Tables\Actions\Action::make('attendance')
                    ->label(__('الحضور'))
                    ->icon('heroicon-o-user-group')
                    ->color('success')

                    ->form([
                        Forms\Components\CheckboxList::make('attendance_id')
                            ->label(__('الأعضاء الحاضرون'))
                            ->options(function () {
                                return $this->getOwnerRecord()
                                    ->users()
                                    ->with('user')
                                    ->get()
                                    ->pluck('user.full_name', 'user_id');
                            })
                            ->formatStateUsing(function (GroupDate $record) {
                                return array_map('intval', array_values(array_filter($record->attendance_id ?? [])));
                            })
                            ->columns(2),

                        Forms\Components\Textarea::make('done_txt')
                            ->label(__('رسالة شكر للحاضرين'))
                            ->rows(3)
                            ->maxLength(1000)
                            ->helperText(__('سيتم إرسال هذه الرسالة للأعضاء الحاضرين')),
                    ])
                    ->action(function (GroupDate $record, array $data): void {
                        $record->update([
                            'attendance_id' => array_map('intval', array_values(array_filter($data['attendance_id'] ?? []))),
                            'done_at' => now(),
                            'done_txt' => $data['done_txt'] ?? null,
                        ]);

                        $this->getOwnerRecord()->touch();
                    }),
            ])
            ->defaultSort('schedule_at', 'desc');
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return true;
    }

    public function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    private function getReportFormSchema(): array
    {
        return [
            Forms\Components\Section::make(__('معلومات التقرير'))
                ->schema([
                    Forms\Components\TextInput::make('title')
                        ->label(__('عنوان الاجتماع'))
                        ->required()
                        ->minLength(2)
                        ->maxLength(48)
                        ->placeholder(__('مثال: اجتماع اللجنة الشهري')),

                    Forms\Components\TextInput::make('location')
                        ->label(__('مكان الاجتماع'))
                        ->required()
                        ->minLength(4)
                        ->maxLength(32)
                        ->placeholder(__('مثال: مقر الصندوق')),
                ])->columns(2),

            Forms\Components\Section::make(__('محتوى التقرير'))
                ->schema([
                    Forms\Components\Repeater::make('discussions')
                        ->label(__('محاور الاجتماع'))
                        ->simple(
                            Forms\Components\Textarea::make('discussion')
                                ->required()
                                ->live()
                                ->minLength(5)
                                ->maxLength(512)
                                ->rows(1)
                                ->placeholder(__('اكتب محور الاجتماع هنا...'))
                        )
                        ->minItems(1)
                        ->defaultItems(1)
                        ->addActionLabel(__('إضافة محور'))
                        ->reorderableWithButtons(),

                    Forms\Components\Repeater::make('decisions')
                        ->label(__('قرارات وتوصيات الاجتماع'))
                        ->simple(
                            Forms\Components\Textarea::make('decision')
                                ->required()
                                ->live()
                                ->minLength(5)
                                ->maxLength(512)
                                ->rows(1)
                                ->placeholder(__('اكتب القرار أو التوصية هنا...'))
                        )
                        ->minItems(1)
                        ->defaultItems(1)
                        ->addActionLabel(__('إضافة قرار/توصية'))
                        ->reorderableWithButtons(),
                ]),
        ];
    }
}
