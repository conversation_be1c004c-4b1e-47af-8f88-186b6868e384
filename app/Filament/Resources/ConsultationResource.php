<?php

namespace App\Filament\Resources;

use App\Filament\Actions\CopyFamilyUserIdsAction;
use App\Filament\Components\UserFilter;
use App\Filament\Components\UserSelect;
use App\Filament\Resources\ConsultationResource\Pages;
use App\Filament\Resources\ConsultationResource\RelationManagers;
use App\Filament\Resources\ConsultationCategoryResource\Pages\ListConsultationCategories;
use App\Models\Consultation;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ConsultationResource extends Resource
{
    protected static ?string $model = Consultation::class;
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    protected static ?string $navigationGroup = 'الخدمات';
    protected static ?string $navigationLabel = 'الاستشارات';
    protected static ?string $modelLabel = 'استشارة';
    protected static ?string $pluralModelLabel = 'استشارات';
    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->columnSpanFull()
                    ->schema([
                        Forms\Components\Section::make(__('معلومات الاستشارة'))
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        UserSelect::make('user_id')
                                            ->label(__('consultationColumns.user'))
                                            ->searchable()
                                            ->required(),

                                        Forms\Components\Select::make('category_id')
                                            ->label(__('consultationColumns.category'))
                                            ->relationship('category', 'title', fn($query) => $query->where('type', 'CONSULTATION'))
                                            ->searchable()
                                            ->preload()
                                            ->required()
                                            ->createOptionForm([
                                                Forms\Components\TextInput::make('title')
                                                    ->label(__('consultationCategory.fields.title'))
                                                    ->required()
                                                    ->maxLength(255),
                                                Forms\Components\Hidden::make('type')
                                                    ->default('CONSULTATION'),
                                                Forms\Components\Hidden::make('index')
                                                    ->default(0),
                                            ]),

                                        Forms\Components\TextInput::make('major')
                                            ->label(__('consultationColumns.major'))
                                            ->required()
                                            ->maxLength(255)
                                            ->columnSpanFull(),

                                        Forms\Components\Select::make('status')
                                            ->label(__('consultationColumns.status'))
                                            ->options([
                                                'NEW' => __('جديد'),
                                                'WAITING' => __('بالانتظار'),
                                                'SCHEDULED' => __('مجدولة'),
                                                'REJECTED' => __('مرفوضة'),
                                                'DONE' => __('تمت')
                                            ])
                                            ->required()
                                            ->default('NEW'),

                                        Forms\Components\Textarea::make('details')
                                            ->label(__('consultationColumns.details'))
                                            ->required()
                                            ->maxLength(5120)
                                            ->rows(4)
                                            ->columnSpanFull(),
                                    ]),
                            ])
                            ->columns(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('major')
                    ->label('العنوان'),
                Tables\Columns\TextColumn::make('status')
                    ->label('الحالة')
                    ->badge()
                    ->formatStateUsing(fn($state) => match ($state) {
                        'NEW' => 'جديد',
                        'WAITING' => 'بالانتظار',
                        'SCHEDULED' => 'مجدولة',
                        'REJECTED' => 'مرفوضة',
                        'DONE' => 'تمت',
                        default => $state,
                    })
                    ->colors([
                        'warning' => 'NEW',
                        'info' => 'WAITING',
                        'primary' => 'SCHEDULED',
                        'danger' => 'REJECTED',
                        'success' => 'DONE',
                    ]),
                Tables\Columns\TextColumn::make('category.title')
                    ->label('التصنيف'),
                Tables\Columns\TextColumn::make('user.fullname')
                    ->url(fn($record) => route('filament.admin.resources.users.view', $record->user_id))
                    ->color('primary')
                    ->label('المستخدم'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'major',
                    'category.title',
                    'user.fullname',
                    'updated_at',
                    'created_at',
                ])->filters([
                    UserFilter::make("user.fullname")->label("المستخدم")->setUserRelationshipName("user"),
                    Tables\Filters\SelectFilter::make('status')
                        ->label("الحالة")
                        ->options([
                            'NEW' => 'جديد',
                            'WAITING' => 'بالانتظار',
                            'SCHEDULED' => 'مجدولة',
                            'REJECTED' => 'مرفوضة',
                            'DONE' => 'تمت'
                        ]),
                    Tables\Filters\SelectFilter::make('category.title')
                        ->label("التصنيف")
                        ->relationship('category', 'title', fn($query) => $query->where('type', 'CONSULTATION')),
                ])->defaultFilters([]),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->visible(fn($record) => is_null($record->deleted_at)),
                    Tables\Actions\EditAction::make()
                        ->visible(fn($record) => is_null($record->deleted_at)),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ForceDeleteAction::make(),
                ]),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
                CopyFamilyUserIdsAction::make()
                    ->field('user.family_user_id'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ConsultantsRelationManager::class,
            RelationManagers\CommentsRelationManager::class,
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        if (
            $page instanceof Pages\ListConsultations ||
            $page instanceof ListConsultationCategories
        )
            return $page->generateNavigationItems([
                Pages\ListConsultations::class,
                ListConsultationCategories::class,
            ]);
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListConsultations::route('/'),
            'create' => Pages\CreateConsultation::route('/create'),
            'edit' => Pages\EditConsultation::route('/{record}/edit'),
            'view' => Pages\ViewConsultation::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
