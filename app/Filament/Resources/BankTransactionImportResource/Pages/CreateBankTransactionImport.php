<?php

namespace App\Filament\Resources\BankTransactionImportResource\Pages;

use App\Filament\Resources\BankTransactionImportResource;
use App\Services\BankTransactionImportService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class CreateBankTransactionImport extends CreateRecord
{
    protected static string $resource = BankTransactionImportResource::class;

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        try {
            $importService = app(BankTransactionImportService::class);

            // The bank_file from Filament is a file path string, not an UploadedFile
            // Let's validate the file exists first
            $filePath = $data['bank_file'];

            if (!Storage::exists($filePath) && !Storage::disk('public')->exists($filePath) && !file_exists($filePath)) {
                Notification::make()
                    ->title('خطأ في الملف')
                    ->body('الملف غير موجود')
                    ->danger()
                    ->send();

                $this->halt();
            }

            $result = $importService->processFileUpload(
                $filePath, // This is a file path string from Filament
                $data['bank_account_id'],
                auth()->id()
            );

            if (!$result['success']) {
                Notification::make()
                    ->title('خطأ في رفع الملف')
                    ->body($result['error'])
                    ->danger()
                    ->send();

                $this->halt();
            }

            Notification::make()
                ->title('تم رفع الملف بنجاح')
                ->body($result['message'])
                ->success()
                ->send();

            return $result['import'];

        } catch (\Exception $e) {
            Notification::make()
                ->title('خطأ في معالجة الملف')
                ->body($e->getMessage())
                ->danger()
                ->send();

            $this->halt();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
