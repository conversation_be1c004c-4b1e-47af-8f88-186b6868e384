<?php

namespace App\Filament\Resources\BankTransactionImportResource\Pages;

use App\Filament\Resources\BankTransactionImportResource;
use App\Services\BankTransactionImportService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;

class CreateBankTransactionImport extends CreateRecord
{
    protected static string $resource = BankTransactionImportResource::class;

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        $importService = app(BankTransactionImportService::class);

        $result = $importService->processFileUpload(
            $data['bank_file'],
            $data['bank_account_id'],
            auth()->id()
        );

        if (!$result['success']) {
            Notification::make()
                ->title('خطأ في رفع الملف')
                ->body($result['error'])
                ->danger()
                ->send();

            $this->halt();
        }

        Notification::make()
            ->title('تم رفع الملف بنجاح')
            ->body($result['message'])
            ->success()
            ->send();

        return $result['import'];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
