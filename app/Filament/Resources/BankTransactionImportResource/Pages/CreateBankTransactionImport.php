<?php

namespace App\Filament\Resources\BankTransactionImportResource\Pages;

use App\Filament\Resources\BankTransactionImportResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Str;

class CreateBankTransactionImport extends CreateRecord
{
    protected static string $resource = BankTransactionImportResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['user_id'] = auth()->id();
        $data['uuid'] = Str::uuid();
        
        // Extract file information if file is uploaded
        if (isset($data['bank_file'])) {
            $data['file_name'] = $data['bank_file'];
            // You would implement file processing logic here
            // For now, we'll set some default values
            $data['items_count'] = 0;
            $data['items_sum_amount'] = 0;
            $data['file_size'] = 0;
        }
        
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
