<?php

namespace App\Filament\Resources\BankTransactionImportResource\Pages;

use App\Filament\Resources\BankTransactionImportResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Support\Enums\FontWeight;

class ViewBankTransactionImport extends ViewRecord
{
    protected static string $resource = BankTransactionImportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('تعديل')
                ->visible(fn () => is_null($this->record->completed_at)),

            Actions\Action::make('confirm')
                ->label('تأكيد الاستيراد')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn () => is_null($this->record->completed_at))
                ->requiresConfirmation()
                ->modalHeading('تأكيد استيراد المعاملات')
                ->modalDescription('هل أنت متأكد من أنك تريد تأكيد استيراد هذه المعاملات؟ لا يمكن التراجع عن هذا الإجراء.')
                ->action(function () {
                    $this->record->update(['completed_at' => now()]);

                    $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                }),

            Actions\Action::make('download_template')
                ->label('تحميل القالب')
                ->icon('heroicon-o-document-arrow-down')
                ->color('gray')
                ->url(route('download.bank-import-template'), shouldOpenInNewTab: true),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make(3)
                    ->schema([
                        Section::make('تفاصيل الاستيراد')
                            ->schema([
                                TextEntry::make('file_name')
                                    ->label('اسم الملف')
                                    ->icon('heroicon-o-document')
                                    ->weight(FontWeight::Bold),

                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('bank_account.title')
                                            ->label('الحساب البنكي')
                                            ->icon('heroicon-o-building-library'),

                                        TextEntry::make('formatted_size')
                                            ->label('حجم الملف')
                                            ->badge()
                                            ->icon('heroicon-o-document'),
                                    ]),

                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('items_count')
                                            ->label('عدد العناصر')
                                            ->numeric()
                                            ->icon('heroicon-o-list-bullet'),

                                        TextEntry::make('items_sum_amount')
                                            ->label('إجمالي المبلغ')
                                            ->riyal()
                                            ->icon('heroicon-o-currency-dollar'),
                                    ]),

                                TextEntry::make('completed_at')
                                    ->label('حالة الاستيراد')
                                    ->formatStateUsing(fn ($state) => $state ? 'مكتمل' : 'في الانتظار')
                                    ->badge()
                                    ->color(fn ($state) => $state ? 'success' : 'warning'),
                            ])
                            ->columnSpan(2),

                        Section::make('معلومات إضافية')
                            ->schema([
                                TextEntry::make('user.name')
                                    ->label('المستخدم')
                                    ->icon('heroicon-o-user'),

                                TextEntry::make('created_at')
                                    ->label('تاريخ الإنشاء')
                                    ->dateTime()
                                    ->icon('heroicon-o-clock'),

                                TextEntry::make('completed_at')
                                    ->label('تاريخ الإكمال')
                                    ->dateTime()
                                    ->placeholder('لم يكتمل بعد')
                                    ->icon('heroicon-o-check-circle'),

                                TextEntry::make('items.count')
                                    ->label('عدد العناصر المعالجة')
                                    ->state(fn () => $this->record->items()->count())
                                    ->icon('heroicon-o-calculator'),

                                TextEntry::make('items.errors_count')
                                    ->label('عدد الأخطاء')
                                    ->state(fn () => $this->record->items()->whereNotNull('error')->count())
                                    ->color(fn ($state) => $state > 0 ? 'danger' : 'success')
                                    ->icon('heroicon-o-exclamation-triangle'),
                            ])
                            ->columnSpan(1),
                    ])
            ]);
    }
}
