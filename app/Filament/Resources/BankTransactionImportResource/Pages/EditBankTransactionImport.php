<?php

namespace App\Filament\Resources\BankTransactionImportResource\Pages;

use App\Filament\Resources\BankTransactionImportResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBankTransactionImport extends EditRecord
{
    protected static string $resource = BankTransactionImportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('عرض'),
            
            Actions\DeleteAction::make()
                ->label('حذف')
                ->visible(fn () => is_null($this->record->completed_at)),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
