<?php

namespace App\Filament\Resources\BankTransactionImportResource\Pages;

use App\Filament\Resources\BankTransactionImportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListBankTransactionImports extends ListRecords
{
    protected static string $resource = BankTransactionImportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('استيراد جديد'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('الكل')
                ->badge(fn () => $this->getModel()::count()),

            'pending' => Tab::make('في الانتظار')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNull('completed_at'))
                ->badge(fn () => $this->getModel()::whereNull('completed_at')->count()),

            'completed' => Tab::make('مكتمل')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotNull('completed_at'))
                ->badge(fn () => $this->getModel()::whereNotNull('completed_at')->count()),

            'recent' => Tab::make('الحديثة')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('created_at', '>=', now()->subDays(7)))
                ->badge(fn () => $this->getModel()::where('created_at', '>=', now()->subDays(7))->count()),
        ];
    }
}
