<?php

namespace App\Filament\Resources\BankTransactionImportResource\RelationManagers;

use App\Models\BankTransactionImportItem;
use App\Models\User;
use App\Services\BankTransactionConfirmationService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    protected static ?string $title = 'عناصر الاستيراد';

    protected static ?string $modelLabel = 'عنصر';

    protected static ?string $pluralModelLabel = 'العناصر';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('تفاصيل العنصر')
                    ->schema([
                        Forms\Components\TextInput::make('notes')
                            ->label('الملاحظات')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('amount')
                            ->label('المبلغ')
                            ->numeric()
                            ->required()
                            ->suffix('ريال'),

                        Forms\Components\DateTimePicker::make('due_at')
                            ->label('تاريخ الاستحقاق')
                            ->required(),

                        Forms\Components\TextInput::make('reference')
                            ->label('المرجع')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('account')
                            ->label('رقم الحساب')
                            ->maxLength(255),

                        Forms\Components\Select::make('user_id')
                            ->label('المستخدم')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->nullable(),

                        Forms\Components\Toggle::make('anonymous')
                            ->label('مجهول')
                            ->default(false),

                        Forms\Components\Textarea::make('error')
                            ->label('رسالة الخطأ')
                            ->rows(3)
                            ->disabled()
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('notes')
            ->columns([
                Tables\Columns\TextColumn::make('notes')
                    ->label('الملاحظات')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= 30) {
                            return null;
                        }

                        return $state;
                    }),

                Tables\Columns\TextColumn::make('amount')
                    ->label('المبلغ')
                    ->riyal()
                    ->sortable(),

                Tables\Columns\TextColumn::make('due_at')
                    ->label('تاريخ الاستحقاق')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('reference')
                    ->label('المرجع')
                    ->searchable()
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('account')
                    ->label('رقم الحساب')
                    ->searchable()
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('المستخدم')
                    ->searchable()
                    ->placeholder('غير محدد'),

                Tables\Columns\IconColumn::make('anonymous')
                    ->label('مجهول')
                    ->boolean(),

                Tables\Columns\TextColumn::make('error')
                    ->label('الخطأ')
                    ->badge()
                    ->color('danger')
                    ->placeholder('لا يوجد')
                    ->limit(20)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();

                        if (!$state || strlen($state) <= 20) {
                            return null;
                        }

                        return $state;
                    }),
            ])
            ->defaultSort('created_at', 'asc')
            ->filters([
                TernaryFilter::make('anonymous')
                    ->label('مجهول'),

                TernaryFilter::make('user_id')
                    ->label('مرتبط بمستخدم')
                    ->nullable(),

                TernaryFilter::make('error')
                    ->label('يحتوي على خطأ')
                    ->nullable(),

                SelectFilter::make('user_id')
                    ->label('المستخدم')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('إضافة عنصر')
                    ->visible(fn () => is_null($this->getOwnerRecord()->completed_at)),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),

                Tables\Actions\EditAction::make()
                    ->label('تعديل')
                    ->visible(fn () => is_null($this->getOwnerRecord()->completed_at)),

                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->visible(fn () => is_null($this->getOwnerRecord()->completed_at)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    BulkAction::make('mark_anonymous')
                        ->label('تحديد كمجهول')
                        ->icon('heroicon-o-user-minus')
                        ->color('warning')
                        ->action(function (Collection $records) {
                            $records->each(fn ($record) => $record->update(['anonymous' => true, 'user_id' => null]));

                            Notification::make()
                                ->title('تم تحديد العناصر كمجهولة')
                                ->success()
                                ->send();
                        })
                        ->visible(fn () => is_null($this->getOwnerRecord()->completed_at)),

                    BulkAction::make('clear_user')
                        ->label('إلغاء ربط المستخدم')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function (Collection $records) {
                            $records->each(fn ($record) => $record->update(['user_id' => null, 'anonymous' => false]));

                            Notification::make()
                                ->title('تم إلغاء ربط المستخدمين')
                                ->success()
                                ->send();
                        })
                        ->visible(fn () => is_null($this->getOwnerRecord()->completed_at)),

                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->visible(fn () => is_null($this->getOwnerRecord()->completed_at)),
                ]),
            ]);
    }
}
