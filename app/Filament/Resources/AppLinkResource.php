<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AppLinkResource\Pages;
use App\Filament\Resources\AppLinkResource\Widgets\AppLinkStatsWidget;
use App\Models\AppLink;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AppLinkResource extends Resource
{
    protected static ?string $model = AppLink::class;

    protected static ?string $navigationIcon = 'heroicon-o-link';

    protected static ?string $navigationGroup = 'التطبيق';

    protected static ?string $modelLabel = 'رابط';
    protected static ?string $pluralModelLabel = 'روابط التواصل';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('العنوان')
                            ->required()
                            ->maxLength(191),
                        Forms\Components\TextInput::make('url')
                            ->label('الرابط')
                            ->required()
                            ->maxLength(1000)
                            ->url(),
                        Forms\Components\FileUpload::make('image')
                            ->label('الصورة')
                            ->image()
                            ->directory('uploads')
                            ->disk('public')
                            ->imageEditor()
                            ->helperText('اختياري'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('index')
                    ->label('الترتيب')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('image_url')
                    ->label('الصورة'),
                Tables\Columns\TextColumn::make('title')
                    ->label('العنوان')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('url')
                    ->label('الرابط')
                    ->limit(50)
                    ->url(fn($record) => $record->url),
                Tables\Columns\TextColumn::make('clicks')
                    ->label('عدد الضغطات')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('success'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'title',
                    'url',
                    'location',
                    'clicks',
                    'created_at',
                    'updated_at',
                ])->filters([
                    Tables\Filters\TrashedFilter::make(),
                ])->defaultFilters([]),
            ])
            ->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ForceDeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->reorderable('index')
            ->defaultSort('index', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAppLinks::route('/'),
            'create' => Pages\CreateAppLink::route('/create'),
            'view' => Pages\ViewAppLink::route('/{record}'),
            'edit' => Pages\EditAppLink::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getWidgets(): array
    {
        return [
            AppLinkStatsWidget::class,
        ];
    }
}
