<?php

namespace App\Filament\Resources;

use App\Filament\Resources\QuranCompetitionResource\Pages;
use App\Filament\Resources\QuranCompetitionResource\RelationManagers;
use App\Models\QuranCompetition;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;

class QuranCompetitionResource extends Resource
{
    protected static ?string $model = QuranCompetition::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';

    protected static ?string $navigationGroup = 'الخدمات';
    protected static ?int $navigationSort = -97;
    protected static ?string $navigationLabel = 'المسابقات القرآنية';

    protected static ?string $label = 'مسابقة القرآنية';
    protected static ?string $pluralModelLabel = 'المسابقات القرآنية';
    protected static ?string $modelLabel = 'المسابقة القرآنية';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->label(__('quran-competition.fields.title')),

                        Forms\Components\Select::make('activity_id')
                            ->relationship('activity', 'title')
                            ->searchable()
                            ->preload()
                            ->label(__('quran-competition.fields.activity_id')),

                        Forms\Components\Grid::make()
                            ->schema([
                                Forms\Components\Toggle::make('closed')
                                    ->label(__('quran-competition.fields.closed'))
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $set('accept_users', false);
                                        }
                                    }),

                                Forms\Components\Toggle::make('accept_users')
                                    ->label(__('quran-competition.fields.accept_users'))
                                    ->disabled(fn(callable $get) => $get('closed')),

                                Forms\Components\Toggle::make('accessible')
                                    ->label(__('quran-competition.fields.accessible'))
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if (!$state) {
                                            $set('accept_users', false);
                                        }
                                    }),

                                Forms\Components\Toggle::make('invisible')
                                    ->label(__('quran-competition.fields.invisible')),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),

                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->label(__('quran-competition.fields.title')),

                Tables\Columns\IconColumn::make('closed')
                    ->alignCenter()
                    ->boolean()
                    ->sortable()
                    ->label(__('quran-competition.fields.closed')),

                Tables\Columns\IconColumn::make('accept_users')
                    ->alignCenter()
                    ->boolean()
                    ->sortable()
                    ->label(__('quran-competition.fields.accept_users')),

                Tables\Columns\TextColumn::make('accepted_users_count')
                    ->counts('accepted_users')
                    ->sortable()
                    ->label(__('quran-competition.data.accepted_users_count')),

                Tables\Columns\TextColumn::make('requests_count')
                    ->counts('requests')
                    ->sortable()
                    ->label(__('quran-competition.data.requests_count')),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d h:i A')
                    ->sortable()
                    ->label(__('common.created_at')),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'title',
                    'accepted_users_count',
                    "accept_users",
                    "closed",
                    'requests_count',
                    'created_at',
                ])->filters([
                    // No existing filters
                ])->defaultFilters([]),
            ])->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make(fn($record) => $record->title)
                    ->headerActions([
                        Action::make('edit')
                            ->label(__('common.edit'))
                            ->url(fn($record) => static::getUrl('edit', ['record' => $record]))
                            ->icon('heroicon-m-pencil-square'),
                    ])
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('activity.title')
                                    ->default('لم يتم ربطه في برنامج بعد')
                                    ->label(__('quran-competition.fields.activity_id')),
                            ]),

                        Grid::make(4)
                            ->schema([
                                IconEntry::make('closed')
                                    ->label(__('quran-competition.fields.closed'))
                                    ->boolean(),

                                IconEntry::make('accept_users')
                                    ->label(__('quran-competition.fields.accept_users'))
                                    ->boolean(),

                                IconEntry::make('accessible')
                                    ->label(__('quran-competition.fields.accessible'))
                                    ->boolean(),

                                IconEntry::make('invisible')
                                    ->label(__('quran-competition.fields.invisible'))
                                    ->boolean(),
                            ]),
                    ])
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ChallengesRelationManager::class,
            RelationManagers\UsersRelationManager::class,
            RelationManagers\RequestsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuranCompetitions::route('/'),
            'create' => Pages\CreateQuranCompetition::route('/create'),
            'edit' => Pages\EditQuranCompetition::route('/{record}/edit'),
            'view' => Pages\ViewQuranCompetition::route('/{record}'),
        ];
    }
}
