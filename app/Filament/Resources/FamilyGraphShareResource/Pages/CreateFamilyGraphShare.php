<?php

namespace App\Filament\Resources\FamilyGraphShareResource\Pages;

use App\Filament\Resources\FamilyGraphShareResource;
use App\Filament\Traits\UserSelectSearchHandler;
use App\Models\FamilyGraphShare;
use Exception;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CreateFamilyGraphShare extends CreateRecord
{
    protected static string $resource = FamilyGraphShareResource::class;
    use UserSelectSearchHandler;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Generate unique slug
        $tries = 0;
        do {
            if ($tries > 20) {
                throw new Exception('Failed to generate unique slug');
            }
            $tries += 1;
            $slug = Str::random($tries > 10 ? 8 : 6);
        } while (FamilyGraphShare::query()->where('slug', $slug)->exists());

        $data['slug'] = $slug;
        $data['created_by_id'] = Auth::id();

        return parent::mutateFormDataBeforeCreate($data);
    }

    protected function handleRecordCreation(array $data): Model
    {
        $record = new ($this->getModel());
        $record->fill($data);

        $record->save();

        return $record;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
