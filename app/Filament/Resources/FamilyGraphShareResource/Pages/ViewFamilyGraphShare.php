<?php

namespace App\Filament\Resources\FamilyGraphShareResource\Pages;

use App\Filament\Resources\FamilyGraphShareResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFamilyGraphShare extends ViewRecord
{
    protected static string $resource = FamilyGraphShareResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('copy_link')
                ->label('نسخ الرابط')
                ->icon('heroicon-o-clipboard-document')
                ->color('info')
                ->url(fn() => $this->record->share_url, shouldOpenInNewTab: true)
                ->extraAttributes([
                    'onclick' => 'navigator.clipboard.writeText(this.href); return false;'
                ]),

            Actions\Action::make('view_link')
                ->label('عرض المشجرة')
                ->icon('heroicon-o-eye')
                ->color('success')
                ->url(fn() => $this->record->share_url, shouldOpenInNewTab: true),

            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
