<?php

namespace App\Filament\Resources;

use App\Enums\Gender;
use App\Enums\UserMaritalStatus;
use App\Filament\Actions\CopyFamilyUserIdsAction;
use App\Filament\Components\UserFilter;
use App\Filament\Exports\UserExporter;
use App\Filament\Resources\ChildFormResource\Pages as ChildFormPages;
use App\Filament\Resources\FamilyTitleResource\Pages as FamilyTitlePages;
use App\Filament\Resources\MembershipResource\Pages as MembershipPages;
use App\Filament\Resources\NonFamilyUserResource\Pages as NonFamilyUserPages;
use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\HusbandWife;
use App\Models\User;
use App\Permissions\UserPermissions;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Pages\ViewRecord;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\QueryBuilder;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

use Illuminate\Database\Eloquent\Builder;
use Rmsramos\Activitylog\Actions\ActivityLogTimelineTableAction;
use Rmsramos\Activitylog\RelationManagers\ActivitylogRelationManager;

class UserResource extends Resource
{


    protected static ?string $model = User::class;

    protected static ?string $recordTitleAttribute = 'full_name';
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'المشجرة';

    protected static ?string $navigationLabel = 'المستخدمون';

    protected static ?int $navigationSort = -99;

    protected static ?string $modelLabel = 'مستخدم';
    protected static ?string $pluralModelLabel = 'المستخدمون';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function table(Table $table): Table
    {
        return $table
            ->filters([
                QueryBuilder::make()
                    ->constraints([
                        // ...
                    ]),
            ], layout: FiltersLayout::AboveContent)
            ->columns([
                Tables\Columns\ImageColumn::make('profile_photo_url')
                    ->withFallbackAvatar()
                    ->label('')
                    ->circular()
                    ->size(40),
                TextColumn::make('family_user_id')
                    ->label('userColumns.family_user_id')
                    ->sortable()
                    ->translateLabel(),
                TextColumn::make('full_name')
                    ->label('userColumns.full_name')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return full_name_filter($query, $search);
                    })
                    ->sortable()
                    ->translateLabel()
                    ->formatStateUsing(function ($state, $record) {
                        if (!$record->is_dead) {
                            return $state;
                        }

                        $phrase = $record->gender === 'FEMALE' ? 'رحمها الله' : 'رحمه الله';
                        return $state . ' <span class="inline-flex items-center justify-center min-h-6 px-2 py-0.5 text-sm font-medium tracking-tight rounded-lg border border-danger-600 bg-danger-100 text-danger-600 dark:bg-danger-500/10 dark:text-danger-400 dark:border-danger-400">' . $phrase . '</span>';
                    })
                    ->html(),
                TextColumn::make('nested_children_count')
                    ->counts('nested_children')
                    ->label('userColumns.nested_children_count')
                    ->sortable()
                    ->translateLabel(),
                TextColumn::make('created_at')
                    ->label('created_at')
                    ->date()
                    ->dateTimeTooltip()
                    ->sortable()
                    ->translateLabel(),
                TextColumn::make('updated_at')
                    ->label('updated_at')
                    ->date()
                    ->dateTimeTooltip()
                    ->sortable()
                    ->translateLabel(),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    "family_user_id",
                    "created_at",
                    "updated_at",
                ])->filters([
                    UserFilter::make('family_user_id')
                        ->label("المستخدم"),

                    SelectFilter::make('roles')
                        ->label('userColumns.roles')
                        ->translateLabel()
                        ->multiple()
                        ->preload()
                        ->relationship('roles', 'title'),
                    Tables\Filters\TrashedFilter::make(),

                ])->defaultFilters([
                    ["family_user_id"]
                ]),
            ])->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersFormColumns(1)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->visible(fn($record) => is_null($record->deleted_at)),
                    Tables\Actions\EditAction::make()
                        ->visible(fn($record) => is_null($record->deleted_at)),
                    ActivityLogTimelineTableAction::make('التغييرات')->limit(30)
                        ->authorize(
                            fn($record) => auth()->user()->can('restore', $record)
                        ),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ForceDeleteAction::make(),
                ]),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()
                        ->exporter(UserExporter::class)
                        ->formats([
                            ExportFormat::Xlsx,
                        ])
                        ->visible(auth()->user()->hasPermissionTo(UserPermissions::downloadExcel))
                        ->label(__('تصدير إلى Excel'))
                        ->icon('heroicon-o-arrow-down-tray'),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                CopyFamilyUserIdsAction::make(),
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make()
                    ->columns(2)
                    ->schema([
                        // Left Column Section
                        Forms\Components\Section::make('المعلومات الأساسية')
                            ->icon('heroicon-o-user')
                            ->schema([
                                Forms\Components\FileUpload::make('profile_photo_path')
                                    ->label('userColumns.profile_photo')
                                    ->translateLabel()
                                    ->avatar()
                                    ->imageEditor()
                                    ->disk('public')
                                    ->directory('user-pic')
                                    ->nullable(),

                                Forms\Components\TextInput::make('name')
                                    ->label('userColumns.name')
                                    ->translateLabel()
                                    ->required()
                                    ->minLength(2)
                                    ->maxLength(32)
                                    ->regex('/^([\x{0621}-\x{063A}\x{0641}-\x{064A}]+)$/u')
                                    ->notIn(['بن', 'بنت']),

                                Forms\Components\Textarea::make('bio')
                                    ->label('userColumns.bio')
                                    ->translateLabel()
                                    ->nullable()
                                    ->maxLength(240),

                                Forms\Components\TextInput::make('national_id')
                                    ->label('userColumns.national_id')
                                    ->translateLabel()
                                    ->nullable()
                                    ->minLength(4)
                                    ->maxLength(32)
                                    ->unique(ignoreRecord: true),

                                Forms\Components\ToggleButtons::make('gender')
                                    ->label('userColumns.gender')
                                    ->translateLabel()
                                    ->disabled(fn($record) => !is_null($record))
                                    ->options(Gender::options())
                                    ->inline()
                                    ->default(Gender::Male)
                                    ->required(),

                                Forms\Components\DatePicker::make('dob')
                                    ->label('userColumns.dob')
                                    ->translateLabel()
                                    ->nullable(),
                            ])
                            ->columnSpan(1),

                        // Right Column Tabs
                        Forms\Components\Tabs::make('Additional Information')
                            ->tabs([
                                // Contact Information Tab
                                Forms\Components\Tabs\Tab::make('معلومات التواصل')
                                    ->icon('heroicon-o-phone')
                                    ->schema([
                                        Forms\Components\TextInput::make('phone')
                                            ->label('userColumns.phone')
                                            ->translateLabel()
                                            ->tel()
                                            ->nullable()
                                            ->unique(ignoreRecord: true),

                                        Forms\Components\TextInput::make('email')
                                            ->label('userColumns.email')
                                            ->translateLabel()
                                            ->email()
                                            ->nullable()
                                            ->unique(ignoreRecord: true),

                                        Forms\Components\Select::make('user_region_id')
                                            ->label('userColumns.user_region_id')
                                            ->translateLabel()
                                            ->relationship('user_region', 'title_ar')
                                            ->searchable()
                                            ->preload()
                                            ->nullable(),
                                        Forms\Components\TextInput::make('linkedin_url')
                                            ->url()
                                            ->label('رابط اللنكدان')
                                            ->translateLabel()
                                            ->nullable(),
                                    ]),

                                // Status Information Tab
                                Forms\Components\Tabs\Tab::make('معلومات الحالة')
                                    ->icon('heroicon-o-identification')
                                    ->schema([
                                        Forms\Components\Select::make('marital_status')
                                            ->label('userColumns.marital_status')
                                            ->translateLabel()
                                            ->options(fn($record) => UserMaritalStatus::options($record?->gender))
                                            ->preload()
                                            ->nullable(),

                                        Forms\Components\Select::make('health_status')
                                            ->label('userColumns.health_status')
                                            ->translateLabel()
                                            ->options(array_combine(all_health_status(), all_health_status()))
                                            //->options(all_health_status())
                                            ->nullable(),

                                        Forms\Components\Select::make('educational_status')
                                            ->label('userColumns.educational_status')
                                            ->translateLabel()
                                            ->options(array_combine(all_educational_status(), all_educational_status()))
                                            //->options(all_educational_status())
                                            ->nullable(),

                                        Forms\Components\Grid::make(2)
                                            ->schema([
                                                Forms\Components\ToggleButtons::make('is_dead')
                                                    ->label('userColumns.is_dead')
                                                    ->default(false)
                                                    ->translateLabel()
                                                    ->inline()
                                                    ->nullable()
                                                    ->boolean()
                                                    ->disabled(fn(callable $get) => !empty($get('dod')))
                                                    ->dehydrated()
                                                    ->reactive(),

                                                Forms\Components\DatePicker::make('dod')
                                                    /*->hintAction(
                                                        Action::make('clear')
                                                            ->action(fn($component) => $component->state(null))
                                                    )*/
                                                    ->label('userColumns.dod')
                                                    ->translateLabel()
                                                    ->visible(fn($get) => $get('is_dead'))
                                                    ->dehydrated()
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        if (!empty($get('dod')))
                                                            $set('is_dead', true);
                                                    }),
                                            ]),
                                    ]),

                                // Family Information Tab
                                Forms\Components\Tabs\Tab::make('معلومات العائلة')
                                    ->icon('heroicon-o-users')
                                    ->schema([
                                        Forms\Components\Select::make('father_id')
                                            ->label('userColumns.father_id')
                                            ->translateLabel()
                                            ->relationship(
                                                name: 'father',
                                                titleAttribute: 'full_name',
                                                modifyQueryUsing: fn(Builder $query) => $query->withTrashed(),
                                                ignoreRecord: true,
                                            )
                                            ->searchable()
                                            ->getSearchResultsUsing(function (string $search): array {
                                                return User::query()
                                                    ->where('gender', Gender::Male)
                                                    ->where(fn($query) => full_name_filter($query, $search))
                                                    ->limit(50)
                                                    ->with(['father.father.father'])
                                                    ->get()
                                                    ->pluck('full_name', 'id')
                                                    ->toArray();
                                            })
                                            ->reactive()
                                            ->afterStateUpdated(fn(callable $set) => $set('mother_id', null))
                                            ->default(fn(callable $get) => request()->query('father_id'))
                                            ->nullable(),

                                        Forms\Components\Select::make('mother_id')
                                            ->label('userColumns.mother_id')
                                            ->translateLabel()
                                            ->relationship('mother', 'full_name')
                                            ->options(function (callable $get) {
                                                $fatherId = $get('father_id');
                                                if ($fatherId) {
                                                    $father = User::find($fatherId);
                                                    if ($father) {
                                                        $wives = $father->wives()
                                                            ->with(['wife' => ['father.father.father', 'family_title']])
                                                            ->get();
                                                        return $wives->pluck('wife')
                                                            ->pluck('full_name', 'id')
                                                            ->toArray();
                                                    }
                                                }
                                                return [];
                                            })
                                            ->disabled(fn(callable $get) => !($get('father_id') && HusbandWife::where('husband_id', $get('father_id'))->exists()))
                                            ->default(fn(callable $get) => request()->query('mother_id'))
                                            ->nullable(),

                                        Forms\Components\Select::make('roles')
                                            ->label('userColumns.roles')
                                            ->translateLabel()
                                            ->multiple()
                                            ->relationship('roles', 'title')
                                            ->preload()
                                            ->nullable(),

                                        /*Forms\Components\Repeater::make('wives')
                                            ->label('الزوجات')
                                            ->relationship('wives')
                                            ->schema([
                                                Forms\Components\Select::make('type')
                                                    ->label('النوع')
                                                    ->options([
                                                        'family' => 'من العائلة',
                                                        'non-family' => 'من خارج العائلة',
                                                    ])
                                                    ->nullable(false)
                                                    ->visible(fn(callable $get) => is_null($get('wife_id')))
                                                    ->required()
                                                    ->reactive(),

                                                Forms\Components\Select::make('wife_id')
                                                    ->label('الزوجة')
                                                    ->relationship(
                                                        name: 'wife',
                                                        titleAttribute: 'full_name',
                                                        modifyQueryUsing: fn(Builder $query) => $query->withTrashed(),
                                                        ignoreRecord: true,
                                                    )
                                                    ->searchable()
                                                    ->getSearchResultsUsing(function (string $search, callable $get): array {
                                                        return User::query()
                                                            ->when($get('type') === 'non-family', fn($q) => $q->withoutGlobalScopes(['family_users'])->whereHas('family_title'))
                                                            ->where('gender', Gender::Female)
                                                            ->where(fn($query) => full_name_filter($query, $search))
                                                            ->limit(50)
                                                            ->with(array_filter([
                                                                'father.father.father',
                                                                ($get('type') === 'non-family' ? 'family_title' : null)
                                                            ]))
                                                            ->get()
                                                            ->pluck('full_name', 'id')
                                                            ->toArray();
                                                    })
                                                    ->reactive()
                                                    ->nullable(false)
                                                    ->disabled(fn(callable $get) => !is_null($get('wife_id')))
                                                    ->dehydrated()
                                                    ->required(),

                                                Forms\Components\DatePicker::make('married_at')
                                                    ->label('تاريخ الزواج')
                                                    ->visible(fn(callable $get) => !is_null($get('wife_id')))
                                                    ->nullable(),

                                                Forms\Components\Select::make('status')
                                                    ->label('الحالة')
                                                    ->reactive()
                                                    ->options(
                                                        collect(MarriageStatus::all())
                                                            ->mapWithKeys(fn($status) => [
                                                                $status => __("userColumns.marriage_status.$status")
                                                            ])
                                                            ->toArray()
                                                    )
                                                    ->default(MarriageStatus::Active)
                                                    ->visible(fn(callable $get) => !is_null($get('wife_id')))
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        if ($get('status') !== MarriageStatus::Divorced)
                                                            $set('divorced_at', null);
                                                        $set('is_divorced', $get('status') !== MarriageStatus::Divorced);
                                                    }),

                                                Forms\Components\DatePicker::make('divorced_at')
                                                    ->label('تاريخ الطلاق')
                                                    ->visible(fn(callable $get) => $get('status') === MarriageStatus::Divorced)
                                                    ->dehydrated()
                                                    ->nullable(),
                                            ]),*/
                                    ]),
                            ]),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\WivesRelationManager::class,
            RelationManagers\HusbandsRelationManager::class,
            RelationManagers\ChildrenRelationManager::class,
            RelationManagers\MotherChildrenRelationManager::class,
            RelationManagers\VoluntariesRelationManager::class,
            ActivitylogRelationManager::class,
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        if (
            $page instanceof Pages\ListUsers ||
            $page instanceof ChildFormPages\ListChildForms ||
            $page instanceof FamilyTitlePages\ListFamilyTitles ||
            $page instanceof NonFamilyUserPages\ListNonFamilyUsers ||
            $page instanceof MembershipPages\ListMemberships ||
            $page instanceof MarriageResource\Pages\ListMarriages
        )
            return $page->generateNavigationItems([
                Pages\ListUsers::class,
                ChildFormPages\ListChildForms::class,
                FamilyTitlePages\ListFamilyTitles::class,
                NonFamilyUserPages\ListNonFamilyUsers::class,
                MembershipPages\ListMemberships::class,
                MarriageResource\Pages\ListMarriages::class,
            ]);
        else if (is_subclass_of(get_class($page), ViewRecord::class))
            return $page->generateNavigationItems([
                Pages\ViewUser::class,
            ]);
        return [];
    }

    /** @return Builder<User> */
    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['father.father.father']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'family_user_id', 'national_id', 'phone'];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
            'family-builder' => Pages\FamilyBuilder::route('/{record}/family-builder'),
        ];
    }
}
