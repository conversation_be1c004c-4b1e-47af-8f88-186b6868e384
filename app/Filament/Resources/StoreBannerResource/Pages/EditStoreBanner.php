<?php

namespace App\Filament\Resources\StoreBannerResource\Pages;

use App\Filament\Resources\StoreBannerResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Intervention\Image\Laravel\Facades\Image;
use Illuminate\Support\Facades\Storage;

class EditStoreBanner extends EditRecord
{
    protected static string $resource = StoreBannerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Extract product_id or category_id from the data JSON field
        if (isset($data['data'])) {
            if (isset($data['data']['product_id'])) {
                $data['product_id'] = $data['data']['product_id'];
            } elseif (isset($data['data']['category_id'])) {
                $data['category_id'] = $data['data']['category_id'];
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Handle image upload like in StoreBannerController
        if (!empty($data['image'])) {
            $imageFile = $data['image'];
            $data['image'] = [
                'path' => $imageFile,
                'disk' => 'tenant',
            ];
        }

        // Set the data field based on the type and selected product/category
        if ($data['type'] === \App\Enums\StoreBannerType::Product && isset($data['product_id'])) {
            $data['data'] = ['product_id' => $data['product_id']];
            unset($data['product_id']);
        } elseif ($data['type'] === \App\Enums\StoreBannerType::Category && isset($data['category_id'])) {
            $data['data'] = ['category_id' => $data['category_id']];
            unset($data['category_id']);
        }

        return $data;
    }
}
