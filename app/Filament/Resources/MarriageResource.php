<?php

namespace App\Filament\Resources;

use App\Enums\Gender;
use App\Enums\MarriageStatus;
use App\Filament\Actions\CopyFamilyUserIdsAction;
use App\Filament\Components\UserFilter;
use App\Filament\Components\UserSelect;
use App\Filament\Resources\MarriageResource\Pages;
use App\Models\FamilyTitle;
use App\Models\HusbandWife;
use App\Models\NonFamilyUser;
use App\Models\User;
use App\Settings\GeneralSettings;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Archilex\AdvancedTables\Filters\SelectFilter as AdvancedSelectFilter;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

class MarriageResource extends Resource
{
    protected static ?string $model = HusbandWife::class;

    protected static ?string $navigationIcon = 'heroicon-o-heart'; // Or another suitable icon

    protected static ?string $navigationGroup = 'المشجرة';

    protected static ?string $navigationLabel = 'الزواجات';

    protected static ?string $modelLabel = 'زواج';

    protected static ?string $pluralModelLabel = 'الزواجات';

    protected static bool $shouldRegisterNavigation = false;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('husband_type')
                    ->label('عائلة الزوج')
                    ->dehydrated(false)
                    ->options([
                        'family' => 'داخل العائلة',
                        'non-family' => 'خارج العائلة',
                    ])
                    ->default('family')
                    ->live()
                    ->afterStateUpdated(function (callable $set, callable $get) {
                        $set('husband_id', null);
                    })
                    ->disabledOn('edit')
                    ->rules([
                        function ($get) {
                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                $husband_type = $get('husband_type');
                                $wife_type = $get('wife_type');

                                if ($husband_type === 'non-family' && $wife_type === 'non-family') {
                                    $fail('لا يمكن أن يكون الزوج والزوجة خارج العائلة.');
                                }
                            };
                        },
                    ])
                    ->required(),
                UserSelect::make('husband_id')
                    ->searchable()
                    ->label('الزوج')
                    ->disabledOn('edit')
                    ->live()
                    ->gender(Gender::Male)
                    ->isNonFamily(fn(callable $get) => $get('husband_type') === 'non-family')
                    ->required()
                    ->different('wife_id')
                    ->validationMessages([
                        'different' => 'لا يمكن أن يكون الزوج والزوجة نفس الشخص.',
                    ])
                    ->rules([
                        function ($get) {
                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                $husband_type = $get('husband_type');
                                if ($husband_type === 'non-family') {
                                    $husband = NonFamilyUser::find($value);
                                } else {
                                    $husband = User::find($value);
                                }
                                $status = $get('status');
                                $recordId = $get('id');

                                if (! $husband) {
                                    $fail('الزوج غير موجود.');
                                }

                                if ($husband->gender !== Gender::Male) {
                                    $fail('يجب أن يكون الزوج ذكرًا.');
                                }

                                if ($status === MarriageStatus::Active) {
                                    $activeWivesCount = HusbandWife::where('husband_id', $value)
                                        ->where('status', MarriageStatus::Active)
                                        ->when($recordId, fn($query) => $query->where('id', '!=', $recordId))
                                        ->count();

                                    if ($activeWivesCount >= 4) {
                                        $fail('يوجد 4 زوجات على ذمة الزوج !');
                                    }
                                }
                            };
                        },
                    ]),
                Select::make('wife_type')
                    ->label('عائلة الزوجة')
                    ->disabledOn('edit')
                    ->options([
                        'family' => 'داخل العائلة',
                        'non-family' => 'خارج العائلة',
                    ])
                    ->rules([
                        function ($get) {
                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                $husband_type = $get('husband_type');
                                $wife_type = $get('wife_type');

                                if ($husband_type === 'non-family' && $wife_type === 'non-family') {
                                    $fail('لا يمكن أن يكون الزوج والزوجة خارج العائلة.');
                                }
                            };
                        },
                    ])
                    ->afterStateUpdated(function (callable $set, callable $get) {
                        $set('wife_id', null);
                    })
                    ->default('family')
                    ->dehydrated(false)
                    ->live()
                    ->required(),
                UserSelect::make('wife_id')
                    ->label('الزوجة')
                    ->disabledOn('edit')
                    ->searchable()
                    ->live()
                    ->gender(Gender::Female)
                    ->isNonFamily(fn(callable $get) => $get('wife_type') === 'non-family')
                    ->required()
                    ->different('husband_id')
                    ->validationMessages([
                        'different' => 'لا يمكن أن يكون الزوج والزوجة نفس الشخص.',
                    ])
                    ->rules([
                        function ($get) {
                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                $wife_type = $get('wife_type');
                                if ($wife_type === 'non-family') {
                                    $wife = NonFamilyUser::find($value);
                                } else {
                                    $wife = User::find($value);
                                }
                                $status = $get('status');
                                $recordId = $get('id');

                                if (! $wife) {
                                    $fail('الزوجة غير موجودة.');
                                }

                                if ($wife->gender !== Gender::Female) {
                                    $fail('يجب أن تكون الزوجة أنثى.');
                                }

                                if ($status === MarriageStatus::Active) {
                                    $activeHusbandCount = HusbandWife::where('wife_id', $value)
                                        ->where('status', MarriageStatus::Active)
                                        ->when($recordId, fn($query) => $query->where('id', '!=', $recordId))
                                        ->count();

                                    if ($activeHusbandCount >= 1) {
                                        $fail('لا يمكن أن يكون أكثر من زوج على ذمة الزوجة.');
                                    }
                                }
                            };
                        },
                    ]),

                DatePicker::make('married_at')
                    ->label('تاريخ الزواج')
                    ->nullable(),

                Select::make('status')
                    ->label('الحالة')
                    ->options(
                        collect(MarriageStatus::all())
                            ->mapWithKeys(fn($status) => [
                                $status => __("userColumns.marriage_status.$status"),
                            ])
                            ->toArray()
                    )
                    ->default(MarriageStatus::Active)
                    ->live()
                    ->reactive()
                    ->afterStateUpdated(function (callable $set, callable $get) {
                        if ($get('status') !== MarriageStatus::Divorced) {
                            $set('divorced_at', null);
                        }
                    })
                    ->required(),
                DatePicker::make('divorced_at')
                    ->label('تاريخ الطلاق')
                    ->visible(fn(callable $get) => $get('status') === MarriageStatus::Divorced)
                    ->requiredIf('status', MarriageStatus::Divorced)
                    ->nullable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn(Builder $query) => $query->with(['husband', 'wife']))
            ->columns([
                ImageColumn::make('husband.profile_photo_url')
                    ->label('')
                    ->circular()
                    ->url(fn(HusbandWife $record): string => $record->husband->is_family ?
                        route('filament.admin.resources.users.view', $record->husband_id) :
                        route('filament.admin.resources.non-family-users.view', $record->husband_id))
                    ->size(40),
                TextColumn::make('husband.full_name')
                    ->searchable(
                        query: function (Builder $query, string $search): Builder {
                            return $query->whereHas('husband', function (Builder $query) use ($search) {
                                return full_name_filter($query, $search);
                            });
                        }
                    )
                    ->url(fn(HusbandWife $record): string => $record->husband->is_family ?
                        route('filament.admin.resources.users.view', $record->husband_id) :
                        route('filament.admin.resources.non-family-users.view', $record->husband_id))
                    ->label('الزوج'),
                ImageColumn::make('wife.profile_photo_url')
                    ->label('')
                    ->circular()
                    ->url(fn(HusbandWife $record): string => $record->wife->is_family ?
                        route('filament.admin.resources.users.view', $record->wife_id) :
                        route('filament.admin.resources.non-family-users.view', $record->wife_id))
                    ->size(40),
                TextColumn::make('wife.full_name')
                    ->searchable(
                        query: function (Builder $query, string $search): Builder {
                            return $query->whereHas('wife', function (Builder $query) use ($search) {
                                return full_name_filter($query, $search);
                            });
                        }
                    )
                    ->url(fn(HusbandWife $record): string => $record->wife->is_family ?
                        route('filament.admin.resources.users.view', $record->wife_id) :
                        route('filament.admin.resources.non-family-users.view', $record->wife_id))
                    ->label('الزوجة'),
                TextColumn::make('status')
                    ->label('الحالة')
                    ->formatStateUsing(fn(string $state): string => __("userColumns.marriage_status.$state"))
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        MarriageStatus::Active => 'success',
                        MarriageStatus::Divorced => 'warning',
                        default => 'primary',
                    }),
                TextColumn::make('children_count')
                    ->label('عدد الابناء')
                    ->getStateUsing(fn(HusbandWife $record): int => $record->children()->count())
                    ->numeric()
                    ->sortable(),
                TextColumn::make('married_at')
                    ->label('تاريخ الزواج')
                    ->date()
                    ->sortable(),
                TextColumn::make('divorced_at')
                    ->label('تاريخ الطلاق')
                    ->date()
                    ->sortable(),
            ])
            ->recordUrl(null)
            ->defaultSort('married_at', 'desc')
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'husband.full_name',
                    'wife.full_name',
                    'married_at',
                    'divorced_at',
                ])->filters([
                    SelectFilter::make('status')
                        ->label('الحالة')
                        ->multiple()
                        ->options(
                            collect(MarriageStatus::all())
                                ->mapWithKeys(fn($status) => [
                                    $status => __("userColumns.marriage_status.$status"),
                                ])
                                ->toArray()
                        ),
                    UserFilter::make('husband.full_name')
                        ->label('الزوج')
                        ->setUserRelationshipName('husband'),
                    UserFilter::make('wife.full_name')
                        ->label('الزوجة')
                        ->setUserRelationshipName('wife'),

                    Tables\Filters\SelectFilter::make('husband_family')
                        ->label('عائلة الزوج')
                        ->multiple()
                        ->options(function () {
                            $options = [];

                            // Add default family from settings
                            $generalSettings = app(GeneralSettings::class);
                            if ($generalSettings->familyNameAr) {
                                $options['default'] = $generalSettings->familyNameAr;
                            }

                            // Add family titles
                            $familyTitles = FamilyTitle::all();
                            foreach ($familyTitles as $title) {
                                $options["title_{$title->id}"] = $title->title;
                            }

                            return $options;
                        })
                        ->query(function (Builder $query, array $data) {
                            if (empty($data['values'])) {
                                return $query;
                            }

                            $values = $data['values'];

                            return $query->whereHas('husband', function (Builder $q) use ($values) {
                                $q->where(function (Builder $subQuery) use ($values) {
                                    foreach ($values as $value) {
                                        if ($value === 'default') {
                                            $subQuery->orWhereNull('family_title_id');
                                        } elseif (str_starts_with($value, 'title_')) {
                                            $titleId = str_replace('title_', '', $value);
                                            $subQuery->orWhere('family_title_id', $titleId);
                                        }
                                    }
                                });
                            });
                        }),

                    Tables\Filters\SelectFilter::make('wife_family')
                        ->label('عائلة الزوجة')
                        ->multiple()
                        ->options(function () {
                            $options = [];

                            // Add default family from settings
                            $generalSettings = app(GeneralSettings::class);
                            if ($generalSettings->familyNameAr) {
                                $options['default'] = $generalSettings->familyNameAr;
                            }

                            // Add family titles
                            $familyTitles = FamilyTitle::all();
                            foreach ($familyTitles as $title) {
                                $options["title_{$title->id}"] = $title->title;
                            }

                            return $options;
                        })
                        ->query(function (Builder $query, array $data) {
                            if (empty($data['values'])) {
                                return $query;
                            }

                            $values = $data['values'];

                            return $query->whereHas('wife', function (Builder $q) use ($values) {
                                $q->where(function (Builder $subQuery) use ($values) {
                                    foreach ($values as $value) {
                                        if ($value === 'default') {
                                            $subQuery->orWhereNull('family_title_id');
                                        } elseif (str_starts_with($value, 'title_')) {
                                            $titleId = str_replace('title_', '', $value);
                                            $subQuery->orWhere('family_title_id', $titleId);
                                        }
                                    }
                                });
                            });
                        }),
                    Filter::make('children_count_filter')
                        ->form([
                            Forms\Components\Select::make('operator')
                                ->label('المعامل')
                                ->options([
                                    '=' => 'يساوي',
                                    '>' => 'أكبر من',
                                    '<' => 'أصغر من',
                                    '>=' => 'أكبر من أو يساوي',
                                    '<=' => 'أصغر من أو يساوي',
                                ])
                                ->default('='),
                            Forms\Components\TextInput::make('count')
                                ->label('عدد الأبناء')
                                ->numeric()
                                ->placeholder('أدخل العدد'),

                        ])
                        ->query(function (Builder $query, array $data): Builder {
                            if (blank($data['count']) || ! is_numeric($data['count'])) {
                                return $query;
                            }

                            $countValue = (int) $data['count'];
                            $operator = $data['operator'] ?? '=';

                            $allowedOperators = ['=', '>', '<', '>=', '<='];
                            if (! in_array($operator, $allowedOperators)) {
                                $operator = '=';
                            }

                            // Get table names dynamically
                            $husbandWifeTable = app(HusbandWife::class)->getTable();
                            $childrenTable = app(User::class)->getTable();

                            $rawCondition = sprintf(
                                'COALESCE((SELECT COUNT(*) FROM %s WHERE %s.father_id = %s.husband_id AND %s.mother_id = %s.wife_id), 0) %s ?',
                                $childrenTable,        // For 1st %s (FROM table)
                                $childrenTable,        // For 2nd %s (WHERE table.father_id)
                                $husbandWifeTable,     // For 3rd %s (husband_wives.husband_id)
                                $childrenTable,        // For 4th %s (table.mother_id)
                                $husbandWifeTable,     // For 5th %s (husband_wives.wife_id)
                                $operator              // For 6th %s (the comparison operator like '=', '>')
                            );

                            // Apply the raw condition to the query
                            return $query->whereRaw($rawCondition, [$countValue]);
                        })
                        ->indicateUsing(function (array $data): ?string {
                            if (blank($data['count']) || ! is_numeric($data['count'])) {
                                return null;
                            }
                            $operatorLabels = [
                                '=' => 'يساوي',
                                '>' => 'أكبر من',
                                '<' => 'أصغر من',
                                '>=' => 'أكبر من أو يساوي',
                                '<=' => 'أصغر من أو يساوي',
                            ];
                            $currentOperator = $data['operator'] ?? '=';
                            $operatorLabel = $operatorLabels[$currentOperator] ?? $currentOperator;

                            return 'عدد الأبناء ' . $operatorLabel . ' ' . $data['count'];
                        })
                        ->label('عدد الابناء'),
                ])->defaultFilters([]),
            ])->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\Action::make('view_children')
                        ->label('الأبناء')
                        ->icon('heroicon-o-users')
                        ->modalContent(fn(HusbandWife $record): HtmlString => new HtmlString(Blade::render('<livewire:marriage-children-table :record-id="' . $record->id . '" />')))
                        ->modalHeading('أبناء الزواج')
                        ->modalSubmitAction(false)
                        ->modalCancelActionLabel('إغلاق'),
                    Tables\Actions\Action::make('add_child')
                        ->label('إضافة مولود')
                        ->icon('heroicon-o-user-plus')
                        ->url(fn(HusbandWife $record): string => UserResource::getUrl('create', [
                            'father_id' => $record->husband_id,
                            'mother_id' => $record->wife_id,
                        ])),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                CopyFamilyUserIdsAction::make('copy_husband_ids')
                    ->label(__('نسخ أرقام الأزواج'))
                    ->field('husband.family_user_id'),
                CopyFamilyUserIdsAction::make('copy_wife_ids')
                    ->label(__('نسخ أرقام الزوجات'))
                    ->field('wife.family_user_id'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // No relations needed here typically, as it's a pivot model resource
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMarriages::route('/'),
            'create' => Pages\CreateMarriage::route('/create'),
            'edit' => Pages\EditMarriage::route('/{record}/edit'),
        ];
    }
}
