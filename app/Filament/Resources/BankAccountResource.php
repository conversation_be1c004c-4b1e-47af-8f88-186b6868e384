<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BankAccountResource\Pages;
use App\Filament\Resources\BankAccountResource\RelationManagers;
use App\Models\BankAccount;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class BankAccountResource extends Resource
{
    protected static ?string $model = BankAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static ?string $navigationLabel = 'الحسابات البنكية';

    protected static ?string $navigationGroup = 'الدعم';

    protected static ?string $modelLabel = 'حساب بنكي';

    protected static ?string $pluralModelLabel = 'الحسابات البنكية';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات الحساب البنكي')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('اسم الحساب')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Select::make('bank')
                            ->label('البنك')
                            ->options([
                                'albilad' => 'بنك البلاد',
                                'alrajhi' => 'مصرف الراجحي',
                                'anb' => 'البنك العربي الوطني',
                                'alahli' => 'البنك الأهلي',
                                'alinma' => 'بنك الإنماء',
                                'aljazira' => 'بنك الجزيرة',
                                'riyad' => 'بنك الرياض',
                            ])
                            ->required()
                            ->searchable(),

                        Forms\Components\Select::make('type')
                            ->label('نوع الحساب')
                            ->options([
                                'zakat' => 'زكاة',
                                'charity' => 'صدقة',
                                'zwaj' => 'زواج',
                                'other' => 'أخرى',
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('account')
                            ->label('رقم الحساب')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('iban')
                            ->label('رقم الآيبان')
                            ->maxLength(255)
                            ->placeholder('SA00 0000 0000 0000 0000 0000'),

                        Forms\Components\Toggle::make('is_enabled')
                            ->label('مفعل')
                            ->default(true),

                        Forms\Components\Textarea::make('description')
                            ->label('الوصف')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->label('اسم الحساب')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('bank')
                    ->label('البنك')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'albilad' => 'بنك البلاد',
                        'alrajhi' => 'مصرف الراجحي',
                        'anb' => 'البنك العربي الوطني',
                        'alahli' => 'البنك الأهلي',
                        'alinma' => 'بنك الإنماء',
                        'aljazira' => 'بنك الجزيرة',
                        'riyad' => 'بنك الرياض',
                        default => $state,
                    })
                    ->badge()
                    ->sortable(),

                TextColumn::make('type')
                    ->label('النوع')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'zakat' => 'زكاة',
                        'charity' => 'صدقة',
                        'zwaj' => 'زواج',
                        'other' => 'أخرى',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'zakat' => 'success',
                        'charity' => 'info',
                        'zwaj' => 'warning',
                        'other' => 'gray',
                        default => 'gray',
                    })
                    ->sortable(),

                TextColumn::make('account')
                    ->label('رقم الحساب')
                    ->searchable()
                    ->copyable(),

                TextColumn::make('transactions_count')
                    ->label('عدد المعاملات')
                    ->counts('transactions')
                    ->sortable(),

                TextColumn::make('transactions_sum_amount')
                    ->label('إجمالي المبالغ')
                    ->sum('transactions', 'amount')
                    ->riyal()
                    ->sortable(),

                IconColumn::make('is_enabled')
                    ->label('مفعل')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                TernaryFilter::make('is_enabled')
                    ->label('مفعل'),

                Tables\Filters\SelectFilter::make('bank')
                    ->label('البنك')
                    ->options([
                        'albilad' => 'بنك البلاد',
                        'alrajhi' => 'مصرف الراجحي',
                        'anb' => 'البنك العربي الوطني',
                        'alahli' => 'البنك الأهلي',
                        'alinma' => 'بنك الإنماء',
                        'aljazira' => 'بنك الجزيرة',
                        'riyad' => 'بنك الرياض',
                    ]),

                Tables\Filters\SelectFilter::make('type')
                    ->label('النوع')
                    ->options([
                        'zakat' => 'زكاة',
                        'charity' => 'صدقة',
                        'zwaj' => 'زواج',
                        'other' => 'أخرى',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\TransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankAccounts::route('/'),
            'create' => Pages\CreateBankAccount::route('/create'),
            'view' => Pages\ViewBankAccount::route('/{record}'),
            'edit' => Pages\EditBankAccount::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('supporters.create');
    }

    public static function canView(Model $record): bool
    {
        return auth()->user()->can('supporters.index');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->can('supporters.update');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('supporters.update') && $record->transactions()->count() === 0;
    }
}
