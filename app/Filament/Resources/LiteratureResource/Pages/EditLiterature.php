<?php

namespace App\Filament\Resources\LiteratureResource\Pages;

use App\Filament\Resources\LiteratureResource;
use App\Filament\Traits\RedirectsToViewRecord;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditLiterature extends EditRecord
{
    use RedirectsToViewRecord;
    protected static string $resource = LiteratureResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
