<?php

namespace App\Filament\Resources\ShortUrlResource\Pages;

use App\Filament\Resources\ShortUrlResource;
use App\Filament\Resources\ShortUrlResource\Widgets\VisitsLineChartWidget;
use App\Filament\Resources\ShortUrlResource\Widgets\BrowserPieChartWidget;
use App\Filament\Resources\ShortUrlResource\Widgets\OperatingSystemPieChartWidget;
use App\Filament\Resources\ShortUrlResource\Widgets\DeviceTypePieChartWidget;
use Filament\Actions;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

class ViewShortUrl extends ViewRecord
{
    protected static string $resource = ShortUrlResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('معلومات أساسية')
                    ->schema([
                        TextEntry::make('title')
                            ->label(__('shortURLsColumns.title'))
                            ->weight('bold'),
                    ])
                    ->columns(2),

                Section::make('الروابط')
                    ->schema([
                        TextEntry::make('url_key')
                            ->label(__('shortURLsColumns.url_key'))
                            ->copyable()
                            ->copyMessage('تم نسخ مفتاح الرابط')
                            ->copyMessageDuration(1500)
                            ->badge()
                            ->color('success')
                            ->suffixAction(
                                Action::make('visit_short')
                                    ->icon('heroicon-m-arrow-top-right-on-square')
                                    ->url(fn($record) => $record->default_short_url)
                                    ->openUrlInNewTab()
                            ),

                        TextEntry::make('default_short_url')
                            ->label(__('shortURLsColumns.default_short_url'))
                            ->copyable()
                            ->copyMessage('تم نسخ الرابط المختصر')
                            ->formatStateUsing(function ($record) {
                                $url = $record->default_short_url;
                                if (!$url) {
                                    return '';
                                }
                                // Remove protocol and www, then cut to 30 chars max, add ellipsis if needed
                                $short = preg_replace('#^https?://(www\.)?#', '', $url);
                                if (mb_strlen($short) > 30) {
                                    $short = mb_substr($short, 0, 30) . '...';
                                }
                                return $short;
                            })
                            ->copyMessageDuration(1500)
                            ->color('primary')
                            ->suffixAction(
                                Action::make('visit_short_url')
                                    ->icon('heroicon-m-arrow-top-right-on-square')
                                    ->url(fn($record) => $record->default_short_url)
                                    ->openUrlInNewTab()
                            ),

                        TextEntry::make('destination_url')
                            ->label(__('shortURLsColumns.destination_url'))
                            ->copyable()
                            ->copyMessage('تم نسخ الرابط الأصلي')
                            ->copyMessageDuration(1500)
                            ->color('info')
                            ->formatStateUsing(function ($record) {
                                $url = $record->destination_url;
                                if (!$url) {
                                    return '';
                                }
                                // Remove protocol and www, then cut to 30 chars max, add ellipsis if needed
                                $short = preg_replace('#^https?://(www\.)?#', '', $url);
                                if (mb_strlen($short) > 30) {
                                    $short = mb_substr($short, 0, 30) . '...';
                                }
                                return $short;
                            })
                            ->suffixAction(
                                Action::make('visit_destination')
                                    ->icon('heroicon-m-arrow-top-right-on-square')
                                    ->url(fn($record) => $record->destination_url)
                                    ->openUrlInNewTab()
                            ),
                    ])
                    ->columns(3),

                Grid::make(2)
                    ->schema([
                        Section::make('الإعدادات')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        IconEntry::make('track_visits')
                                            ->label(__('shortURLsColumns.track_visits'))
                                            ->boolean()
                                            ->trueColor('success')
                                            ->falseColor('danger'),

                                        IconEntry::make('single_use')
                                            ->label(__('shortURLsColumns.single_use'))
                                            ->boolean()
                                            ->trueColor('success')
                                            ->falseColor('danger'),
                                    ]),
                            ]),

                        Section::make('الإحصائيات')
                            ->schema([
                                TextEntry::make('visits_count')
                                    ->label(__('shortURLsColumns.visits_count'))
                                    ->numeric()
                                    ->badge()
                                    ->color(fn($state) => match (true) {
                                        $state >= 1000 => 'success',
                                        $state >= 100 => 'warning',
                                        $state >= 10 => 'info',
                                        default => 'gray',
                                    })
                                    ->suffix(' زيارة'),

                                TextEntry::make('created_at')
                                    ->label(__('shortURLsColumns.created_at'))
                                    ->dateTime()
                                    ->since()
                                    ->icon('heroicon-m-calendar'),

                                TextEntry::make('updated_at')
                                    ->label(__('shortURLsColumns.updated_at'))
                                    ->dateTime()
                                    ->since()
                                    ->icon('heroicon-m-pencil'),
                            ])
                            ->columns(3),
                    ]),
            ]);
    }

    protected function getHeaderWidgets(): array
    {
        // Only show widgets if visit tracking is enabled
        if (!$this->record->track_visits) {
            return [];
        }

        return [
            VisitsLineChartWidget::make(['record' => $this->record]),
            BrowserPieChartWidget::make(['record' => $this->record]),
            OperatingSystemPieChartWidget::make(['record' => $this->record]),
            DeviceTypePieChartWidget::make(['record' => $this->record]),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            Actions\Action::make('visit_short_url')
                ->label('زيارة الرابط المختصر')
                ->icon('heroicon-o-arrow-top-right-on-square')
                ->color('primary')
                ->url(fn($record) => $record->default_short_url)
                ->openUrlInNewTab(),
            Actions\Action::make('copy_short_url')
                ->label('نسخ الرابط المختصر')
                ->icon('heroicon-o-clipboard')
                ->color('gray')
                ->action(function ($record) {
                    $this->js('navigator.clipboard.writeText("' . $record->default_short_url . '")');
                }),
        ];
    }
}
