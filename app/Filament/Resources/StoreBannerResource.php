<?php

namespace App\Filament\Resources;

use App\Enums\ProductType;
use App\Enums\StoreBannerType;
use App\Filament\Resources\StoreBannerResource\Pages;
use App\Filament\Resources\StoreBannerResource\Widgets\StoreBannerStatsWidget;
use App\Models\Category;
use App\Models\Product;
use App\Models\StoreBanner;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Archilex\AdvancedTables\Filters\BooleanFilter;
use Archilex\AdvancedTables\Filters\SelectFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StoreBannerResource extends Resource
{
    protected static ?string $model = StoreBanner::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'إدارة المتجر';

    protected static ?string $modelLabel = 'اعلان';
    protected static ?string $pluralModelLabel = 'اعلانات المتجر';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label(__('storeBanner.title'))
                            ->required()
                            ->maxLength(191),
                        Forms\Components\Select::make('type')
                            ->label(__('storeBanner.type'))
                            ->options([
                                StoreBannerType::Product => __('storeBanner.type_product'),
                                StoreBannerType::Category => __('storeBanner.type_category'),
                            ])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn(callable $set) => $set('product_id', null)),
                        Forms\Components\Select::make('product_id')
                            ->label(__('storeBanner.product'))
                            ->options(Product::where('type', ProductType::Case)->pluck('title', 'id'))
                            ->searchable()
                            ->visible(fn(callable $get) => $get('type') === StoreBannerType::Product)
                            ->required(fn(callable $get) => $get('type') === StoreBannerType::Product),
                        Forms\Components\Select::make('category_id')
                            ->label(__('storeBanner.category'))
                            ->options(Category::where('type', 'PRODUCT')->pluck('title', 'id'))
                            ->searchable()
                            ->visible(fn(callable $get) => $get('type') === StoreBannerType::Category)
                            ->required(fn(callable $get) => $get('type') === StoreBannerType::Category),
                        Forms\Components\FileUpload::make('image')
                            ->label(__('storeBanner.image'))
                            ->required()
                            ->image()
                            ->directory('uploads')
                            ->imageEditor()
                            ->imageCropAspectRatio('10:3')
                            ->maxWidth(1500)
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('index')->hidden(),
                Tables\Columns\ImageColumn::make('image_url')
                    ->label(__('storeBanner.image'))
                    ->circular(),
                Tables\Columns\TextColumn::make('title')
                    ->label(__('storeBanner.title'))
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('storeBanner.type'))
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        StoreBannerType::Product => __('storeBanner.type_product'),
                        StoreBannerType::Category => __('storeBanner.type_category'),
                        default => $state,
                    })
                    ->color(fn(string $state): string => match ($state) {
                        StoreBannerType::Product => 'success',
                        StoreBannerType::Category => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('target_name')
                    ->label(__('storeBanner.target'))
                    ->getStateUsing(function (StoreBanner $record) {
                        if ($record->type === StoreBannerType::Product && isset($record->data['product_id'])) {
                            return Product::find($record->data['product_id'])->title;
                        }
                        if ($record->type === StoreBannerType::Category && isset($record->data['category_id'])) {
                            return Category::find($record->data['category_id'])->title;
                        }
                        return null;
                    })
                    ->toggleable()
                    ->visible(fn() => StoreBanner::whereIn('type', [StoreBannerType::Product, StoreBannerType::Category])->exists()),
                Tables\Columns\TextColumn::make('clicks')
                    ->label(__('storeBanner.clicks'))
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('warning'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.created_at'))
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.updated_at'))
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'title',
                    'type',
                    'clicks',
                    'created_at',
                    'updated_at',
                ])->filters([
                    SelectFilter::make('type')
                        ->label(__('storeBanner.type'))
                        ->options([
                            StoreBannerType::Product => __('storeBanner.type_product'),
                            StoreBannerType::Category => __('storeBanner.type_category'),
                        ]),
                    Tables\Filters\TrashedFilter::make(),
                ])->defaultFilters([]),
            ])->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ForceDeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])->reorderable('index')
            ->defaultSort('index', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStoreBanners::route('/'),
            'create' => Pages\CreateStoreBanner::route('/create'),
            'edit' => Pages\EditStoreBanner::route('/{record}/edit'),
            'view' => Pages\ViewStoreBanner::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getWidgets(): array
    {
        return [
            StoreBannerStatsWidget::class,
        ];
    }
}
