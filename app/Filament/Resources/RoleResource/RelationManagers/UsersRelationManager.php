<?php

namespace App\Filament\Resources\RoleResource\RelationManagers;

use App\Filament\Actions\CopyFamilyUserIdsAction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Support\Enums\MaxWidth;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use App\Filament\Components\UserFilter;
use App\Filament\Components\UserSelect;
use Illuminate\Database\Eloquent\Builder;
use Archilex\AdvancedTables\AdvancedTables;

class UsersRelationManager extends RelationManager
{
    use AdvancedTables;
    protected static string $relationship = 'users';

    protected static ?string $title = 'المستخدمون';

    protected static ?string $modelLabel = 'مستخدم';

    protected static ?string $pluralModelLabel = 'المستخدمون';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('المستخدم')
                    ->relationship('users', 'full_name')
                    ->searchable()
                    ->preload()
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('full_name')
            ->emptyStateHeading('لا يوجد مستخدمين معينين لهذا الدور')
            ->columns([
                ImageColumn::make('profile_photo_url')
                    ->label('')
                    ->circular()
                    ->url(fn($record) => route('filament.admin.resources.users.view', $record))
                    ->size(40),

                TextColumn::make('family_user_id')
                    ->label('الرقم التعريفي')
                    ->searchable()
                    ->url(fn($record) => route('filament.admin.resources.users.view', $record))
                    ->sortable(),

                TextColumn::make('full_name')
                    ->label('الاسم الكامل')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return full_name_filter($query, $search);
                    }),

                TextColumn::make('roles_count')
                    ->label('عدد الأدوار')
                    ->counts('roles')
                    ->sortable(),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    "family_user_id",
                ])->filters([
                    UserFilter::make('family_user_id')
                        ->label("المستخدم"),

                    SelectFilter::make('roles')
                        ->label('الأدوار الأخرى')
                        ->multiple()
                        ->preload()
                        ->relationship('roles', 'title')
                        ->query(function (Builder $query, array $data): Builder {
                            if (!empty($data['values'])) {
                                return $query->whereHas('roles', function (Builder $roleQuery) use ($data) {
                                    $roleQuery->whereIn('id', $data['values']);
                                });
                            }
                            return $query;
                        }),

                ])->defaultFilters([]),
            ])->filtersLayout(Tables\Enums\FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersFormColumns(1)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->label('إضافة مستخدم')
                    ->form(fn(Tables\Actions\AttachAction $action): array => [
                        UserSelect::make('recordId')
                            ->label('اختر المستخدمين')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->placeholder('ابحث عن المستخدم بالاسم أو الرقم التعريفي')
                            ->afterStateUpdated(function (UserSelect $component, $state) {
                                // Filter out users that already have this role
                                if ($state) {
                                    $existingUserIds = $this->getOwnerRecord()->users()->pluck('id')->toArray();
                                    $filteredState = array_diff((array) $state, $existingUserIds);
                                    $component->state($filteredState);
                                }
                            }),
                    ])
                    ->modalHeading('إضافة مستخدمين إلى الدور')->modalSubmitActionLabel('إضافة المستخدمين')
                    ->modalCancelActionLabel('إلغاء')
                    ->successNotificationTitle('تم إضافة المستخدمين بنجاح'),
            ])
            ->actions([
                Tables\Actions\DetachAction::make()
                    ->label('إزالة')
                    ->modalHeading('إزالة المستخدم من الدور')
                    ->modalDescription('هل أنت متأكد من إزالة هذا المستخدم من الدور؟')
                    ->modalSubmitActionLabel('إزالة')
                    ->modalCancelActionLabel('إلغاء')
                    ->visible(fn() => $this->getOwnerRecord()->name !== 'admin'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('إزالة المحدد')
                        ->modalHeading('إزالة المستخدمين من الدور')
                        ->modalDescription('هل أنت متأكد من إزالة المستخدمين المحددين من الدور؟')
                        ->modalSubmitActionLabel('إزالة')
                        ->modalCancelActionLabel('إلغاء')
                        ->visible(fn() => $this->getOwnerRecord()->name !== 'admin'),
                ]),
                CopyFamilyUserIdsAction::make(),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
