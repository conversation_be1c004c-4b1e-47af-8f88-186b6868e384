<?php

namespace App\Filament\Resources\BankAccountResource\RelationManagers;

use App\Models\BankAccountTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Filters\SelectFilter;

class TransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';

    protected static ?string $title = 'المعاملات';

    protected static ?string $modelLabel = 'معاملة';

    protected static ?string $pluralModelLabel = 'المعاملات';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('amount')
                    ->label('المبلغ')
                    ->numeric()
                    ->required()
                    ->suffix('ريال'),

                Forms\Components\DatePicker::make('due_at')
                    ->label('تاريخ الاستحقاق')
                    ->required(),

                Forms\Components\TextInput::make('reference')
                    ->label('المرجع')
                    ->maxLength(255),

                Forms\Components\TextInput::make('account')
                    ->label('رقم الحساب')
                    ->maxLength(255),

                Forms\Components\Select::make('user_id')
                    ->label('المستخدم')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->nullable(),

                Forms\Components\Toggle::make('anonymous')
                    ->label('معاملة مجهولة')
                    ->default(false),

                Forms\Components\Toggle::make('is_verified')
                    ->label('تم التحقق')
                    ->default(false),

                Forms\Components\Toggle::make('is_refund')
                    ->label('عملية استرداد')
                    ->default(false),

                Forms\Components\Textarea::make('upgrade_notes')
                    ->label('ملاحظات')
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('amount')
            ->columns([
                Tables\Columns\TextColumn::make('amount')
                    ->label('المبلغ')
                    ->riyal()
                    ->sortable(),

                Tables\Columns\TextColumn::make('due_at')
                    ->label('تاريخ الاستحقاق')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('reference')
                    ->label('المرجع')
                    ->searchable()
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('account')
                    ->label('رقم الحساب')
                    ->searchable()
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('المستخدم')
                    ->searchable()
                    ->placeholder('غير محدد'),

                Tables\Columns\IconColumn::make('anonymous')
                    ->label('مجهول')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_verified')
                    ->label('محقق')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_refund')
                    ->label('مسترد')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                TernaryFilter::make('anonymous')
                    ->label('معاملة مجهولة'),

                TernaryFilter::make('is_verified')
                    ->label('تم التحقق'),

                TernaryFilter::make('is_refund')
                    ->label('عملية استرداد'),

                TernaryFilter::make('user_id')
                    ->label('مرتبط بمستخدم')
                    ->nullable(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('إضافة معاملة'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),

                Tables\Actions\EditAction::make()
                    ->label('تعديل'),

                Tables\Actions\DeleteAction::make()
                    ->label('حذف'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),
                ]),
            ]);
    }
}
