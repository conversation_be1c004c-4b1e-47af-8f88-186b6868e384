<?php

namespace App\Filament\Resources\AppLinkResource\Pages;

use App\Filament\Resources\AppLinkResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAppLink extends EditRecord
{
  protected static string $resource = AppLinkResource::class;

  protected function getHeaderActions(): array
  {
    return [
      Actions\ViewAction::make(),
      Actions\DeleteAction::make(),
      Actions\ForceDeleteAction::make(),
      Actions\RestoreAction::make(),
    ];
  }

  protected function mutateFormDataBeforeSave(array $data): array
  {
    // Handle image storage format
    if (!empty($data['image']) && is_string($data['image'])) {
      $imagePath = $data['image'];
      $data['image'] = [
        'path' => str_replace('public/', '', $imagePath),
        'disk' => 'tenant',
      ];
    } elseif (empty($data['image'])) {
      unset($data['image']);
    }

    return $data;
  }
}
