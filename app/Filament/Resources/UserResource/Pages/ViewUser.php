<?php

namespace App\Filament\Resources\UserResource\Pages;

use Filament\Actions;
use App\Enums\UserMaritalStatus;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components;
use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\Fieldset;

class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;

    protected static ?string $navigationLabel = 'تفاصيل المستخدم';

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Grid::make(12)
                    ->schema([
                        Components\Section::make()
                            ->columnSpan([
                                'md' => 4,  // 4 columns on medium screens and up
                                'default' => 12,  // full width (12 columns) on smaller screens
                            ])
                            ->schema([
                                Components\Grid::make(1)
                                    ->schema([
                                        Components\Group::make([
                                            Components\Grid::make([
                                                'default' => 2,    // Stack on mobile (single column)
                                                'sm' => 2,        // 2 columns on small screens
                                                'lg' => 2,        // 2 columns on large screens
                                            ])
                                                ->schema([
                                                    Components\TextEntry::make('family_user_id')
                                                        ->label('userColumns.family_user_id')
                                                        ->hiddenLabel()
                                                        ->translateLabel()
                                                        ->badge()
                                                        ->alignment('start')  // This is a Filament-specific way to align
                                                        ->copyable(),
                                                    Components\TextEntry::make('membership.package.title')
                                                        ->label('userColumns.membership')
                                                        ->hiddenLabel()
                                                        ->translateLabel()
                                                        ->badge()
                                                        ->extraAttributes(['style' => 'line-height: 2;'])
                                                        ->alignment('end')  // This is a Filament-specific way to align
                                                        ->copyable(),
                                                ]),
                                            Components\ImageEntry::make('profile_photo_url')
                                                ->label('userColumns.profile_photo')
                                                ->translateLabel()
                                                ->checkFileExistence(false)
                                                ->disk('public')
                                                ->grow(false)
                                                ->hiddenLabel()
                                                ->circular()
                                                ->alignCenter()
                                                ->withFallbackAvatar(),
                                            Components\TextEntry::make('name')
                                                ->label('userColumns.name')
                                                ->hiddenLabel()
                                                ->alignCenter()
                                                ->html()
                                                ->tooltip(function ($record) {
                                                    return $record->gender === 'MALE' ? 'ذكر' : 'أنثى';
                                                })
                                                ->formatStateUsing(function ($record) {
                                                    $genderIcon = $record->gender === 'MALE'
                                                        ? '<span class="text-xl">♂</span>'
                                                        : '<span class="text-xl">♀</span>';

                                                    $name = "<div class='text-center'><span>{$genderIcon} {$record->name}</span>";

                                                    if ($record->is_dead) {
                                                        $phrase = $record->gender === 'FEMALE' ? 'رحمها الله' : 'رحمه الله';
                                                        $name .= '<br><span class="inline-flex items-center justify-center min-h-6 px-2 py-0.5 text-sm font-medium tracking-tight rounded-lg border border-danger-600 bg-danger-100 text-danger-600 dark:bg-danger-500/10 dark:text-danger-400 dark:border-danger-400 mt-1">' . $phrase . '</span>';
                                                    }

                                                    $name .= '</div>';

                                                    return $name;
                                                })
                                                ->extraAttributes(['class' => 'font-bold rtl:font-[naskh]'])
                                                ->translateLabel(),
                                            Components\TextEntry::make('bio')
                                                ->label('userColumns.bio')
                                                ->hiddenLabel()
                                                ->alignCenter()
                                                ->translateLabel(),
                                            Components\TextEntry::make('father.full_name')
                                                ->url(fn($record): string => self::getUrl(['record' => $record->father_id]))
                                                ->label('userColumns.father_id')
                                                ->translateLabel()
                                                ->inlineLabel()
                                                ->visible(fn($record) => !is_null($record->father)),
                                            Components\TextEntry::make('mother.full_name')
                                                ->url(fn($record): string => self::getUrl(['record' => $record->mother_id]))
                                                ->label('userColumns.mother_id')
                                                ->translateLabel()
                                                ->inlineLabel()
                                                ->visible(fn($record) => !is_null($record->mother)),
                                            Components\TextEntry::make('dob')
                                                ->label('userColumns.dob')
                                                ->inlineLabel()
                                                ->columnSpanFull()
                                                ->translateLabel()
                                                ->formatStateUsing(function ($state) {
                                                    if (!$state) {
                                                        return null;
                                                    }

                                                    $date = \Carbon\Carbon::parse($state);
                                                    $age = $date->age;

                                                    return $date->format('d-m-Y') . ' (' . $age . ')';
                                                })
                                                ->copyableState(function ($state) {
                                                    if (!$state) {
                                                        return null;
                                                    }

                                                    return \Carbon\Carbon::parse($state)->format('d-m-Y');
                                                })
                                                ->copyable()
                                                ->sensitive()
                                                ->visible(fn($record) => !is_null($record->dob)),
                                            Components\TextEntry::make('dod')
                                                ->label('userColumns.dod')
                                                ->inlineLabel()
                                                ->translateLabel()
                                                ->date('Y-m-d')
                                                ->copyable()
                                                ->visible(fn($record) => !is_null($record->dod))
                                                ->sensitive(),

                                            Components\TextEntry::make('national_id')
                                                ->label('userColumns.national_id')
                                                ->translateLabel()
                                                ->inlineLabel()
                                                ->copyable()
                                                ->visible(fn($record) => !is_null($record->national_id))
                                                ->sensitive(),
                                            Components\ViewEntry::make('phone')
                                                ->label('userColumns.phone')
                                                ->view('components.social-icon-link')
                                                ->viewData([
                                                    'icon' => 'heroicon-o-chat-bubble-left-right',
                                                    'url' => fn($record) => 'whatsapp://send?phone=' . preg_replace('/[^0-9]/', '', $record->phone),
                                                    'display' => fn($record) => $record->phone,
                                                    'is_hidden' => fn() => !isShowSensitiveData(),
                                                ])
                                                ->translateLabel()
                                                ->openUrlInNewTab()
                                                ->inlineLabel()
                                                ->visible(fn($record) => !is_null($record->phone)),
                                            Components\TextEntry::make('user_region.title_ar')
                                                ->label('مكان الإقامة')
                                                ->translateLabel()
                                                ->inlineLabel()
                                                ->copyable()
                                                ->sensitive()
                                                ->visible(fn($record) => !is_null($record->user_region)),
                                            Components\TextEntry::make('marital_status')
                                                ->label('الحالة الاجتماعية')
                                                ->translateLabel()
                                                ->inlineLabel()
                                                ->formatStateUsing(function ($state, $record) {
                                                    if (!$state) {
                                                        return null;
                                                    }
                                                    $options = UserMaritalStatus::options($record->gender);

                                                    return $options[$state] ?? $state;
                                                })
                                                ->badge()
                                                ->color(fn(string $state): string => match ($state) {
                                                    UserMaritalStatus::Single => 'gray',
                                                    UserMaritalStatus::Married => 'success',
                                                    UserMaritalStatus::Divorced => 'warning',
                                                    UserMaritalStatus::Widower => 'danger',
                                                    default => 'gray',
                                                })
                                                ->visible(fn($record) => !is_null($record->marital_status)),
                                            Components\TextEntry::make('email')
                                                ->label('userColumns.email')
                                                ->translateLabel()
                                                ->inlineLabel()
                                                ->copyable()
                                                ->sensitive()
                                                ->visible(fn($record) => !is_null($record->email)),
                                            Components\TextEntry::make('branchName')
                                                ->label('userColumns.branch')
                                                ->translateLabel()
                                                ->inlineLabel()
                                                ->sensitive()
                                                ->visible(fn($record) => !is_null($record->branch)),
                                            Components\TextEntry::make('total_voluntary')
                                                ->label('ساعات التطوع')
                                                ->translateLabel()
                                                ->inlineLabel()
                                                ->getStateUsing(fn($record) => $record->total_voluntary),
                                            Components\ViewEntry::make('linkedin_url')
                                                ->label('رابط لنكدان')
                                                ->view('components.social-icon-link')
                                                ->viewData([
                                                    'icon' => 'heroicon-s-link',
                                                    'url' => fn($record) => $record->linkedin_url,
                                                    'display' => fn($record) => 'رابط لنكدان',
                                                    'is_hidden' => fn() => !isShowSensitiveData(),
                                                ])
                                                ->translateLabel()
                                                ->openUrlInNewTab()
                                                ->inlineLabel()
                                                ->visible(fn($record) => !is_null($record->linkedin_url)),

                                            Fieldset::make('معلومات النظام')
                                                ->schema([
                                                    Components\TextEntry::make('created_at')
                                                        ->translateLabel()
                                                        ->dateTooltip('Y-m-d')
                                                        ->formatStateUsing(fn($record) => $record->created_at->diffForHumans())
                                                        ->visible(fn($record) => !is_null($record->created_at)),
                                                    Components\TextEntry::make('updated_at')
                                                        ->translateLabel()
                                                        ->dateTooltip('Y-m-d')
                                                        ->formatStateUsing(fn($record) => $record->updated_at->diffForHumans())
                                                        ->visible(fn($record) => !is_null($record->updated_at)),
                                                ]),
                                        ]),
                                    ]),
                            ])->compact(),
                        Components\Section::make()
                            ->columnSpan([
                                'md' => 8,  // 8 columns on medium screens and up
                                'default' => 12,  // full width (12 columns) on smaller screens
                            ])
                            ->schema([
                                Components\ViewEntry::make('family_tree')
                                    ->view('filament.components.family-tree-wrapper', [
                                        'user' => $this->record,
                                    ]),
                            ]),

                    ]),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
