<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\User;
use Filament\Resources\Pages\Page; // Stays as Page since it's a Filament page
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
// use Illuminate\Validation\ValidationException; // Not directly used in provided code
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log; // Good for debugging if needed

class FamilyBuilder extends Page // Stays as Page
{
    protected static string $resource = UserResource::class;
    protected static ?string $title = 'منشئ شجرة العائلة';
    protected static string $view = 'filament.resources.user-resource.pages.family-builder';

    // Properties for the form on the right side (as used by <PERSON>)
    // These are NOT directly bound with wire:model in the PHP,
    // but are used by your $wire.addMember and $wire.updateMember calls from Alpine
    // The form's data is managed by <PERSON>'s `formData`.

    // Properties for the tree data (if you were to entangle, but not used in your current Alpine)
    // public $treeMembersData = []; // Not directly used if Alpine fetches via methods

    // Modal controls (if modal was Livewire-controlled, but your form is a separate Alpine component)
    // public $showFormModal = false;
    // public $isEditing = false;

    // These are from your original code, seem to be remnants or for a different form setup.
    // If your current Alpine form manages state, these might not be needed in PHP.
    // public $name, $gender = 'MALE', $father_id, $mother_id, $bio, $dob, $dod, $is_dead = false, $phone, $email, $userId;

    // No listeners needed if Alpine is driving refreshes by recalling methods.
    // protected function getListeners(): array
    // {
    //     return [
    //         'refreshTree' => '$refresh', // This would re-render the Livewire component
    //     ];
    // }


    // Your existing getMembers method - This is called by Alpine's init() and fetchMembers()
    // It should return the ROOT members, or search results that are treated as roots.
    public function getMembers($searchTerm = null) // searchTerm argument added
    {
        $query = User::withoutGlobalScope('family_users')
            ->where('gender', 'MALE');

        if ($searchTerm) {
            // When searching, we return matching users. They will be displayed as top-level in the tree.
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                    ->orWhere('email', 'like', "%{$searchTerm}%")
                    ->orWhere('phone', 'like', "%{$searchTerm}%");
            });
        } else {
            // No search term: Get ROOT members (no father_id) that HAVE MALE CHILDREN
            $query->whereNull('father_id')
                ->whereHas('children', function ($subQuery) {
                    $subQuery->withoutGlobalScope('family_users'); // If needed for children check
                });
        }

        $users = $query->orderBy('name')->get();

        return $users->map(function ($item) {
            return $this->formatMemberForAlpine($item);
        })->values()->all();
    }

    // New: Search method called by Alpine (similar to getMembers with a search term)
    public function search($searchTerm)
    {
        // The getMembers method already handles searchTerm, so we can reuse it.
        return $this->getMembers($searchTerm);
    }

    // Your existing getChildren method - This is fine
    public function getChildren($userId)
    {
        $childrenQuery = User::withoutGlobalScope('family_users')
            ->where('father_id', $userId)
            ->orderBy('name');

        $children = $childrenQuery->get();

        return $children->map(function ($item) {
            return $this->formatMemberForAlpine($item);
        })->values()->all();
    }

    // Helper to format user data for Alpine consistently
    protected function formatMemberForAlpine(User $item)
    {
        $hasChildren = User::withoutGlobalScope('family_users')
            ->where('father_id', $item->id)
            ->exists();

        return [
            'id' => $item->id,
            'name' => $item->name,
            'gender' => $item->gender, // Added for completeness, form needs it
            'father_id' => $item->father_id,
            'mother_id' => $item->mother_id,
            'bio' => $item->bio,
            'dob' => $item->dob ? $item->dob->format('Y-m-d') : null,
            'dod' => $item->dod ? $item->dod->format('Y-m-d') : null,
            'is_dead' => (bool)$item->is_dead,
            'phone' => $item->phone,
            'email' => $item->email,
            'hasChildren' => $hasChildren, // Alpine uses this as 'hasChildren'
            // Alpine manages 'children', 'depth', 'expandedNodes' client-side
        ];
    }

    // New: selectUser method called by Alpine to get data for the edit form
    public function selectUser($userId)
    {
        $user = User::withoutGlobalScope('family_users')->find($userId);
        if ($user) {
            return $this->formatMemberForAlpine($user); // Return full formatted data
        }
        return null; // Or throw an error/send notification
    }


    // New: addMember method for the form
    public function addMember(array $formData)
    {
        // Validate formData (adjust rules as needed)
        $validatedData = validator($formData, [
            'name' => 'required|string|max:255',
            'gender' => 'required|in:male,female', // Your form has male/female
            'father_id' => 'nullable|sometimes|exists:users,id', // sometimes if it might not be present
            'mother_id' => 'nullable|sometimes|exists:users,id',
            'email' => ['nullable', 'email', 'max:255', 'unique:users,email'],
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string',
            'dob' => 'nullable|date_format:Y-m-d',
            'dod' => 'nullable|date_format:Y-m-d|after_or_equal:dob',
            'is_dead' => 'boolean',
        ])->validate();

        // Ensure gender is stored as MALE/FEMALE if your DB expects uppercase
        $validatedData['gender'] = strtoupper($validatedData['gender']);
        if ($validatedData['gender'] !== 'MALE') {
            // If this tree is strictly for males, you might want to enforce it here or reject
            // For now, we allow it based on form, but tree display is filtered to MALE by getMembers/getChildren
        }
        // Convert empty strings to null for nullable fields if necessary
        $validatedData['father_id'] = $validatedData['father_id'] === '' ? null : $validatedData['father_id'];
        $validatedData['mother_id'] = $validatedData['mother_id'] === '' ? null : $validatedData['mother_id'];


        DB::beginTransaction();
        try {
            User::create($validatedData);
            DB::commit();
            Notification::make()->title('تم إضافة العضو بنجاح')->success()->send();
            // Alpine will call fetchMembers() to refresh the tree
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error adding member: ' . $e->getMessage());
            Notification::make()->title('خطأ في الإضافة')->body('حدث خطأ أثناء محاولة إضافة العضو.')->danger()->send();
            // Optionally re-throw or return an error state
        }
    }

    // New: updateMember method for the form
    public function updateMember($userId, array $formData)
    {
        $user = User::withoutGlobalScope('family_users')->find($userId);
        if (!$user) {
            Notification::make()->title('خطأ')->body('لم يتم العثور على العضو للتحديث.')->danger()->send();
            return;
        }

        $validatedData = validator($formData, [
            'name' => 'required|string|max:255',
            'gender' => 'required|in:male,female',
            'father_id' => 'nullable|sometimes|exists:users,id',
            'mother_id' => 'nullable|sometimes|exists:users,id',
            'email' => ['nullable', 'email', 'max:255', \Illuminate\Validation\Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string',
            'dob' => 'nullable|date_format:Y-m-d', // HTML date input sends Y-m-d
            'dod' => 'nullable|date_format:Y-m-d|after_or_equal:dob',
            'is_dead' => 'boolean',
        ])->validate();

        $validatedData['gender'] = strtoupper($validatedData['gender']);
        $validatedData['father_id'] = $validatedData['father_id'] === '' ? null : $validatedData['father_id'];
        $validatedData['mother_id'] = $validatedData['mother_id'] === '' ? null : $validatedData['mother_id'];

        DB::beginTransaction();
        try {
            $user->update($validatedData);
            DB::commit();
            Notification::make()->title('تم تحديث بيانات العضو بنجاح')->success()->send();
            // Alpine will call fetchMembers() to refresh the tree
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error updating member ' . $userId . ': ' . $e->getMessage());
            Notification::make()->title('خطأ في التحديث')->body('حدث خطأ أثناء محاولة تحديث بيانات العضو.')->danger()->send();
        }
    }


    // Your existing deleteMember method - called by Alpine's deleteMember()
    // It was missing the $id parameter in your original code.
    public function deleteMember($id)
    {
        DB::beginTransaction();
        try {
            $user = User::withoutGlobalScope('family_users')->find($id);
            if (!$user) {
                throw new ModelNotFoundException('لم يتم العثور على العضو.');
            }

            // Check if the user has MALE children for this tree type
            if (User::withoutGlobalScope('family_users')->where('father_id', $id)->where('gender', 'MALE')->exists()) {
                Notification::make()
                    ->title('لا يمكن الحذف')
                    ->body('لا يمكن حذف عضو لديه أبناء (ذكور) في الشجرة. يرجى حذف الأبناء أولاً أو نقلهم.')
                    ->danger()
                    ->send();
                DB::rollBack(); // Rollback before returning
                return;
            }

            $user->delete();
            DB::commit();
            Notification::make()->title('تم حذف العضو بنجاح')->success()->send();
            // Alpine will call fetchMembers() to refresh the tree
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error deleting member ' . $id . ': ' . $e->getMessage());
            Notification::make()->title('خطأ في الحذف')->body('خطأ: ' . $e->getMessage())->danger()->send();
        }
    }

    // The confirmDeleteMember and form state methods (openAddModal, saveMember, etc.) from your
    // original file are not strictly needed if the form and confirmation are handled entirely by
    // the Alpine component on the right side and it calls the new addMember/updateMember/deleteMember methods.
    // I've kept deleteMember as it's directly called.
    // If you had a Livewire-controlled modal, then openAddModal etc. would be relevant.
}
