<?php

namespace App\Filament\Resources\AnonymousTransactionResource\Pages;

use App\Filament\Resources\AnonymousTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListAnonymousTransactions extends ListRecords
{
    protected static string $resource = AnonymousTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('إضافة معاملة مجهولة'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('الكل')
                ->badge(fn () => $this->getModel()::where('anonymous', true)->where('is_verified', false)->count()),

            'recent' => Tab::make('الحديثة')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('created_at', '>=', now()->subDays(7)))
                ->badge(fn () => $this->getModel()::where('anonymous', true)
                    ->where('is_verified', false)
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count()),

            'refunds' => Tab::make('المستردة')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_refund', true))
                ->badge(fn () => $this->getModel()::where('anonymous', true)
                    ->where('is_verified', false)
                    ->where('is_refund', true)
                    ->count()),

            'linked' => Tab::make('مرتبطة بمستخدمين')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotNull('user_id'))
                ->badge(fn () => $this->getModel()::where('anonymous', true)
                    ->where('is_verified', false)
                    ->whereNotNull('user_id')
                    ->count()),
        ];
    }
}
