<?php

namespace App\Filament\Resources\AnonymousTransactionResource\Pages;

use App\Filament\Resources\AnonymousTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAnonymousTransaction extends EditRecord
{
    protected static string $resource = AnonymousTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('عرض'),
            
            Actions\DeleteAction::make()
                ->label('حذف'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
