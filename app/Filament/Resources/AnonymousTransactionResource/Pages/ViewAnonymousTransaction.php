<?php

namespace App\Filament\Resources\AnonymousTransactionResource\Pages;

use App\Filament\Resources\AnonymousTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Support\Enums\FontWeight;

class ViewAnonymousTransaction extends ViewRecord
{
    protected static string $resource = AnonymousTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('تعديل'),

            Actions\Action::make('refund')
                ->label('استرداد')
                ->icon('heroicon-o-arrow-uturn-left')
                ->color('warning')
                ->visible(fn () => !$this->record->is_refund)
                ->requiresConfirmation()
                ->modalHeading('تأكيد عملية الاسترداد')
                ->modalDescription('هل أنت متأكد من أنك تريد تحويل هذه المعاملة إلى عملية استرداد؟')
                ->action(function () {
                    $this->record->update(['is_refund' => true]);

                    $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                }),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make(3)
                    ->schema([
                        Section::make('تفاصيل المعاملة')
                            ->schema([
                                TextEntry::make('reference')
                                    ->label('المرجع')
                                    ->copyable()
                                    ->icon('heroicon-o-tag')
                                    ->weight(FontWeight::Bold)
                                    ->placeholder('غير محدد'),

                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('bank_account.title')
                                            ->label('الحساب البنكي')
                                            ->icon('heroicon-o-building-library'),

                                        TextEntry::make('amount')
                                            ->label('المبلغ')
                                            ->riyal()
                                            ->icon('heroicon-o-currency-dollar'),
                                    ]),

                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('account')
                                            ->label('رقم حساب الداعم')
                                            ->placeholder('غير محدد')
                                            ->copyable()
                                            ->icon('heroicon-o-credit-card'),

                                        TextEntry::make('due_at')
                                            ->label('تاريخ الاستحقاق')
                                            ->date()
                                            ->icon('heroicon-o-calendar-days'),
                                    ]),

                                TextEntry::make('upgrade_notes')
                                    ->label('ملاحظات')
                                    ->placeholder('لا توجد ملاحظات')
                                    ->columnSpanFull(),

                                Grid::make(3)
                                    ->schema([
                                        IconEntry::make('anonymous')
                                            ->label('معاملة مجهولة')
                                            ->boolean(),

                                        IconEntry::make('is_verified')
                                            ->label('تم التحقق')
                                            ->boolean(),

                                        IconEntry::make('is_refund')
                                            ->label('عملية استرداد')
                                            ->boolean(),
                                    ]),
                            ])
                            ->columnSpan(2),

                        Section::make('معلومات إضافية')
                            ->schema([
                                TextEntry::make('user.name')
                                    ->label('مرتبط بمستخدم')
                                    ->placeholder('غير مرتبط')
                                    ->url(fn () => $this->record->user ?
                                        \App\Filament\Resources\UserResource::getUrl('view', ['record' => $this->record->user->id]) : null
                                    ),

                                TextEntry::make('bank_account.transactions_count')
                                    ->label('عدد المعاملات للحساب')
                                    ->state(fn () => $this->record->bank_account->transactions()->count())
                                    ->icon('heroicon-o-calculator'),

                                TextEntry::make('bank_account.transactions_sum')
                                    ->label('إجمالي المبالغ للحساب')
                                    ->state(fn () => $this->record->bank_account->transactions()->sum('amount'))
                                    ->riyal()
                                    ->icon('heroicon-o-banknotes'),

                                TextEntry::make('created_at')
                                    ->label('تاريخ الإنشاء')
                                    ->dateTime(),

                                TextEntry::make('updated_at')
                                    ->label('تاريخ التحديث')
                                    ->dateTime(),
                            ])
                            ->columnSpan(1),
                    ])
            ]);
    }
}
