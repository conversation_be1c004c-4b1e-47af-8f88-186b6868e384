<?php

namespace App\Filament\Resources\AnonymousTransactionResource\Pages;

use App\Filament\Resources\AnonymousTransactionResource;
use Filament\Resources\Pages\CreateRecord;

class CreateAnonymousTransaction extends CreateRecord
{
    protected static string $resource = AnonymousTransactionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['anonymous'] = true;
        $data['is_verified'] = false;
        
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
