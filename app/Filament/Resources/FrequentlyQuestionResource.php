<?php

namespace App\Filament\Resources;

use App\Filament\Forms\Components\EditorJS;
use App\Filament\Resources\FrequentlyQuestionResource\Pages;
use App\Models\FrequentlyQuestion;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class FrequentlyQuestionResource extends Resource
{
    protected static ?string $model = FrequentlyQuestion::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    protected static ?string $navigationGroup = "التطبيق";
    protected static ?string $navigationLabel = "الأسئلة الشائعة";
    protected static ?string $pluralNavigationLabel = "الأسئلة الشائعة";
    protected static ?string $pluralModelLabel = "الأسئلة الشائعة";
    protected static ?string $modelLabel = "سؤال";

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\Section::make('معلومات السؤال')
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->label("عنوان السؤال")
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Hidden::make('index')
                                    ->default(fn() => FrequentlyQuestion::max('index') + 1),
                            ])
                            ->columnSpan(1),

                        Forms\Components\Section::make('المحتوى')
                            ->schema([
                                EditorJS::make('content')
                                    ->label("محتوى الإجابة")
                                    ->disabledOn("view")
                                    ->required(),
                            ])
                            ->columnSpan(2),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('index')
                    ->label("#")
                    ->sortable()
                    ->width(60),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->label("عنوان السؤال")
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')->translateLabel()->dateTime('d-m-Y H:i'),
                Tables\Columns\TextColumn::make('updated_at')->translateLabel()->dateTime('d-m-Y H:i'),
            ])
            ->defaultSort('index', 'asc')
            ->reorderable('index')
            ->filters([
                //
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ])
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFrequentlyQuestions::route('/'),
            'create' => Pages\CreateFrequentlyQuestion::route('/create'),
            'edit' => Pages\EditFrequentlyQuestion::route('/{record}/edit'),
            'view' => Pages\ViewFrequentlyQuestion::route('/{record}'),
        ];
    }
}
