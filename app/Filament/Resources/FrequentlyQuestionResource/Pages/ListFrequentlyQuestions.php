<?php

namespace App\Filament\Resources\FrequentlyQuestionResource\Pages;

use App\Filament\Resources\FrequentlyQuestionResource;
use App\Filament\Resources\FrequentlyQuestionResource\Widgets\FrequentlyQuestionWidgets;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Archilex\AdvancedTables\AdvancedTables;

class ListFrequentlyQuestions extends ListRecords
{
    use AdvancedTables;
    protected static string $resource = FrequentlyQuestionResource::class;
    protected static ?string $navigationLabel = 'الأسئلة';

    public function getHeaderWidgets(): array
    {
        return [
            FrequentlyQuestionWidgets::class,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
