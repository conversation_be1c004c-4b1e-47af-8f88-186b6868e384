<?php

namespace App\Filament\Resources\NewsResource\Pages;

use App\Filament\Resources\NewsResource;
use App\Filament\Resources\NewsResource\Widgets\NewsOverviewWidget;
use App\Filament\Resources\NewsResource\Widgets\NewsWidgets;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Archilex\AdvancedTables\AdvancedTables;

class ListNews extends ListRecords
{
    use AdvancedTables;
    protected static string $resource = NewsResource::class;
    protected static ?string $navigationLabel = 'الأخبار';


    public function getHeaderWidgets(): array
    {
        return [
            NewsWidgets::class,
        ];
    }



    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getSubNavigation(): array
    {
        return static::getResource()::getRecordSubNavigation($this);
    }
}
