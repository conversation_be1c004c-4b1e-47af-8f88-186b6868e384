<?php

namespace App\Filament\Resources\NewsResource\Pages;

use App\Filament\Resources\NewsResource;
use App\Filament\Resources\NewsResource\Widgets\NewsWidgets;
use App\Models\News;
use Filament\Actions\Action;
use Filament\Pages\Page;
use Filament\Resources\Pages\ViewRecord;
use Filament\Tables\Columns\TextColumn;

class ViewNews extends ViewRecord
{
    protected static string $resource = NewsResource::class;
    protected static ?string $navigationIcon = 'heroicon-o-eye';

    protected function getHeaderActions(): array
    {
        return [
            Action::make('edit')
                ->url(route('filament.admin.resources.news.edit', ['record' => $this->getRecord()]))
                ->icon("heroicon-o-pencil")
                ->label("تعديل"),
        ];
    }
}
