<?php

namespace App\Filament\Resources\MarriageResource\Pages;

use App\Filament\Resources\MarriageResource;
use App\Enums\MarriageStatus;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateMarriage extends CreateRecord
{
    protected static string $resource = MarriageResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['is_divorced'] = $data['status'] === MarriageStatus::Divorced;

        if ($data['status'] !== MarriageStatus::Divorced) {
            $data['divorced_at'] = null;
        }

        unset($data['husband_type']);
        unset($data['wife_type']);
        return $data;
    }
}
