<?php

namespace App\Filament\Resources\TransactionResource\Pages;

use Auth;
use Exception;
use Filament\Forms;
use Filament\Actions;
use App\Models\Supporter;
use Illuminate\Support\Facades\Log;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components;
use App\Filament\Resources\UserResource;
use App\Filament\Resources\TransactionResource;
use Illuminate\Support\HtmlString;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Str;

class ViewTransaction extends ViewRecord
{
    protected static string $resource = TransactionResource::class;

    protected static ?string $title = 'عرض العملية';

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Grid::make(8)
                    ->schema([
                        Components\Grid::make()
                            ->columnSpan(6)
                            ->schema([
                                // Header Stats Section
                                Components\Section::make()
                                    ->schema([
                                        Components\Split::make([
                                            Components\Grid::make()
                                                ->columns(2)
                                                ->schema([
                                                    Components\TextEntry::make('uuid')
                                                        ->label('رقم العملية')
                                                        ->weight(FontWeight::Bold)
                                                        ->size(Components\TextEntry\TextEntrySize::Large),

                                                    Components\TextEntry::make('payment_status')
                                                        ->label('حالة العملية')
                                                        ->formatStateUsing(fn($state) => __("transaction.enum.payment_status.{$state}"))
                                                        ->badge()
                                                        ->color(fn($state) => match($state) {
                                                            'paid' => 'success',
                                                            'pending' => 'warning',
                                                            'refunded' => 'info',
                                                            'no_payment' => 'gray',
                                                            default => 'danger',
                                                        })
                                                        ->size(Components\TextEntry\TextEntrySize::Large),
                                                ]),
                                        ])->from('md'),
                                    ])
                                    ->compact(),

                                // Main Content Tabs
                                Components\Tabs::make('Main Content')
                                    ->tabs([
                                        Components\Tabs\Tab::make('transaction_details')
                                            ->label('تفاصيل العملية')
                                            ->icon('heroicon-o-information-circle')
                                            ->schema([
                                                Components\Split::make([
                                                    Components\Grid::make(2)
                                                        ->schema([
                                                            Components\TextEntry::make('product.title')
                                                                ->label('المنتج')
                                                                ->weight(FontWeight::Bold),

                                                            Components\TextEntry::make('amount')
                                                                ->label('المبلغ')
                                                                ->riyal()
                                                                ->weight(FontWeight::Bold),

                                                            Components\TextEntry::make('payment.due_deposit')
                                                                ->label('المبلغ بعد الخصم')
                                                                ->state(fn($record) => $record->payment?->due_deposit)
                                                                ->visible(fn($record) => $record->payment?->due_deposit)
                                                                ->riyal(3)
                                                                ->weight(FontWeight::Bold),

                                                            Components\TextEntry::make('payment.service_charge')
                                                                ->label('رسوم الخدمة')
                                                                ->state(fn($record) => $record->payment?->service_charge + $record->payment?->vat_amount)
                                                                ->visible(fn($record) => $record->payment?->service_charge)
                                                                ->riyal(3)
                                                                ->weight(FontWeight::Bold),

                                                            Components\IconEntry::make('is_recurring')
                                                                ->label('دفع متكرر')
                                                                ->boolean(),

                                                            Components\TextEntry::make('confirmed_at')
                                                                ->label('تاريخ تأكيد الدفع')
                                                                ->dateTime(),
                                                        ]),
                                                ])->from('lg'),
                                            ]),

                                        Components\Tabs\Tab::make('payment_details')
                                            ->label('بيانات الدفع')
                                            ->icon('heroicon-o-credit-card')
                                            ->schema([
                                                Components\Split::make([
                                                    Components\Grid::make(2)
                                                        ->schema([
                                                            Components\TextEntry::make('payment.arabicTitle')
                                                                ->label('بوابة الدفع')
                                                                ->default('غير معروف'),

                                                            Components\TextEntry::make('payment.payment_method')
                                                                ->label('طريقة الدفع')
                                                                ->visible(fn($record) => $record->payment)
                                                                ->default('غير متوفر'),

                                                            Components\TextEntry::make('payment.card_number')
                                                                ->formatStateUsing(fn($record) => Str::mask($record->payment?->card_number, 'x', 0, 12))
                                                                ->label('رقم البطاقة'),

                                                            Components\TextEntry::make('payment.paid_at')
                                                                ->label('تاريخ تأكيد الدفع')
                                                                ->date('Y-m-d H:i:s')
                                                                ->default(fn($record) => $record->confirmed_at),
                                                        ]),
                                                ])->from('lg'),
                                            ]),

                                        Components\Tabs\Tab::make('refund_details')
                                            ->label('معلومات الاسترداد')
                                            ->icon('heroicon-o-arrow-path')
                                            ->schema([
                                                Components\Split::make([
                                                    Components\Grid::make(2)
                                                        ->schema([
                                                            Components\TextEntry::make('refunded_at')
                                                                ->label('تاريخ الاسترداد')
                                                                ->dateTime(),

                                                            Components\TextEntry::make('payment.refund_amount')
                                                                ->label('مبلغ الاسترداد')
                                                                ->riyal()
                                                                ->default('غير متوفر'),

                                                            Components\TextEntry::make('refund_notes')
                                                                ->label('سبب الاسترداد')
                                                                ->default('لا يوجد'),

                                                            Components\TextEntry::make('refunded_by.full_name')
                                                                ->label('الاسترداد بواسطة')
                                                                ->default('غير متوفر'),

                                                            Components\TextEntry::make('payment.refund_id')
                                                                ->label('رقم الاسترداد')
                                                                ->visible(fn($record) => $record->payment && $record->payment?->refund_id)
                                                                ->columnSpanFull(),
                                                        ]),
                                                ])->from('lg'),
                                            ])
                                            ->visible(fn($record) => $record->payment && $record->payment?->is_refunded),

                                        Components\Tabs\Tab::make('customer_details')
                                            ->label('بيانات العميل')
                                            ->icon('heroicon-o-user')
                                            ->schema([
                                                Components\Split::make([
                                                    Components\Grid::make(2)
                                                        ->schema([
                                                            Components\TextEntry::make('user.full_name')
                                                                ->formatStateUsing(fn($record) => $record->user?->full_name ?? $record->user_name)
                                                                ->url(fn($record) => $record->user ? UserResource::getUrl('view', ['record' => $record->user->id]) : null)
                                                                ->openUrlInNewTab()
                                                                ->label('المستخدم')
                                                                ->icon('heroicon-o-user'),

                                                            Components\TextEntry::make('user_phone')
                                                                ->label('الهاتف')
                                                                ->icon('heroicon-o-phone'),
                                                        ]),
                                                ])->from('lg'),
                                            ]),
                                    ])
                                    ->columnSpanFull(),
                            ]),

                        Components\Grid::make()
                            ->columnSpan(2)
                            ->schema([
                                $this->sideInfoSection(),
                            ]),
                    ]),
            ]);
    }

    protected function sideInfoSection()
    {
        return Components\Section::make('معلومات العملية')
            ->schema([
                Components\Grid::make(1)
                    ->schema([
                        Components\TextEntry::make('created_at')
                            ->label('تاريخ الإنشاء')
                            ->dateTime()
                            ->icon('heroicon-o-calendar'),

                        Components\TextEntry::make('updated_at')
                            ->label('آخر تحديث')
                            ->dateTime()
                            ->icon('heroicon-o-clock'),
                    ]),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('refund')
                ->label('استرداد')
                ->icon('heroicon-o-arrow-path')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('استرداد العملية')
                ->modalDescription('أدخل المبلغ الذي ترغب في استرداده. الحد الأقصى للمبلغ هو القيمة الكاملة للعملية.')
                ->form([
                    Forms\Components\TextInput::make('amount')
                        ->label('مبلغ الاسترداد')
                        ->numeric()
                        ->required()
                        ->placeholder('أدخل مبلغ الاسترداد')
                        ->suffixIcon('heroicon-o-currency-dollar')
                        ->minValue(1)
                        ->maxValue(fn($record) => $record->amount)
                        ->helperText(fn($record) => 'الحد الأقصى للمبلغ: ' . $record->amount),
                    Forms\Components\Textarea::make('notes')
                        ->label('سبب الاسترداد')
                        ->placeholder('أدخل سبب الاسترداد')
                        ->required(),
                ])
                ->modalSubmitActionLabel('تأكيد الاسترداد')
                ->modalCancelActionLabel('إلغاء')
                ->visible(function () {
                    // Only show for confirmed transactions that haven't been refunded
                    // and have a payment gateway that supports refunds
                    $record = $this->getRecord();
                    $payment = $record->payment;
                    $gateway = $record->gateway();
                    return $record->confirmed_at &&
                        !$record->refunded_at &&
                        $payment &&
                        $gateway &&
                        $gateway->supportsRefunds();
                })
                ->action(function (array $data, $record) {
                    $gateway = $record->gateway();
                    $payment = $record->payment;

                    try {
                        // Process refund through the transaction
                        $result = $gateway->refund($payment, $data['amount']);

                        if ($result['success']) {
                            // If this is a recurring payment, cancel it
                            /*if ($record->is_recurring && $record->recurringPayment) {
                                $record->recurringPayment->status = 'cancelled';
                                $record->recurringPayment->save();
                            }*/

                            $refundedAt = now();
                            // Mark transaction as refunded
                            $record->refunded_at = $refundedAt;
                            $record->payment_status = 'refunded';
                            $record->refunded_by_id = Auth::id();
                            $record->refund_reference = $result['refund_id'] ?? null;
                            $record->refund_notes = $data['notes'];
                            $record->save();

                            $record->payment?->update([
                                'status' => 'refunded',
                                'refunded_at' => $refundedAt,
                                'refund_amount' => $data['amount'],
                                'refund_id' => $result['refund_id'] ?? null,
                                'refund_reference' => $result['refund_reference'] ?? null,
                                'refund_invoice_id' => $result['refund_invoice_id'] ?? null,
                            ]);

                            // Refresh the record to show the updated status
                            $this->refreshFormData(['record']);

                            Notification::make()
                                ->title('تم الاسترداد بنجاح')
                                ->body('تم استرداد العملية بنجاح.')
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('فشل الاسترداد')
                                ->body($result['message'] ?? 'تعذر معالجة الاسترداد.')
                                ->danger()
                                ->send();
                        }
                    } catch (Exception $e) {
                        Log::error('Error processing refund: ' . $e->getMessage(), [
                            'transaction_uuid' => $record->uuid,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                        ]);

                        Notification::make()
                            ->title('خطأ')
                            ->body('حدث خطأ: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('download_invoice')
                ->label('تحميل الفاتورة')
                ->icon('heroicon-o-document-arrow-down')
                ->color('gray')
                ->url(fn() => $this->getRecord()->pdf_url)
                ->openUrlInNewTab()
                ->visible(fn() => $this->getRecord()->pdf_url !== null),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load relationships that might be needed in the view
        $this->getRecord()->load(['product', 'user', 'refunded_by', 'payment']);

        return $data;
    }
}
