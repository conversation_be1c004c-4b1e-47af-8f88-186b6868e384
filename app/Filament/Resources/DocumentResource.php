<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DocumentResource\Pages;
use App\Filament\Resources\DocumentResource\RelationManagers;
use App\Models\Document;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DocumentResource extends Resource
{
    protected static ?string $model = Document::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $recordTitleAttribute = 'title';

    protected static ?string $navigationGroup = 'التاريخ العائلي';
    protected static ?int $navigationSort = -95;

    public static function getNavigationLabel(): string
    {
        return __('document.resource.documents');
    }

    public static function getModelLabel(): string
    {
        return __('document.resource.document');
    }

    public static function getPluralModelLabel(): string
    {
        return __('document.resource.documents');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->options(__('document.enum.type'))
                            ->required()
                            ->live()
                            ->label(__('document.fields.type')),

                        Forms\Components\Select::make('event_type')
                            ->options(__('document.enum.event_type'))
                            ->visible(fn(Forms\Get $get) => $get('type') === 'EVENT')
                            ->label(__('document.fields.event_type')),

                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->label(__('document.fields.title')),

                        Forms\Components\DatePicker::make('gregorian_date')
                            ->label(__('document.fields.gregorian_date')),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('hijri_day')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(30)
                                    ->label(__('document.fields.hijri_day')),

                                Forms\Components\TextInput::make('hijri_month')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(12)
                                    ->label(__('document.fields.hijri_month')),

                                Forms\Components\TextInput::make('hijri_year')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(9999)
                                    ->label(__('document.fields.hijri_year')),
                            ])->columnSpan('full'),

                        Forms\Components\Textarea::make('details')
                            ->required()
                            ->maxLength(20000)
                            ->label(__('document.fields.details')),

                        Forms\Components\Section::make(__('document.fields.attachments'))
                            ->schema([
                                Forms\Components\Repeater::make('attachments')
                                    // Remove ->relationship('attachments') to prevent auto-saving with null paths
                                    // Attachments will be handled manually in CreateDocument/EditDocument pages
                                    ->default(function ($record) {
                                        if (!$record || !$record->exists) {
                                            return [];
                                        }

                                        return $record->attachments->map(function ($attachment) {
                                            return [
                                                'id' => $attachment->id,
                                                'title' => $attachment->title,
                                                'file_type' => $attachment->file_type,
                                                'type' => $attachment->type,
                                                'user_id' => $attachment->user_id,
                                                'path' => $attachment->path,
                                                'thumb_path' => $attachment->thumb_path,
                                                '_existing_attachment' => true, // Flag to identify existing attachments
                                            ];
                                        })->toArray();
                                    })
                                    ->schema([
                                        Forms\Components\TextInput::make('title')
                                            ->required()
                                            ->label(__('document.fields.title'))
                                            ->placeholder('Enter attachment title')
                                            ->helperText('Provide a descriptive title for this attachment'),

                                        // Show existing file info for edit mode
                                        Forms\Components\Placeholder::make('existing_file_info')
                                            ->label('Current File')
                                            ->content(function ($get) {
                                                $isExisting = $get('_existing_attachment');
                                                $fileType = $get('file_type');
                                                $id = $get('id');

                                                if (!$isExisting || !$id) {
                                                    return 'No file uploaded yet';
                                                }

                                                $icons = [
                                                    'pdf' => '📄',
                                                    'image' => '🖼️',
                                                    'word' => '📝',
                                                    'excel' => '📊',
                                                    'powerpoint' => '📽️',
                                                    'text' => '📃',
                                                    'document' => '📁',
                                                ];

                                                $icon = $icons[$fileType] ?? '📁';
                                                $fileTypeDisplay = strtoupper($fileType);

                                                return "{$icon} {$fileTypeDisplay} - Existing file (ID: {$id})";
                                            })
                                            ->visible(fn($get) => $get('_existing_attachment')),

                                        // File upload field - NOT saved to database, only for processing
                                        Forms\Components\FileUpload::make('new_file_upload')
                                            ->label(fn($get) => $get('_existing_attachment') ? 'Replace File' : 'Upload File')
                                            ->required(fn($get) => !$get('_existing_attachment'))
                                            ->directory('temp-uploads')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->downloadable()
                                            ->previewable()
                                            ->openable()
                                            ->maxSize(20480) // 20MB
                                            ->helperText('Upload any document type (Max: 20MB)')
                                            ->live()
                                            // Remove dehydrated(false) so the field is included in form submission
                                            ->afterStateUpdated(function ($state, $set, $get) {
                                                if ($state) {
                                                    try {
                                                        // Auto-detect file type based on uploaded file
                                                        $filePath = is_array($state) ? $state[0] : $state;
                                                        if ($filePath) {
                                                            $fullPath = storage_path('app/public/' . $filePath);
                                                            if (file_exists($fullPath)) {
                                                                $mimeType = mime_content_type($fullPath);

                                                                // Determine file type category
                                                                $fileType = 'document'; // Default
                                                                if ($mimeType === 'application/pdf') {
                                                                    $fileType = 'pdf';
                                                                } elseif (str_starts_with($mimeType, 'image/')) {
                                                                    $fileType = 'image';
                                                                } elseif (str_starts_with($mimeType, 'application/vnd.openxmlformats-officedocument.wordprocessingml') ||
                                                                         str_starts_with($mimeType, 'application/msword')) {
                                                                    $fileType = 'word';
                                                                } elseif (str_starts_with($mimeType, 'application/vnd.openxmlformats-officedocument.spreadsheetml') ||
                                                                         str_starts_with($mimeType, 'application/vnd.ms-excel')) {
                                                                    $fileType = 'excel';
                                                                } elseif (str_starts_with($mimeType, 'application/vnd.openxmlformats-officedocument.presentationml') ||
                                                                         str_starts_with($mimeType, 'application/vnd.ms-powerpoint')) {
                                                                    $fileType = 'powerpoint';
                                                                } elseif (str_starts_with($mimeType, 'text/')) {
                                                                    $fileType = 'text';
                                                                }

                                                                $set('file_type', $fileType);
                                                                $set('type', 'document');
                                                                $set('user_id', auth()->id());

                                                                // Store the temporary file path in a hidden field for processing
                                                                $set('_temp_file_path', $filePath);

                                                                // Show success notification
                                                                \Filament\Notifications\Notification::make()
                                                                    ->title('File uploaded successfully')
                                                                    ->body("File type detected: " . strtoupper($fileType) . " ({$mimeType})")
                                                                    ->success()
                                                                    ->send();
                                                            }
                                                        }
                                                    } catch (\Exception $e) {
                                                        \Log::error('File type detection error: ' . $e->getMessage());

                                                        // Set default file type
                                                        $set('file_type', 'document');
                                                        $set('type', 'document');
                                                        $set('user_id', auth()->id());

                                                        if ($state) {
                                                            $filePath = is_array($state) ? $state[0] : $state;
                                                            $set('_temp_file_path', $filePath);
                                                        }
                                                    }
                                                }
                                            }),

                                        // File type indicator
                                        Forms\Components\Placeholder::make('file_type_indicator')
                                            ->label('File Type')
                                            ->content(function ($get) {
                                                $fileType = $get('file_type');
                                                if (!$fileType) return 'Not detected';

                                                $icons = [
                                                    'pdf' => '📄',
                                                    'image' => '🖼️',
                                                    'word' => '📝',
                                                    'excel' => '📊',
                                                    'powerpoint' => '📽️',
                                                    'text' => '📃',
                                                    'document' => '📁',
                                                ];

                                                $icon = $icons[$fileType] ?? '📁';
                                                return $icon . ' ' . strtoupper($fileType);
                                            })
                                            ->visible(fn($get) => !empty($get('file_type'))),

                                        // Database fields - these will be saved
                                        Forms\Components\Hidden::make('file_type')
                                            ->default('document'),

                                        Forms\Components\Hidden::make('type')
                                            ->default('document'),

                                        Forms\Components\Hidden::make('user_id')
                                            ->default(fn() => auth()->id()),

                                        // Temporary field for processing - included in form data but not saved to database
                                        Forms\Components\Hidden::make('_temp_file_path'),

                                        // Hidden field to track existing attachment ID for updates
                                        Forms\Components\Hidden::make('id'),

                                        // Hidden field to flag existing attachments
                                        Forms\Components\Hidden::make('_existing_attachment'),
                                    ])
                                    ->collapsible()
                                    ->collapsed(false)
                                    ->itemLabel(function (array $state): ?string {
                                        $title = $state['title'] ?? null;
                                        $fileType = $state['file_type'] ?? null;

                                        if ($title && $fileType) {
                                            $icons = [
                                                'pdf' => '📄',
                                                'image' => '🖼️',
                                                'word' => '📝',
                                                'excel' => '📊',
                                                'powerpoint' => '📽️',
                                                'text' => '📃',
                                                'document' => '📁',
                                            ];

                                            $icon = $icons[$fileType] ?? '📁';
                                            return $icon . ' ' . $title;
                                        }

                                        return $title ?? 'New Attachment';
                                    })
                                    ->defaultItems(0)
                                    ->addActionLabel(__('document.actions.add_attachment'))
                                    ->reorderableWithButtons()
                                    ->cloneable()
                                    ->deleteAction(
                                        fn(\Filament\Forms\Components\Actions\Action $action) => $action
                                            ->requiresConfirmation()
                                            ->modalHeading('Delete Attachment')
                                            ->modalDescription('Are you sure you want to delete this attachment? This action cannot be undone.')
                                    ),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('index')
                    ->rowIndex()
                    ->label('#'),

                Tables\Columns\SelectColumn::make('type')
                    ->disabled(true)
                    ->options(__('document.enum.type'))
                    ->sortable()
                    ->label(__('document.fields.type')),

                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(25)
                    ->label(__('document.fields.title')),

                Tables\Columns\TextColumn::make('gregorian_date')
                    ->date()
                    ->sortable()
                    ->label(__('document.fields.gregorian_date')),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->label(__('common.updated_at')),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label(__('common.created_at')),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'title',
                    'gregorian_date',
                    'updated_at',
                    'created_at',
                ])->filters([
                    Tables\Filters\SelectFilter::make('type')
                        ->options(__('document.enum.type'))
                        ->label(__('document.fields.type')),
                    Tables\Filters\TrashedFilter::make(),
                ])->defaultFilters([]),
            ])->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('common.view')),
                Tables\Actions\EditAction::make()
                    ->label(__('common.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(__('common.delete')),
                Tables\Actions\RestoreAction::make()
                    ->label(__('common.restore')),
                Tables\Actions\ForceDeleteAction::make()
                    ->label(__('common.force_delete')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\UsersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocuments::route('/'),
            'create' => Pages\CreateDocument::route('/create'),
            'edit' => Pages\EditDocument::route('/{record}/edit'),
            'view' => Pages\ViewDocument::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
