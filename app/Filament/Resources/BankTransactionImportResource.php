<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BankTransactionImportResource\Pages;
use App\Filament\Resources\BankTransactionImportResource\RelationManagers;
use App\Models\BankTransactionImport;
use App\Services\BankTransactionConfirmationService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class BankTransactionImportResource extends Resource
{
    protected static ?string $model = BankTransactionImport::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-arrow-up';

    protected static ?string $navigationLabel = 'استيراد المعاملات البنكية';

    protected static ?string $navigationGroup = 'الدعم';

    protected static ?string $modelLabel = 'استيراد معاملات';

    protected static ?string $pluralModelLabel = 'استيراد المعاملات البنكية';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('تفاصيل الاستيراد')
                    ->schema([
                        Forms\Components\Select::make('bank_account_id')
                            ->label('الحساب البنكي')
                            ->relationship('bank_account', 'title')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\FileUpload::make('bank_file')
                            ->label('ملف Excel')
                            ->acceptedFileTypes([
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                'application/vnd.ms-excel'
                            ])
                            ->required()
                            ->downloadable()
                            ->openable()
                            ->maxSize(10240) // 10MB
                            ->directory('temp-imports')
                            ->visibility('private')
                            ->helperText('يرجى رفع ملف Excel يحتوي على بيانات المعاملات البنكية'),

                        Forms\Components\TextInput::make('file_name')
                            ->label('اسم الملف')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\TextInput::make('items_count')
                            ->label('عدد العناصر')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\TextInput::make('items_sum_amount')
                            ->label('إجمالي المبلغ')
                            ->numeric()
                            ->suffix('ريال')
                            ->disabled()
                            ->dehydrated(false),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات إضافية')
                    ->schema([
                        Forms\Components\Placeholder::make('user_info')
                            ->label('المستخدم')
                            ->content(fn (?BankTransactionImport $record) =>
                                $record?->user?->name ?? auth()->user()->name
                            ),

                        Forms\Components\Placeholder::make('created_at')
                            ->label('تاريخ الإنشاء')
                            ->content(fn (?BankTransactionImport $record) =>
                                $record?->created_at?->format('Y-m-d H:i:s') ?? now()->format('Y-m-d H:i:s')
                            ),

                        Forms\Components\Placeholder::make('completed_at')
                            ->label('تاريخ الإكمال')
                            ->content(fn (?BankTransactionImport $record) =>
                                $record?->completed_at?->format('Y-m-d H:i:s') ?? 'لم يكتمل بعد'
                            ),
                    ])
                    ->columns(3)
                    ->visibleOn('view'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('file_name')
                    ->label('اسم الملف')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('bank_account.title')
                    ->label('الحساب البنكي')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('user.name')
                    ->label('المستخدم')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('items_count')
                    ->label('عدد العناصر')
                    ->numeric()
                    ->sortable(),

                TextColumn::make('items_sum_amount')
                    ->label('إجمالي المبلغ')
                    ->riyal()
                    ->sortable(),

                TextColumn::make('formatted_size')
                    ->label('حجم الملف')
                    ->badge(),

                TextColumn::make('completed_at')
                    ->label('تاريخ الإكمال')
                    ->dateTime()
                    ->placeholder('لم يكتمل')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('bank_account_id')
                    ->label('الحساب البنكي')
                    ->relationship('bank_account', 'title')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('user_id')
                    ->label('المستخدم')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('completed')
                    ->label('مكتمل')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('completed_at')),

                Tables\Filters\Filter::make('pending')
                    ->label('في الانتظار')
                    ->query(fn (Builder $query): Builder => $query->whereNull('completed_at')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),

                Tables\Actions\EditAction::make()
                    ->label('تعديل')
                    ->visible(fn (BankTransactionImport $record) => is_null($record->completed_at)),

                Action::make('confirm')
                    ->label('تأكيد الاستيراد')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (BankTransactionImport $record) => is_null($record->completed_at))
                    ->requiresConfirmation()
                    ->modalHeading('تأكيد استيراد المعاملات')
                    ->modalDescription('هل أنت متأكد من أنك تريد تأكيد استيراد هذه المعاملات؟ لا يمكن التراجع عن هذا الإجراء.')
                    ->action(function (BankTransactionImport $record) {
                        $confirmationService = app(BankTransactionConfirmationService::class);
                        $result = $confirmationService->confirmImport($record);

                        if ($result['success']) {
                            Notification::make()
                                ->title($result['message'])
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('خطأ في التأكيد')
                                ->body($result['error'])
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->visible(fn (BankTransactionImport $record) => is_null($record->completed_at)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankTransactionImports::route('/'),
            'create' => Pages\CreateBankTransactionImport::route('/create'),
            'view' => Pages\ViewBankTransactionImport::route('/{record}'),
            'edit' => Pages\EditBankTransactionImport::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('bank-transaction-import.create');
    }

    public static function canView(Model $record): bool
    {
        return auth()->user()->can('bank-transaction-import.index');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->can('bank-transaction-import.update') && is_null($record->completed_at);
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('bank-transaction-import.update') && is_null($record->completed_at);
    }
}
