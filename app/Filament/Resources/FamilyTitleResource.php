<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FamilyTitleResource\Pages;
use App\Filament\Resources\FamilyTitleResource\RelationManagers;
use App\Models\FamilyTitle;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FamilyTitleResource extends Resource
{
    protected static ?string $model = FamilyTitle::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'المشجرة';

    protected static ?string $navigationLabel = 'ألقاب العائلات';
    protected static ?string $modelLabel = 'لقب عائلة';
    protected static ?string $pluralModelLabel = 'ألقاب العائلات';
    protected static bool $shouldRegisterNavigation = false;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255)
                    ->label(__('familyTitle.title')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label(__('familyTitle.title'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('users_count')
                    ->label(__('familyTitle.users_count'))
                    ->counts('users')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.created_at'))
                    ->dateTime('Y-m-d h:i A')
                    ->sortable(),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'title',
                    'users_count',
                    'created_at',
                ])->filters([])->defaultFilters([]),
                Tables\Filters\TrashedFilter::make(),
            ], layout: FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->label(__('familyTitle.actions.view')),
                    Tables\Actions\EditAction::make()
                        ->label(__('familyTitle.actions.edit'))
                        ->hidden(fn($record) => $record->trashed()),
                    Tables\Actions\DeleteAction::make()
                        ->label(__('familyTitle.actions.delete'))
                        ->hidden(fn($record) => $record->trashed())
                        ->before(function ($record, $action) {
                            if ($record->users()->count() > 0) {
                                \Filament\Notifications\Notification::make()
                                    ->danger()
                                    ->title(__('familyTitle.notifications.cannot_delete_title'))
                                    ->body(__('familyTitle.notifications.has_users_message'))
                                    ->send();
                                
                                $action->cancel();
                            }
                        }),
                    Tables\Actions\RestoreAction::make()
                        ->label(__('familyTitle.actions.restore'))
                        ->hidden(fn($record) => !$record->trashed()),
                    Tables\Actions\ForceDeleteAction::make()
                        ->label(__('familyTitle.actions.force_delete'))
                        ->hidden(fn($record) => !$record->trashed())
                        ->before(function ($record, $action) {
                            if ($record->users()->count() > 0) {
                                \Filament\Notifications\Notification::make()
                                    ->danger()
                                    ->title(__('familyTitle.notifications.cannot_delete_title'))
                                    ->body(__('familyTitle.notifications.has_users_message'))
                                    ->send();
                                
                                $action->cancel();
                            }
                        }),
                ])
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function ($records, $action) {
                            $recordsWithUsers = $records->filter(fn($record) => $record->users()->count() > 0);
                            
                            if ($recordsWithUsers->count() > 0) {
                                \Filament\Notifications\Notification::make()
                                    ->danger()
                                    ->title(__('familyTitle.notifications.cannot_delete_titles'))
                                    ->body(__('familyTitle.notifications.some_titles_have_users'))
                                    ->send();
                                
                                $action->cancel();
                            }
                        }),
                    Tables\Actions\ForceDeleteBulkAction::make()
                        ->before(function ($records, $action) {
                            $recordsWithUsers = $records->filter(fn($record) => $record->users()->count() > 0);
                            
                            if ($recordsWithUsers->count() > 0) {
                                \Filament\Notifications\Notification::make()
                                    ->danger()
                                    ->title(__('familyTitle.notifications.cannot_delete_titles'))
                                    ->body(__('familyTitle.notifications.some_titles_have_users'))
                                    ->send();
                                
                                $action->cancel();
                            }
                        }),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ExternalUsersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFamilyTitles::route('/'),
            'create' => Pages\CreateFamilyTitle::route('/create'),
            'edit' => Pages\EditFamilyTitle::route('/{record}/edit'),
            'view' => Pages\ViewFamilyTitle::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
