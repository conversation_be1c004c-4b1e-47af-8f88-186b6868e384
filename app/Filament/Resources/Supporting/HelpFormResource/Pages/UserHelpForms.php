<?php

namespace App\Filament\Resources\Supporting\HelpFormResource\Pages;

use App\Filament\Resources\Supporting\HelpFormResource;
use App\Models\HelpForm;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Route;
use JetBrains\PhpStorm\NoReturn;
use Livewire\Attributes\On;

class UserHelpForms extends ListRecords
{
    protected static string $resource = HelpFormResource::class;

    public HelpForm $record;

    protected static ?string $navigationLabel = 'النماذج السابقة';

    protected static ?string $navigationIcon = 'heroicon-o-clipboard';

    public function table(Table $table): Table
    {
        $userId = $this->record->user_id;
        $table->modifyQueryUsing(
            fn(Builder $query) => $query
                ->where('uuid', '!=', $this->record->uuid)
                ->where('user_id', $userId)
        );
        return static::getResource()::table($table);
    }

    public function getSubNavigationParameters(): array
    {
        return [
            ...parent::getSubNavigationParameters(),
            'record' => $this->record,
        ];
    }

    public function getSubNavigation(): array
    {
        return static::getResource()::getRecordSubNavigation($this);
    }
}
