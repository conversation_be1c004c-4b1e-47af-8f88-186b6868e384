<?php

namespace App\Filament\Resources\Supporting\HelpFormResource\Pages;

use App\Filament\Resources\Supporting\HelpFormResource;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Joaopaulolndev\FilamentPdfViewer\Infolists\Components\PdfViewerEntry;

class HelpFormPdf extends ViewRecord
{
    protected static string $resource = HelpFormResource::class;

    protected static ?string $navigationLabel = 'PDF';

    protected static ?string $navigationIcon = 'heroicon-o-document';

    public function infolist(Infolist $infolist): Infolist
    {
        $pdfUrl = route('admin.help-forms.pdf', [
            'help_form' => $this->record,
            'form_id' => $this->record->form_id,
        ]);
        return $infolist
            ->schema([
                PdfViewerEntry::make('file')
                    ->hiddenLabel()
                    ->fileUrl($pdfUrl)
                    ->minHeight('80svh')
                    ->columnSpanFull()
            ]);
    }
}
