<?php

namespace App\Filament\Resources\Supporting\HelpFormResource\Pages\Traits;

use Exception;
use Illuminate\Http\UploadedFile;
use Intervention\Image\Laravel\Facades\Image;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Log;
use Spatie\PdfToImage\Pdf;

trait FileUploadHandlers
{
    /**
     * Handle file upload process for all types of files
     *
     * @param UploadedFile|TemporaryUploadedFile $file
     * @return array
     */
    protected function handleFileUpload($file)
    {
        $isPdf = $file->getMimeType() === 'application/pdf';
        $isSvg = in_array($file->getMimeType(), ['image/svg+xml', 'image/svg']);
        $directory = $isPdf ? 'pdf' : 'images';

        // Store the original file
        $path = $file->store("public/help-forms/$directory");
        $fileName = basename($path);
        $baseName = pathinfo($fileName, PATHINFO_FILENAME);
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);

        $data = [
            'type' => $isPdf ? 'PDF' : 'IMAGE',
            'path' => "help-forms/$directory/$fileName",
        ];

        // Generate thumbnail based on file type
        if ($isPdf) {
            $data = array_merge($data, $this->handlePdfThumbnail($path, $baseName, $directory));
        } else {
            $data = array_merge($data, $this->handleImageThumbnail($path, $fileName, $baseName, $extension, $directory, $isSvg));
        }

        return $data;
    }

    /**
     * Generate thumbnail for PDF files
     *
     * @param string $path
     * @param string $baseName
     * @param string $directory
     * @return array
     */
    protected function handlePdfThumbnail($path, $baseName, $directory)
    {
        $filePath = storage_path("app/$path");
        $thumbPath = storage_path("app/public/help-forms/$directory/thumbnails/$baseName");

        // Ensure thumbnail directory exists
        $thumbDir = dirname($thumbPath);
        if (!file_exists($thumbDir)) {
            mkdir($thumbDir, 0755, true);
        }

        $data = [];

        try {
            $pdf = new Pdf($filePath);
            if ($pdf->setOutputFormat('jpg')->saveImage("$thumbPath.jpg")) {
                Image::read("$thumbPath.jpg")
                    ->resize(100, 150)
                    ->save("$thumbPath.jpg");
                $data['thumb'] = "help-forms/$directory/thumbnails/$baseName.jpg";
            }
        } catch (Exception $e) {
            Log::error("PDF thumbnail generation failed: {$e->getMessage()}");
        }

        return $data;
    }

    /**
     * Generate thumbnail for image files
     *
     * @param string $path
     * @param string $fileName
     * @param string $baseName
     * @param string $extension
     * @param string $directory
     * @param bool $isSvg
     * @return array
     */
    protected function handleImageThumbnail($path, $fileName, $baseName, $extension, $directory, $isSvg)
    {
        $filePath = storage_path("app/$path");
        $thumbPath = storage_path("app/public/help-forms/$directory/thumbnails/$baseName");

        $data = [];

        if ($isSvg) {
            try {
                if (!file_exists($thumbPath)) {
                    symlink($filePath, $thumbPath);
                }
                $data['thumb'] = "help-forms/$directory/thumbnails/$fileName";
            } catch (Exception $e) {
                Log::error("SVG thumbnail symlink failed: {$e->getMessage()}");
                $data['thumb'] = "help-forms/$directory/$fileName";
            }
        } else {
            try {
                $image = Image::read($filePath);
                // Get original dimensions
                $originalWidth = $image->width();
                $originalHeight = $image->height();

                // Calculate new dimensions maintaining aspect ratio
                $maxWidth = 250;
                $maxHeight = 125;

                $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
                $newWidth = round($originalWidth * $ratio);
                $newHeight = round($originalHeight * $ratio);

                // Create directory if it doesn't exist
                $thumbDir = dirname("$thumbPath.$extension");
                if (!file_exists($thumbDir)) {
                    mkdir($thumbDir, 0755, true);
                }

                $image->resize($newWidth, $newHeight)
                    ->save("$thumbPath.$extension");
                $data['thumb'] = "help-forms/$directory/thumbnails/$fileName";
            } catch (Exception $e) {
                Log::error("Image thumbnail generation failed: {$e->getMessage()}");
                $data['thumb'] = "help-forms/$directory/$fileName";
            }
        }

        return $data;
    }
}
