<?php

namespace App\Filament\Resources\Supporting\HelpFormResource\Pages;

use Exception;
use App\Enums\Gender;
use App\Models\HelpForm;
use Filament\Actions\Action;
use Filament\Infolists\Infolist;
use Filament\Actions\ActionGroup;
use Filament\Infolists\Components;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\FontWeight;
use Filament\Infolists\Components\Tabs;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Resources\UserResource;
use Filament\Infolists\Components\Split;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\TextEntry;
use Filament\Forms\Components as FormsComponents;
use Filament\Infolists\Components\Grid as InfoGrid;
use Filament\Infolists\Components\RepeatableEntry;
use Hugomyb\FilamentMediaAction\Actions\MediaAction;
use App\Filament\Resources\Supporting\HelpFormResource;
use Filament\Infolists\Components\Section as InfoSection;

/**
 * @property HelpForm $record
 */
class ViewHelpForm extends ViewRecord
{
    protected static string $resource = HelpFormResource::class;

    protected static ?string $navigationLabel = 'تفاصيل النموذج';

    /**
     * Check if the user is authorized to view this help form
     */
    protected function authorizeAccess(): void
    {
        parent::authorizeAccess();

        /*if (!Auth::user()->can('view', $this->getRecord())) {
            Notification::make()
                ->danger()
                ->title('غير مسموح')
                ->body('ليس لديك صلاحية لعرض هذا النموذج')
                ->send();

            //$this->redirectRoute('filament.admin.resources.supporting.help-forms.index');
        }*/
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Grid::make(8)
                    ->schema([
                        Components\Grid::make()
                            ->columnSpan(6)
                            ->schema([
                                // Header Stats Section
                                InfoSection::make()
                                    ->schema([
                                        Split::make([
                                            InfoGrid::make()
                                                ->columns($this->record->status === 'APPROVED' ? 4 : 3)
                                                ->schema([
                                                    TextEntry::make('status')
                                                        ->label(__('helpFormColumns.status'))
                                                        ->badge()
                                                        ->state(fn($record) => $record->status ?? 'PENDING')
                                                        ->formatStateUsing(fn(string $state): string => match ($state) {
                                                            'APPROVED' => 'مقبول',
                                                            'DECLINED' => 'مرفوض',
                                                            default => 'تحت الدراسة',
                                                        })
                                                        ->color(fn(string $state): string => match ($state) {
                                                            'APPROVED' => 'success',
                                                            'DECLINED' => 'danger',
                                                            default => 'info',
                                                        })
                                                        ->icon(fn(string $state): string => match ($state) {
                                                            'APPROVED' => 'heroicon-o-check-circle',
                                                            'DECLINED' => 'heroicon-o-x-circle',
                                                            default => 'heroicon-o-clock',
                                                        })
                                                        ->size(TextEntry\TextEntrySize::Large),

                                                    TextEntry::make('form_id')
                                                        ->label(__('helpFormColumns.form_id'))
                                                        ->icon('heroicon-o-document-text')
                                                        ->copyable()
                                                        ->weight(FontWeight::Bold),

                                                    TextEntry::make('template.title')
                                                        ->label(__('helpFormColumns.template'))
                                                        ->icon('heroicon-o-document')
                                                        ->weight(FontWeight::Medium),

                                                    TextEntry::make('helping_value')
                                                        ->label(__('helpFormColumns.helping_value'))
                                                        ->riyal()
                                                        ->sensitive()
                                                        ->icon('heroicon-o-currency-dollar')
                                                        ->color('success')
                                                        ->visible(fn($record) => $record->status === 'APPROVED'),


                                                    TextEntry::make('calculated_annual_helping_value')
                                                        ->label(__('helpFormColumns.calculated_annual_helping_value'))
                                                        ->riyal()
                                                        ->sensitive()
                                                        ->tooltip(fn($record) => $record->calculated_annual_helping_caption)
                                                        ->hiddenEmpty()
                                                        ->icon('heroicon-o-currency-dollar')
                                                        ->color('danger'),

                                                    TextEntry::make('calculated_income_value')
                                                        ->label(__('helpFormColumns.calculated_income_value'))
                                                        ->riyal()
                                                        ->sensitive()
                                                        ->hiddenEmpty()
                                                        ->icon('heroicon-o-currency-dollar')
                                                        ->color('danger'),

                                                    TextEntry::make('calculated_restricted_value')
                                                        ->label(__('helpFormColumns.calculated_restricted_value'))
                                                        ->riyal()
                                                        ->sensitive()
                                                        ->hiddenEmpty()
                                                        ->icon('heroicon-o-currency-dollar')
                                                        ->color('danger'),

                                                    TextEntry::make('suggested_deserved_status')
                                                        ->label(__('helpFormColumns.suggested_deserved_status'))
                                                        ->badge()
                                                        ->state(fn($record) => $record->calculated_restricted_value > $record->calculated_income_value ? 'DESERVED' : 'UNDESERVED')
                                                        ->formatStateUsing(fn(string $state): string => match ($state) {
                                                            'DESERVED' => __('helpFormColumns.deserved_helping'),
                                                            'UNDESERVED' => __('helpFormColumns.undeserved_helping'),
                                                            default => 'غير معروف',
                                                        })
                                                        ->sensitive()
                                                        ->color(fn(string $state): string => match ($state) {
                                                            'DESERVED' => 'success',
                                                            'UNDESERVED' => 'danger',
                                                            default => 'info',
                                                        }),
                                                ]),
                                        ])->from('md'),
                                    ])
                                    ->compact(),

                                // Main Content Tabs
                                Tabs::make('Main Content')
                                    ->tabs([
                                        Tabs\Tab::make('Personal Details')
                                            ->label('بيانات المستفيد')
                                            ->icon('heroicon-o-user')
                                            ->schema([
                                                Split::make([
                                                    InfoGrid::make(2)
                                                        ->schema([
                                                            TextEntry::make('user.full_name')
                                                                ->hiddenEmpty()
                                                                ->url(
                                                                    fn($record) => $record->user
                                                                        ? UserResource::getUrl('view', ['record' => $record->user->id])
                                                                        : null
                                                                )
                                                                ->sensitive()
                                                                ->label(__('helpFormColumns.user'))
                                                                ->icon('heroicon-o-user')
                                                                ->weight(FontWeight::Bold),

                                                            TextEntry::make('user.national_id')
                                                                ->hiddenEmpty()
                                                                ->label(__('userColumns.national_id'))
                                                                ->icon('heroicon-o-identification')
                                                                ->copyable()
                                                                ->sensitive(),

                                                            TextEntry::make('user.phone')
                                                                ->hiddenEmpty()
                                                                ->label(__('userColumns.phone'))
                                                                ->icon('heroicon-o-phone')
                                                                ->copyable()
                                                                ->sensitive(),

                                                            TextEntry::make('age')
                                                                ->hiddenEmpty()
                                                                ->label(__('userColumns.age'))
                                                                ->sensitive()
                                                                ->icon('heroicon-o-calendar'),

                                                            TextEntry::make('gender')
                                                                ->hiddenEmpty()
                                                                ->label(__('userColumns.gender'))
                                                                ->formatStateUsing(fn($state) => __("userColumns.gender.{$state}"))
                                                                ->sensitive()
                                                                ->icon(fn(string $state): string => $state === 'MALE' ? 'heroicon-o-user' : 'heroicon-o-user-circle'),

                                                            TextEntry::make('marital_status')
                                                                ->label(__('userColumns.marital_status'))
                                                                ->formatStateUsing(fn($state, $record) => __("userColumns.marital_status.{$record->user->gender}.{$state}"))
                                                                ->sensitive()
                                                                ->hiddenEmpty()
                                                                ->icon('heroicon-o-heart'),

                                                            TextEntry::make('address')
                                                                ->label(__('helpFormColumns.address'))
                                                                ->hiddenEmpty()
                                                                ->columnSpanFull()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-map-pin'),
                                                            TextEntry::make('notes')
                                                                ->label(__('helpFormColumns.notes'))
                                                                ->sensitive()
                                                                ->columnSpanFull()
                                                                ->icon('heroicon-o-pencil-square'),
                                                        ]),

                                                    /*InfoGrid::make(1)
                                                        ->schema([
                                                        ]),*/
                                                ])->from('lg'),
                                            ]),

                                        $this->familySection(),

                                        Tabs\Tab::make('Financial Details')
                                            ->visible(fn($record) => (
                                                !is_null($record->salary) ||
                                                !is_null($record->social_support) ||
                                                !is_null($record->citizen_account) ||
                                                !is_null($record->spouses_salary) ||
                                                !is_null($record->debts) ||
                                                !is_null($record->income)
                                            ))
                                            ->label('البيانات المالية')
                                            ->icon('heroicon-o-banknotes')
                                            ->schema([
                                                Split::make([
                                                    InfoGrid::make(2)
                                                        ->schema([
                                                            TextEntry::make('salary')
                                                                ->hiddenEmpty()
                                                                ->label(__('helpFormColumns.salary'))
                                                                ->riyal()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-banknotes')
                                                                ->color('success'),

                                                            TextEntry::make('social_support')
                                                                ->hiddenEmpty()
                                                                ->label(__('helpFormColumns.social_support'))
                                                                ->riyal()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-gift')
                                                                ->color('info'),

                                                            TextEntry::make('citizen_account')
                                                                ->hiddenEmpty()
                                                                ->label(__('helpFormColumns.citizen_account'))
                                                                ->riyal()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-building-library')
                                                                ->color('info'),

                                                            TextEntry::make('spouses_salary')
                                                                ->hiddenEmpty()
                                                                ->label(__('helpFormColumns.spouses_salary'))
                                                                ->riyal()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-currency-dollar')
                                                                ->color('success'),

                                                            TextEntry::make('debts')
                                                                ->hiddenEmpty()
                                                                ->label(__('helpFormColumns.debts'))
                                                                ->riyal()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-exclamation-circle')
                                                                ->color('danger'),

                                                            TextEntry::make('income')
                                                                ->hiddenEmpty()
                                                                ->label(__('helpFormColumns.income'))
                                                                ->riyal()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-calculator')
                                                                ->color('success')
                                                                ->size(TextEntry\TextEntrySize::Large)
                                                                ->weight(FontWeight::Bold),
                                                        ]),
                                                ])->from('lg'),
                                            ]),

                                        Tabs\Tab::make('Residence Info')
                                            ->label('بيانات السكن')
                                            ->icon('heroicon-o-home')
                                            ->schema([
                                                Split::make([
                                                    InfoGrid::make(2)
                                                        ->schema([
                                                            TextEntry::make('residence')
                                                                ->label(__('helpFormColumns.residence'))
                                                                ->hiddenEmpty()
                                                                ->badge()
                                                                ->formatStateUsing(fn(string $state) => __('helpFormColumns.residence.' . $state))
                                                                ->sensitive()
                                                                ->icon('heroicon-o-home')
                                                                ->color(fn(string $state): string => match ($state) {
                                                                    'OWNER' => 'success',
                                                                    'RENT' => 'info',
                                                                    default => 'warning',
                                                                }),

                                                            TextEntry::make('residence_type')
                                                                ->label(__('helpFormColumns.residence_type'))
                                                                ->hiddenEmpty()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-building-office'),

                                                            TextEntry::make('residence_size')
                                                                ->label(__('helpFormColumns.residence_size'))
                                                                ->sensitive()
                                                                ->hiddenEmpty()
                                                                ->icon('heroicon-o-rectangle-stack'),

                                                            TextEntry::make('residence_rent_cost')
                                                                ->label(__('helpFormColumns.residence_rent_cost'))
                                                                ->visible(fn($record) => $record->callbackIsRentResidence())
                                                                ->hiddenEmpty()
                                                                ->riyal()
                                                                ->sensitive()
                                                                ->icon('heroicon-o-currency-dollar'),
                                                        ]),
                                                ])->from('lg'),
                                            ]),

                                        $this->invoicesSection(),

                                        $this->attachmentSection(),
                                    ])
                                    ->columnSpanFull(),

                                // Comments / Reasons Content Tabs
                                Tabs::make('comments')
                                    ->tabs([
                                        Tabs\Tab::make('التعليقات')
                                            ->icon('heroicon-o-clipboard-document-check')
                                            ->schema([
                                                HelpFormResource\Components\HelpFormComments::make('reasons'),
                                            ]),
                                    ])
                                    ->columnSpanFull(),
                            ]),

                        Components\Grid::make()
                            ->columnSpan(2)
                            ->schema([
                                $this->sideSection1(),
                                $this->sideSection2(),
                            ]),
                    ]),

                // Approval History Section
                InfoSection::make('Approval History')
                    ->collapsed()
                    ->icon('heroicon-o-clipboard-document-check')
                    ->visible(false)
                    ->schema([
                        RepeatableEntry::make('reasons')
                            ->schema([
                                Split::make([
                                    InfoGrid::make(2)
                                        ->schema([
                                            TextEntry::make('user.identifier')
                                                ->icon('heroicon-o-user'),
                                            TextEntry::make('created_at')
                                                ->dateTime()
                                                ->icon('heroicon-o-clock'),
                                            TextEntry::make('status')
                                                ->badge()
                                                ->color(fn(string $state): string => match ($state) {
                                                    'APPROVED' => 'success',
                                                    'DECLINED' => 'danger',
                                                    default => 'warning',
                                                }),
                                            TextEntry::make('content')
                                                ->columnSpanFull()
                                                ->markdown(),
                                        ]),
                                ])->from('md'),
                            ]),
                    ]),
            ]);
    }

    protected function familySection(): Tabs\Tab
    {
        return Tabs\Tab::make('Family Members')
            ->label('أفراد الأسرة')
            ->icon('heroicon-o-users')
            ->visible(fn($record) => $record->active_family_members->count() > 0)
            ->badge(fn($record) => $record->active_family_members->count())
            ->schema([
                RepeatableEntry::make('active_family_members')
                    ->label('أفراد العائلة المشمولين في الدعم')
                    ->schema([
                        Split::make([
                            InfoGrid::make(3)
                                ->schema([
                                    TextEntry::make('member.full_name')
                                        ->label(__('userColumns.name'))
                                        ->url(
                                            fn($record) => $record->member
                                                ? UserResource::getUrl('view', ['record' => $record->member->id])
                                                : null
                                        )
                                        ->icon('heroicon-o-user')
                                        ->columnSpan(2)
                                        ->sensitive()
                                        ->weight(FontWeight::Medium),

                                    TextEntry::make('relation')
                                        ->label('العلاقة')
                                        ->badge()
                                        ->formatStateUsing(fn(string $state, $record): string => $state === 'CHILD' ? match ($record->member->gender) {
                                            Gender::Male => 'ابن',
                                            Gender::Female => 'ابنة',
                                            default => 'ابن / ابنة',
                                        } : __($state))
                                        ->sensitive()
                                        ->color(fn(string $state): string => match ($state) {
                                            'WIFE', 'HUSBAND' => 'success',
                                            'CHILD' => 'info',
                                            default => 'warning',
                                        }),

                                    TextEntry::make('age')
                                        ->label(__('userColumns.age'))
                                        ->formatStateUsing(fn($state) => $state ?? 'غير معروف')
                                        ->sensitive()
                                        ->icon('heroicon-o-calendar'),

                                    TextEntry::make('health_status')
                                        ->label(__('userColumns.health_status'))
                                        ->sensitive()
                                        ->icon('heroicon-o-heart'),

                                    TextEntry::make('educational_status')
                                        ->label(__('userColumns.educational_status'))
                                        ->icon('heroicon-o-academic-cap')
                                        ->sensitive(),
                                ]),
                        ])->from('md'),
                    ])
                    ->columns(1),
            ]);
    }

    protected function invoicesSection(): Tabs\Tab
    {
        return Tabs\Tab::make('Invoices')
            ->label('الفواتير')
            ->icon('heroicon-o-receipt-percent')
            ->visible(fn($record) => $record->invoices && $record->invoices->count() > 0)
            ->badge(fn($record) => $record->invoices ? $record->invoices->count() : 0)
            ->schema([
                RepeatableEntry::make('invoices')
                    ->label('الفواتير')
                    ->schema([
                        Split::make([
                            InfoGrid::make(5)
                                ->schema([
                                    TextEntry::make('type')
                                        ->label(__('helpFormInvoiceColumns.type'))
                                        ->icon('heroicon-o-document-text')
                                        ->sensitive()
                                        ->weight(FontWeight::Medium),

                                    TextEntry::make('no')
                                        ->label(__('helpFormInvoiceColumns.no'))
                                        ->icon('heroicon-o-identification')
                                        ->copyable()
                                        ->sensitive(),

                                    TextEntry::make('due_at')
                                        ->label(__('helpFormInvoiceColumns.due_at'))
                                        ->date()
                                        ->sensitive()
                                        ->icon('heroicon-o-calendar'),

                                    TextEntry::make('value')
                                        ->label(__('helpFormInvoiceColumns.value'))
                                        ->riyal()
                                        ->sensitive()
                                        ->icon('heroicon-o-currency-dollar'),

                                    Components\Group::make([
                                        Components\ImageEntry::make('thumb_path')
                                            ->hiddenLabel()
                                            ->height('100')
                                            ->extraAttributes([
                                                'class' => 'border border-gray-200 rounded-lg shadow-sm',
                                            ])
                                            ->statePath('')
                                            ->getStateUsing(function (Components\ImageEntry $component) {
                                                $containerState = $component->getContainer()->getState();
                                                $state = $containerState instanceof Model ?
                                                    $component->getStateFromRecord($containerState) :
                                                    data_get($containerState, $component->getStatePath());
                                                return !is_null($state) && isset($state['thumb_path']) ? tenant_asset($state['thumb_path']) : null;
                                            })
                                            ->action(
                                                \Hugomyb\FilamentMediaAction\Infolists\Components\Actions\MediaAction::make('عرض')
                                                    ->hiddenLabel()
                                                    ->icon('heroicon-o-eye')
                                                    ->color('gray')
                                                    ->media(function (Components\ImageEntry $component) {
                                                        $containerState = $component->getContainer()->getState();
                                                        $state = $containerState instanceof Model ?
                                                            $component->getStateFromRecord($containerState) :
                                                            data_get($containerState, $component->getStatePath());
                                                        return tenant_asset($state->file_path);
                                                    })
                                                    ->preload(false)
                                            ),
                                    ]),
                                ]),
                        ])->from('lg'),
                    ])
                    ->columns(1),
            ]);
    }

    protected function attachmentSection(): Tabs\Tab
    {
        $this->record->items->load(['template_item', 'template_section']);

        // Get submitted form items and prepare them for display
        $attachments = $this->record->items
            ->whereNotNull('submitted_at')
            //->reject(fn($items) => !$items->template_section->is_active)
            ->groupBy(function ($item) {
                // Use the template section's ID as the grouping key
                return $item->template_item?->help_form_template_section_id ?? 'unknown';
            })
            ->map(function (Collection $items, $sectionId) {
                // Skip unknown sections
                if ($sectionId === 'unknown') {
                    return null;
                }

                // Get the section from the first item
                $section = $items->first()?->template_item?->section;
                if (!$section) {
                    return null;
                }

                // Sort items by type priority
                $sortedItems = $items
                    ->sort(fn($item) => [
                        'FILE' => 0,
                        'MULTIPLE_FILE' => 1,
                        'TEXT' => 2,
                        'LONGTEXT' => 3,
                        'NUMBER' => 4,
                        'BOOLEAN' => 5,
                        'DATE' => 6,
                        'SELECT' => 7,
                        'RADIO' => 8,
                        'IMAGE' => 9,
                        'MULTIPLE_IMAGE' => 10,
                        'PDF' => 11,
                        'MULTIPLE_PDF' => 12,
                        'BLADE' => 13,
                    ][$item->type] ?? 999)
                    ->sortBy(fn($item) => $item->template_item?->id ?? 0);
                // Create a section with the sorted items
                return InfoSection::make($section->title)
                    ->schema([
                        Split::make([
                            InfoGrid::make(1)
                                ->schema([
                                    TextEntry::make('section_title')
                                        ->state($section->title)
                                        ->label($section->title)
                                        ->sensitive()
                                        ->icon('heroicon-o-folder')
                                        ->weight(FontWeight::Bold)
                                        ->size(TextEntry\TextEntrySize::Large),
                                ]),
                            InfoGrid::make(4)
                                ->state($this->record)
                                ->statePath('items')
                                ->hidden(!isShowSensitiveData())
                                ->schema(
                                    $this->generateAttachmentComponents($sortedItems, isShowSensitiveData())
                                )
                                ->columns(2),
                        ])->from('lg'),
                    ])
                    ->collapsible(false)
                    ->persistCollapsed();
            })
            ->filter() // Remove null values
            ->values()
            ->all();

        return Tabs\Tab::make('Attachments')
            ->label('البيانات الإضافية')
            ->icon('heroicon-o-paper-clip')
            ->visible(fn($record) => count($attachments) > 0)
            ->badge(fn($record) => count($attachments))
            ->schema($attachments);
    }

    /**
     * Generate attachment components for the infolist
     *
     * @param Collection $items
     * @param bool $showSensitiveData
     * @return array
     */
    protected function generateAttachmentComponents(Collection $items, bool $showSensitiveData): array
    {
        return $items->map(function ($item, $k) use ($showSensitiveData) {
            // Handle different item types
            switch ($item->type) {
                case 'FILE':
                    return $this->createSingleFileComponent($item, $k);

                case 'MULTIPLE_FILE':
                    return $this->createMultipleFileComponent($item, $k);

                case 'TEXT':
                case 'LONGTEXT':
                    return $this->createTextComponent($item, $k);

                case 'NUMBER':
                    return $this->createNumberComponent($item, $k, $showSensitiveData);

                case 'BOOLEAN':
                    return $this->createBooleanComponent($item, $k);

                case 'BOOLEAN_TEXT':
                    return $this->createBooleanTextComponent($item, $k);

                case 'BOOLEAN_NUMBER':
                    return $this->createBooleanNumberComponent($item, $k, $showSensitiveData);

                case 'DATE':
                    return $this->createDateComponent($item, $k);

                case 'SELECT':
                case 'RADIO':
                    return $this->createSelectComponent($item, $k);

                default:
                    return null;
            }
        })->filter()->toArray();
    }

    /**
     * Create a component for displaying a single file
     *
     * @param $item
     * @param $index
     * @return Components\Group
     */
    protected function createSingleFileComponent($item, $index)
    {
        // Handle case where data is not properly formatted
        if (!is_array($item->data) || empty($item->data)) {
            return null;
        }

        $fileUrl = isset($item->data['path']) ? tenant_asset($item->data['path']) : null;
        $thumbUrl = isset($item->data['thumb']) ? tenant_asset($item->data['thumb']) : $fileUrl;

        if (empty($fileUrl)) {
            return null;
        }

        return Components\Group::make([
            Components\TextEntry::make('template_item_title_' . $index)
                ->state($item->template_item?->title ?? 'مرفق')
                ->hiddenLabel()
                ->icon('heroicon-o-document')
                ->weight(FontWeight::Medium),

            Components\ImageEntry::make('file_thumb_' . $index)
                ->state($thumbUrl)
                ->hiddenLabel()
                ->height('100px')
                ->extraAttributes([
                    'class' => 'border border-gray-200 rounded-lg shadow-sm',
                ])
                ->checkFileExistence(false)
                ->action(
                    \Hugomyb\FilamentMediaAction\Infolists\Components\Actions\MediaAction::make('إستعراض')
                        ->hiddenLabel()
                        ->icon('heroicon-o-eye')
                        ->color('gray')
                        ->media($fileUrl)
                        ->preload(false)
                ),
        ])
            ->columnSpanFull();
    }

    /**
     * Create a component for displaying multiple files
     *
     * @param $item
     * @param $index
     * @return RepeatableEntry
     */
    protected function createMultipleFileComponent($item, $index)
    {
        // Handle case where data is not properly formatted
        if (!is_array($item->data) || empty($item->data)) {
            return null;
        }

        return RepeatableEntry::make("items.{$index}.data")
            ->state($item->data)
            ->label($item->template_item?->title ?? 'مرفقات متعددة')
            ->schema([
                Components\Group::make([
                    TextEntry::make('title')
                        ->hiddenLabel()
                        ->icon('heroicon-o-document')
                        ->weight(FontWeight::Medium),

                    Components\ImageEntry::make('image')
                        ->hiddenLabel()
                        ->height('100')
                        ->extraAttributes([
                            'class' => 'border border-gray-200 rounded-lg shadow-sm',
                        ])
                        ->statePath('')
                        ->getStateUsing(function (Components\ImageEntry $component) {
                            $containerState = $component->getContainer()->getState();
                            $state = $containerState instanceof Model ?
                                $component->getStateFromRecord($containerState) :
                                data_get($containerState, $component->getStatePath());
                            return !is_null($state) && isset($state['thumb']) ? tenant_asset($state['thumb']) : null;
                        })
                        ->action(
                            \Hugomyb\FilamentMediaAction\Infolists\Components\Actions\MediaAction::make('إستعراض')
                                ->hiddenLabel()
                                ->icon('heroicon-o-eye')
                                ->color('gray')
                                ->media(function (Components\ImageEntry $component) {
                                    $containerState = $component->getContainer()->getState();
                                    $state = $containerState instanceof Model ?
                                        $component->getStateFromRecord($containerState) :
                                        data_get($containerState, $component->getStatePath());
                                    return tenant_asset($state['path']);
                                })
                                ->preload(false)
                        ),
                ])
                    ->columnSpanFull(),
            ])
            ->statePath("$index.data")
            ->contained(false)
            ->columnSpanFull();
    }

    /**
     * Create a component for displaying text content
     *
     * @param $item
     * @param $index
     * @return TextEntry
     */
    protected function createTextComponent($item, $index)
    {
        return TextEntry::make('text_value_' . $index)
            ->state($item->data['value'] ?? null)
            ->label($item->template_item?->title ?? 'نص')
            ->icon('heroicon-o-document-text')
            ->columnSpanFull();
    }

    /**
     * Create a component for displaying boolean text content
     *
     * @param $item
     * @param $index
     * @return Components\Group
     */
    protected function createBooleanTextComponent($item, $index)
    {
        $isChecked = isset($item->data['is_checked']) ? (bool)$item->data['is_checked'] : false;
        $value = $isChecked ? ($item->data['value'] ?? null) : null;

        return Components\Group::make([
            TextEntry::make('boolean_text_value_' . $index)
                ->state($isChecked ? 'نعم' : 'لا')
                ->label($item->template_item?->title ?? 'خيار نصي')
                ->icon($isChecked ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle')
                ->color($isChecked ? 'success' : 'danger'),

            TextEntry::make('boolean_text_extra_' . $index)
                ->state($value)
                ->label($item->template_item?->placeholder ?? 'التفاصيل')
                ->visible($isChecked && !empty($value))
                ->icon('heroicon-o-document-text'),
        ])
            ->columnSpanFull();
    }

    /**
     * Create a component for displaying boolean number content
     *
     * @param $item
     * @param $index
     * @param bool $showSensitiveData
     * @return Components\Group
     */
    protected function createBooleanNumberComponent($item, $index, bool $showSensitiveData)
    {
        $isChecked = isset($item->data['is_checked']) ? (bool)$item->data['is_checked'] : false;
        $value = $isChecked ? ($item->data['value'] ?? null) : null;

        return Components\Group::make([
            TextEntry::make('boolean_number_value_' . $index)
                ->state($isChecked ? 'نعم' : 'لا')
                ->label($item->template_item?->title ?? 'خيار رقمي')
                ->icon($isChecked ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle')
                ->color($isChecked ? 'success' : 'danger'),

            TextEntry::make('boolean_number_extra_' . $index)
                ->state($value)
                ->label($item->template_item?->placeholder ?? 'القيمة')
                ->visible($isChecked && !empty($value))
                ->riyal()
                ->sensitive()
                ->icon('heroicon-o-calculator'),
        ])
            ->columnSpanFull();
    }

    /**
     * Create a component for displaying numeric content
     *
     * @param $item
     * @param $index
     * @param bool $showSensitiveData
     * @return TextEntry
     */
    protected function createNumberComponent($item, $index, bool $showSensitiveData)
    {
        return TextEntry::make('number_value_' . $index)
            ->state($item->data['value'] ?? null)
            ->label($item->template_item?->title ?? 'رقم')
            ->riyal()
            ->sensitive()
            ->icon('heroicon-o-calculator')
            ->columnSpanFull();
    }

    /**
     * Create a component for displaying boolean content
     *
     * @param $item
     * @param $index
     * @return TextEntry
     */
    protected function createBooleanComponent($item, $index)
    {
        $value = isset($item->data['value']) ? (bool)$item->data['value'] : false;

        return TextEntry::make('boolean_value_' . $index)
            ->state($value ? 'نعم' : 'لا')
            ->label($item->template_item?->title ?? 'خيار')
            ->icon($value ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle')
            ->color($value ? 'success' : 'danger')
            ->columnSpanFull();
    }

    /**
     * Create a component for displaying date content
     *
     * @param $item
     * @param $index
     * @return TextEntry
     */
    protected function createDateComponent($item, $index)
    {
        return TextEntry::make('date_value_' . $index)
            ->state($item->data['value'] ?? null)
            ->date()
            ->label($item->template_item?->title ?? 'تاريخ')
            ->icon('heroicon-o-calendar')
            ->columnSpanFull();
    }

    /**
     * Create a component for displaying select content
     *
     * @param $item
     * @param $index
     * @return TextEntry
     */
    protected function createSelectComponent($item, $index)
    {
        // Try to get the label for the selected value
        $selectedValue = $item->data['value'] ?? null;
        $options = $item->template_item->data['options'] ?? [];
        $selectedLabel = null;

        if ($selectedValue && is_array($options)) {
            $selectedOption = collect($options)->firstWhere('value', $selectedValue);
            $selectedLabel = $selectedOption['label'] ?? $selectedValue;
        }

        return TextEntry::make('select_value_' . $index)
            ->state($selectedLabel ?? $selectedValue)
            ->label($item->template_item?->title ?? 'اختيار')
            ->icon('heroicon-o-list-bullet')
            ->badge()
            ->columnSpanFull();
    }

    protected function sideSection1()
    {
        return Components\Section::make()
            ->schema([
                Components\Grid::make(1)
                    ->schema([
                        Components\Group::make([
                            Components\TextEntry::make('created_at')
                                ->translateLabel()
                                ->dateTooltip()
                                ->formatStateUsing(fn($record) => $record->created_at->diffForHumans())
                                ->visible(fn($record) => $record->created_at),
                            Components\TextEntry::make('updated_at')
                                ->translateLabel()
                                ->dateTooltip()
                                ->formatStateUsing(fn($record) => $record->updated_at->diffForHumans())
                                ->visible(fn($record) => $record->updated_at),

                            Components\TextEntry::make('created_by.full_name')
                                ->label(__('helpFormColumns.created_by_id'))
                                ->url(
                                    fn($record) => $record->created_by
                                        ? UserResource::getUrl('view', ['record' => $record->created_by->id])
                                        : null
                                )
                                ->visible(fn($record) => $record->created_by),
                            Components\TextEntry::make('responsible_user.full_name')
                                ->label(__('helpFormColumns.responsible_user_id'))
                                ->url(
                                    fn($record) => $record->responsible_user
                                        ? UserResource::getUrl('view', ['record' => $record->responsible_user->id])
                                        : null
                                )
                                ->visible(fn($record) => $record->responsible_user),
                        ]),
                    ]),
            ]);
    }

    protected function sideSection2()
    {
        return Components\Section::make()
            ->schema([
                Components\Grid::make(1)
                    ->schema([
                        Components\Group::make([
                            Components\Group::make([
                                Components\TextEntry::make('payment_status')
                                    ->label('حالة الصرف')
                                    ->visible(fn($record) => $record->status === 'APPROVED')
                                    ->state(function ($record) {
                                        if (!$record->payment_at) {
                                            return 'لم يتم الصرف';
                                        }
                                        return $record->payment_at->format('Y-m-d');
                                    })
                                    ->color(fn($record) => $record->payment_at ? 'success' : 'warning')
                                    ->icon('heroicon-o-currency-dollar'),

                                // Payment Receipt Link
                                /*Components\ImageEntry::make('payment_receipt_path')
                                    ->label('إيصال الدفع')
                                    ->checkFileExistence(false)
                                    ->disk('public')
                                    ->grow(false)
                                    ->visible(fn($record) => $record->payment_at && $record->payment_receipt_path)
                                    ->url(fn($record) => $record->payment_receipt_path)
                                    ->extraImgAttributes(fn($record) => [
                                        'loading' => 'lazy',
                                    ])
                                    ->openUrlInNewTab(),*/
                            ])->visible(fn($record) => $record->status === 'APPROVED'),

                            Components\TextEntry::make('user_region.title_ar')
                                ->color($this->record->user_region ? null : 'danger')
                                ->state($this->record->user_region ? $this->record->user_region->title_ar : 'لم يتم تحديد مكان إقامة المستفيد، هذا مؤثر على حساب مقدار المساعدة')
                                ->sensitive()
                                ->label(__('userColumns.user_region_id')),
                            Components\TextEntry::make('breadwinner.full_name')
                                ->label(__('helpFormColumns.breadwinner_id'))
                                ->url(
                                    fn($record) => $record->breadwinner
                                        ? UserResource::getUrl('view', ['record' => $record->breadwinner->id])
                                        : null
                                )
                                ->visible(fn($record) => $record->breadwinner),
                        ]),
                    ]),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            //\Parallax\FilamentComments\Actions\CommentsAction::make(),
            MediaAction::make('print')
                ->label('PDF')
                ->icon('heroicon-o-printer')
                ->color('gray')
                ->media(fn($record) => route('admin.help-forms.pdf', [
                    'help_form' => $record,
                    'form_id' => $record->form_id,
                ]))
                ->visible(fn($record) => Auth::user()->can('pdf', $record))
                ->preload(false),

            ActionGroup::make([
                /*Action::make('approve')
                    ->label('Approve Form')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Approve Help Form')
                    ->modalDescription('Are you sure you want to approve this help form? Please provide the approval details.')
                    ->visible(fn (HelpForm $record) =>
                        empty($record->closed_at) &&
                        auth()->user()->can('status', $record)
                    )
                    ->form([
                        Textarea::make('reason')
                            ->label('Approval Reason')
                            ->required()
                            ->maxLength(1000),
                        TextInput::make('helping_value')
                            ->label('Support Amount')
                            ->numeric()
                            ->required()
                            ->minValue(0),
                    ])
                    ->action(function (HelpForm $record, array $data) {
                        // Implement approval logic
                        $record->update([
                            'status' => CaseStatus::Approved,
                            'helping_value' => $data['helping_value'],
                            'closed_at' => now(),
                        ]);

                        $record->reasons()->create([
                            'user_id' => auth()->id(),
                            'content' => $data['reason'],
                            'status' => CaseStatus::Approved,
                        ]);

                        $this->notify('success', 'Form approved successfully');
                    }),

                Action::make('reject')
                    ->label('Reject Form')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Reject Help Form')
                    ->modalDescription('Are you sure you want to reject this help form? Please provide the rejection reason.')
                    ->visible(fn (HelpForm $record) =>
                        empty($record->closed_at) &&
                        auth()->user()->can('status', $record)
                    )
                    ->form([
                        Textarea::make('reason')
                            ->label('Rejection Reason')
                            ->required()
                            ->maxLength(1000),
                    ])
                    ->action(function (HelpForm $record, array $data) {
                        // Implement rejection logic
                        $record->update([
                            'status' => CaseStatus::Declined,
                            'closed_at' => now(),
                        ]);

                        $record->reasons()->create([
                            'user_id' => auth()->id(),
                            'content' => $data['reason'],
                            'status' => CaseStatus::Declined,
                        ]);

                        $this->notify('success', 'Form rejected successfully');
                    }),*/

                /*Action::make('comment')
                    ->label('Add Comment')
                    ->icon('heroicon-o-chat-bubble-left-ellipsis')
                    ->modalHeading('Add Comment')
                    ->visible(fn (HelpForm $record) => auth()->user()->can('comment', $record))
                    ->form([
                        Textarea::make('comment')
                            ->label('Comment')
                            ->required()
                            ->maxLength(1000),
                    ])
                    ->action(function (HelpForm $record, array $data) {
                        $record->comments()->create([
                            'user_id' => auth()->id(),
                            'content' => $data['comment'],
                        ]);

                        $this->notify('success', 'Comment added successfully');
                    }),*/

                Action::make('recalculateValues')
                    ->label('إعادة حساب القيم')
                    ->icon('heroicon-o-calculator')
                    ->color('warning')
                    ->visible(
                        fn($record) => Auth::user()->can('update', $record) &&
                            is_null($record->status)
                    )
                    ->requiresConfirmation()
                    ->modalHeading('إعادة حساب قيم المساعدة')
                    ->modalDescription('هل أنت متأكد من رغبتك في إعادة حساب قيم المساعدة؟ سيتم تحديث القيم المحسوبة تلقائياً.')
                    ->action(function (HelpForm $record) {
                        try {
                            if (/*is_null($record->user_region_id) &&*/!is_null($record->user->user_region_id))
                                $record->update([
                                    'user_region_id' => $record->user->user_region_id,
                                ]);

                            $annualHelping = $record->_calculated_annual_helping();
                            // Call the service or method that calculates the restricted value
                            $updatedValues = [
                                'calculated_restricted_value' => $record->_calculated_restricted_value(),
                                'calculated_annual_helping_value' => $annualHelping['value'],
                                'calculated_annual_helping_caption' => $annualHelping['caption'],
                            ];

                            // Update the record with new values
                            $record->update($updatedValues);

                            $this->refreshFormData([
                                'calculated_restricted_value',
                                'calculated_annual_helping_value',
                                'calculated_annual_helping_caption',
                            ]);

                            Notification::make()
                                ->title('تم إعادة الحساب بنجاح')
                                ->body('تم إعادة حساب وتحديث قيم المساعدة بنجاح.')
                                ->success()
                                ->send();
                        } catch (Exception $e) {
                            Log::error($e);
                            Notification::make()
                                ->title('خطأ في إعادة الحساب')
                                ->body('حدث خطأ أثناء محاولة إعادة حساب القيم: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])->visible(fn() => Auth::user()->can('update', $this->getRecord())),

            Action::make('addPayment')
                ->label('توثيق الصرف')
                ->icon('heroicon-o-currency-dollar')
                ->color('success')
                ->visible(
                    fn($record) => $record->status === 'APPROVED' &&
                        !$record->payment_at &&
                        Auth::user()->can('update', $record)
                )
                ->form([
                    FormsComponents\DatePicker::make('payment_at')
                        ->label('تاريخ الصرف')
                        ->required()
                        ->native(false)
                        ->displayFormat('Y-m-d'),

                    FormsComponents\FileUpload::make('payment_receipt')
                        ->label('إيصال المصروف')
                        ->required()
                        ->image()
                        ->directory('payment-receipts')
                        ->visibility('private'),
                ])
                ->action(function (array $data, $record) {
                    $record->update([
                        'payment_at' => $data['payment_at'],
                        'payment_receipt_path' => $data['payment_receipt'],
                    ]);
                    Notification::make()
                        ->body('تم حفظ بيانات المصروف بنجاح')
                        ->send();
                }),

            // Payment Receipt Viewer Action
            MediaAction::make('viewPaymentReceipt')
                ->label('عرض الإيصال')
                ->icon('heroicon-o-document')
                ->visible(
                    fn($record) => $record->status === 'APPROVED' &&
                        $record->payment_at &&
                        $record->payment_receipt_path
                )
                ->media(fn($record) => $record->payment_receipt_url)
                ->preload(false),
        ];
    }
}
