<?php

namespace App\Filament\Resources\Supporting\HelpFormRegionResource\Pages;

use App\Filament\Resources\Supporting\HelpFormRegionResource;
use Filament\Resources\Pages\ListRecords;

class ListHelpFormRegions extends ListRecords
{
    protected static string $resource = HelpFormRegionResource::class;
    protected static ?string $navigationLabel = 'الإعدادات';
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    public function getSubNavigation(): array
    {
        return static::getResource()::getRecordSubNavigation($this);
    }

    protected function getHeaderActions(): array
    {
        return [];
    }
}
