<?php

namespace App\Filament\Resources\Supporting\HelpFormRegionResource\RelationManagers;

use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class HelpFormUsersRelationManager extends RelationManager
{
    protected static string $relationship = 'help_form_users';

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return parent::canViewForRecord($ownerRecord, $pageClass);
    }

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return 'المستخدمون';
    }

    protected static function getModelLabel(): ?string
    {
        return 'مستخدم';
    }

    protected static function getPluralModelLabel(): ?string
    {
        return 'مستخدمون';
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('full_name')
            ->columns([
                Tables\Columns\TextColumn::make('family_user_id')
                    ->label(__('userColumns.family_user_id'))
                    ->sortable(false),
                Tables\Columns\TextColumn::make('full_name')
                    ->label(__('userColumns.full_name'))
                    ->sortable(false),
            ])
            ->filters([
                //
            ])
            ->inverseRelationship('help_form_user_regions')
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->form(fn(Tables\Actions\AttachAction $action): array => [
                        $action
                            ->getRecordSelect()
                            ->unique('help_form_user_regions', 'user_id',
                                modifyRuleUsing: fn($rule) => $rule->where('user_region_id', $this->getOwnerRecord()->id)
                            )
                            ->getSearchResultsUsing(function (string $search, callable $get): array {
                                return User::query()
                                    ->where(fn($query) => full_name_filter($query, $search))
                                    ->limit(50)
                                    ->with(['father.father.father'])
                                    ->get()
                                    //->pluck('full_name', 'id')
                                    ->mapWithKeys(function ($user) {
                                        return [$user->id => $user->full_name];
                                    })
                                    ->toArray();
                            }),
                        /*Forms\Components\Select::make('recordId')
                            ->label('المستخدم')
                            ->relationship(
                                name: 'user',
                                titleAttribute: 'full_name',
                                modifyQueryUsing: fn(Builder $query) => $query->withTrashed(),
                                ignoreRecord: true,
                            )
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search, callable $get): array {
                                return User::query()
                                    ->where(fn($query) => full_name_filter($query, $search))
                                    ->limit(50)
                                    ->with([
                                        'father.father.father',
                                    ])
                                    ->get()
                                    ->pluck('full_name', 'id')
                                    ->toArray();
                            })
                            ->reactive()
                            ->nullable(false)
                            ->dehydrated()
                            ->required(),*/
                    ])
                    ->recordSelectSearchColumns(['name', 'family_user_id']),
            ])
            ->actions([
                //Tables\Actions\EditAction::make(),
                Tables\Actions\DetachAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DetachAction::make(),
                ]),
            ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('المستخدم')
                    ->relationship(
                        name: 'user',
                        titleAttribute: 'full_name',
                        modifyQueryUsing: fn(Builder $query) => $query->withTrashed(),
                        ignoreRecord: true,
                    )
                    ->searchable()
                    ->getSearchResultsUsing(function (string $search, callable $get): array {
                        return User::query()
                            ->where(fn($query) => full_name_filter($query, $search))
                            ->limit(50)
                            ->with(['father.father.father'])
                            ->get()
                            ->pluck('full_name', 'id')
                            ->toArray();
                    })
                    ->reactive()
                    ->nullable(false)
                    ->dehydrated()
                    ->required(),
            ]);
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
