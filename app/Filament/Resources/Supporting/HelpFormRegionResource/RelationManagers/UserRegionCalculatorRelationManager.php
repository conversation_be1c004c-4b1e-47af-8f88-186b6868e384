<?php

namespace App\Filament\Resources\Supporting\HelpFormRegionResource\RelationManagers;

use App\Helpers\ArabicFormatter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class UserRegionCalculatorRelationManager extends RelationManager
{
    protected static string $relationship = 'calculator';

    protected static ?string $recordTitleAttribute = 'id';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return 'حاسبة الدعم';
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return parent::canViewForRecord($ownerRecord, $pageClass);
    }

    public function form(Form $form): Form
    {
        $restrictedValueFields = [];
        for ($i = 1; $i <= 20; $i++) {
            $restrictedValueFields["restricted_values.{$i}"] = Forms\Components\TextInput::make("restricted_values.{$i}")
                ->label($this->getRestrictedValuesTitle($i))
                ->numeric()
                ->default(0)
                ->minValue(0)
                ->maxValue(100000)
                ->required();
        }

        return $form
            ->schema([
                Forms\Components\Section::make('قيم الدعم الأساسية')
                    ->schema([
                        Forms\Components\TextInput::make('per_case')
                            ->label('قيمة دعم الحالة')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100000)
                            ->required(),

                        Forms\Components\TextInput::make('male_children')
                            ->label('قيمة دعم الأطفال "الذكور"')
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100000)
                            ->required(),

                        Forms\Components\TextInput::make('female_children')
                            ->label('قيمة دعم الأطفال "الإناث"')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100000)
                            ->required(),

                        Forms\Components\TextInput::make('wives')
                            ->label('قيمة دعم الزوجات')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100000)
                            ->required(),

                        Forms\Components\TextInput::make('family_husband')
                            ->label('قيمة دعم الزوج من داخل العائلة')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100000)
                            ->required(),

                        Forms\Components\TextInput::make('non_family_husband')
                            ->label('قيمة دعم الزوج من خارج العائلة')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100000)
                            ->required(),

                        Forms\Components\TextInput::make('debit')
                            ->label('قيمة دعم المديونية')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100000)
                            ->required(),

                        Forms\Components\TextInput::make('rent')
                            ->label('قيمة دعم الإيجار')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100000)
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('الحد المانع حسب أفراد الأسرة')
                    ->schema($restrictedValueFields)
                    ->columns(4),
            ]);
    }

    protected function getRestrictedValuesTitle(int $persons): string
    {
        return ArabicFormatter::formatNumber($persons, 'فرد');
    }

    public function table(Table $table): Table
    {
        // Initialize calculator if it doesn't exist
        $this->initializeCalculator();

        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('per_case')
                    ->label('قيمة دعم الحالة')
                    ->numeric(locale: 'en-US')
                    ->sortable(),

                Tables\Columns\TextColumn::make('male_children')
                    ->label('دعم الأطفال الذكور')
                    ->numeric(locale: 'en-US')
                    ->sortable(),

                Tables\Columns\TextColumn::make('female_children')
                    ->label('دعم الأطفال الإناث')
                    ->numeric(locale: 'en-US')
                    ->sortable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                //Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                //Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    protected function initializeCalculator(): void
    {
        if (!$this->ownerRecord->calculator()->exists()) {
            $defaultValues = [
                'per_case' => 0,
                'male_children' => 0,
                'female_children' => 0,
                'wives' => 0,
                'family_husband' => 0,
                'non_family_husband' => 0,
                'debit' => 0,
                'rent' => 0,
                'restricted_values' => array_fill(1, 20, 0),
            ];

            $this->ownerRecord->calculator()->create($defaultValues);
            $this->ownerRecord->load('calculator');
        }
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
