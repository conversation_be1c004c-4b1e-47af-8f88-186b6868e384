<?php

namespace App\Filament\Resources;

use App\Models\UserRegion;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use App\Filament\Resources\UserRegionResource\Pages;

class UserRegionResource extends Resource
{
    protected static ?string $model = UserRegion::class;
    protected static ?string $navigationIcon = 'heroicon-o-map';
    protected static ?string $navigationLabel = 'أماكن الإقامة';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'المشجرة';
    protected static ?string $modelLabel = 'مكان إقامة';
    protected static ?string $pluralModelLabel = 'أماكن الإقامة';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title_ar')
                    ->label('الاسم بالعربية')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('title_en')
                    ->label('الاسم بالإنجليزية')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(
                UserRegion::query()
                    ->withCount('users') // This will add users_count attribute
                    ->select(['id', 'title_en', 'title_ar', 'created_at', 'updated_at'])
                    ->orderBy('sort_order', 'asc')
            )
            ->columns([
                Tables\Columns\TextColumn::make('title_ar')
                    ->label('الاسم بالعربية')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('title_en')
                    ->label('الاسم بالإنجليزية')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('users_count')
                    ->label('عدد المستخدمين')
                    ->counts('users') // This explicitly tells Filament to count the relationship
                    ->numeric()
                    ->sortable(),


                Tables\Columns\TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->date(format: 'D, d M Y')
                    ->timezone('Asia/Riyadh') // Adjust to your timezone
                    ->sortable(),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'title_ar',
                    'title_en',
                    'users_count',
                    'updated_at',
                ])->filters([
                    // No existing filters
                ])->defaultFilters([]),
            ])->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalWidth('md'),
                Tables\Actions\EditAction::make()
                    ->modalWidth('md')
                    ->modalHeading('تعديل المنطقة')
                    ->modalSubmitActionLabel('حفظ')
                    ->modalCancelActionLabel('إلغاء'),
                Tables\Actions\DeleteAction::make()
                    ->modalHeading('حذف المنطقة')
                    ->modalDescription('هل أنت متأكد من حذف هذه المنطقة؟')
                    ->modalSubmitActionLabel('حذف')
                    ->modalCancelActionLabel('إلغاء'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->modalHeading('حذف المناطق المحددة')
                        ->modalDescription('هل أنت متأكد من حذف المناطق المحددة؟')
                        ->modalSubmitActionLabel('حذف')
                        ->modalCancelActionLabel('إلغاء'),
                ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserRegions::route('/'),
        ];
    }
}
