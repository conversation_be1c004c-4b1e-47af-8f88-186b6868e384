<?php

namespace App\Filament\Resources\BankAccountTransactionUserResource\Pages;

use App\Filament\Resources\BankAccountTransactionUserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBankAccountTransactionUsers extends ListRecords
{
    protected static string $resource = BankAccountTransactionUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
