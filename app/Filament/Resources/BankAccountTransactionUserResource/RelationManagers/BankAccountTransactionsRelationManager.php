<?php

namespace App\Filament\Resources\BankAccountTransactionUserResource\RelationManagers;

use App\Filament\Resources\BankAccountTransactionResource;
use App\Filament\Resources\UserResource;
use App\Models\BankAccountTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BankAccountTransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';
    protected static ?string $title = 'الحوالات';
    protected static ?int $navigationSort = -99;
    protected static ?string $modelLabel = 'حوالة';
    protected static ?string $pluralModelLabel = 'الحوالات';


    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('bank_account_id')
                    ->relationship('bank_account', 'title')
                    ->required()
                    ->label('الحساب البنكي'),

                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->maxLength(191)
                    ->label('المبلغ'),

                Forms\Components\TextInput::make('reference')
                    ->maxLength(191)
                    ->label('المرجع'),

                Forms\Components\Select::make('account')
                    ->options(function () {
                        return BankAccountTransaction::query()
                            ->where('user_id', $this->getOwnerRecord()->id)
                            ->whereNotNull('account')
                            ->distinct()
                            ->pluck('account', 'account')
                            ->toArray();
                    })
                    ->searchable()
                    ->createOptionForm([
                        Forms\Components\TextInput::make('account')
                            ->label('رقم الحساب')
                            ->maxLength(191)
                            ->required()
                    ])
                    ->createOptionUsing(function (array $data) {
                        return $data['account'];
                    })
                    ->label('الحساب'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('reference')
            ->recordUrl(fn($record) => BankAccountTransactionResource::getUrl('view', ['record' => $record]))
            ->columns([
                Tables\Columns\TextColumn::make('amount')
                    ->numeric(locale: 'en-US')
                    ->searchable()
                    ->sortable()
                    ->label('المبلغ'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإضافة')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reference')
                    ->searchable()
                    ->sortable()
                    ->label('المرجع'),
                Tables\Columns\TextColumn::make('account')
                    ->searchable()
                    ->sortable()
                    ->label('الحساب'),
                Tables\Columns\TextColumn::make('bank_account.title')
                    ->searchable()
                    ->sortable()
                    ->label('الحساب البنكي'),

            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->authorize(fn() => auth()->user()->can('create', BankAccountTransaction::class))
                    ->label('إنشاء')
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('تعديل'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),
                ]),
            ]);
    }

    public function isReadOnly(): bool
    {
        return parent::isReadOnly();
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return parent::canViewForRecord($ownerRecord, $pageClass);
    }
}
