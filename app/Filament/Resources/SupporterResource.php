<?php

namespace App\Filament\Resources;

use App\Enums\Gender;
use App\Filament\Resources\SupporterResource\Pages;
use App\Filament\Resources\SupporterResource\RelationManagers;
use App\Models\Supporter;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Actions;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SupporterResource extends Resource
{
    protected static ?string $model = Supporter::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'الداعمون';
    protected static ?string $navigationGroup = 'الدعم';
    protected static ?string $modelLabel = 'داعم';
    protected static ?string $pluralModelLabel = 'الداعمون';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('اسم الداعم')
                            ->placeholder('اسم الداعم')
                            ->required(),

                        Forms\Components\TextInput::make('phone')
                            ->label('رقم الجوال')
                            ->placeholder('رقم الجوال')
                            ->required()
                            ->unique()
                            ->tel(),

                        Forms\Components\TextInput::make('account')
                            ->label('رقم الحساب')
                            ->required()
                            ->unique()
                            ->placeholder('رقم الحساب'),

                        Forms\Components\Select::make('user_id')
                            ->label('المستخدم')
                            ->placeholder('المستخدم')
                            ->relationship('user', 'full_name')
                            ->searchable()
                            ->debounce(250)
                            ->allowHtml()
                            ->getSearchResultsUsing(function (string $search): array {
                                return User::query()
                                    ->where(fn($query) => full_name_filter($query, $search))
                                    ->limit(50)
                                    ->with(['father.father.father'])
                                    ->get()
                                    ->pluck('full_name', 'id')
                                    ->toArray();
                            })
                            ->reactive()
                            ->getOptionLabelUsing(function ($value) {
                                $user = \App\Models\User::find($value);
                                if (!$user) return null;

                                $label = $user->full_name ?? $user->name;
                                if ($user->family_user_id) {
                                    $label .= " #{$user->family_user_id}";
                                }
                                return $label;
                            }),
                    ]),
            ])
            ->columns(12);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(trans('supporterColumns.id'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('name')
                    ->label(trans('supporterColumns.name'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('phone')
                    ->label(trans('supporterColumns.phone'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('account')
                    ->label(trans('supporterColumns.account'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('transactions_count')
                    ->label(trans('bankAccountColumns.transactions_count'))
                    ->counts('transactions')
                    ->sortable(),

                Tables\Columns\TextColumn::make('transactions_sum_amount')
                    ->label(trans('bankAccountColumns.transactions_sum_amount'))
                    ->sum('transactions', 'amount')
                    ->numeric(0)
                    ->formatStateUsing(fn($state) => number_format($state)),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(trans('supporterColumns.created_at'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(trans('supporterColumns.updated_at'))
                    ->sortable(),
            ])
            ->filters([
                // You can add filters here later if needed
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    // Add transaction modal action
                    Tables\Actions\Action::make('support')
                        ->label('دعم')
                        ->icon('heroicon-o-currency-dollar')
                        ->visible(fn(Supporter $record) => auth()->user()->can('create', $record) || auth()->user()->can('update', $record))
                        ->modalHeading('إضافة تحويل جديد')
                        ->modalSubmitActionLabel('حفظ')
                        ->form([
                            Forms\Components\Select::make('bank_account_id')
                                ->label('حساب المؤسسة')
                                ->placeholder('حساب المؤسسة')
                                ->options(function () {
                                    $options = [];
                                    $bankAccounts = \App\Models\BankAccount::where('is_enabled', true)
                                        ->get()
                                        ->groupBy('type');

                                    foreach ($bankAccounts as $type => $accounts) {
                                        $optionGroup = [];
                                        foreach ($accounts as $account) {
                                            $optionGroup[$account->id] = $account->title;
                                        }
                                        $options[__("bankAccountColumns.types.$type")] = $optionGroup;
                                    }

                                    return $options;
                                })
                                ->required(),

                            Forms\Components\TextInput::make('reference')
                                ->label(trans('bankAccountTransactionColumns.reference'))
                                ->placeholder(trans('bankAccountTransactionColumns.reference'))
                                ->maxLength(191),

                            Forms\Components\TextInput::make('amount')
                                ->label(trans('bankAccountTransactionColumns.amount'))
                                ->placeholder(trans('bankAccountTransactionColumns.amount'))
                                ->numeric()
                                ->required(),

                            Forms\Components\DatePicker::make('due_at')
                                ->label(trans('bankAccountTransactionColumns.due_at'))
                                ->placeholder(trans('bankAccountTransactionColumns.due_at')),

                            Forms\Components\Toggle::make('is_refund')
                                ->label(trans('bankAccountTransactionColumns.is_refund'))
                                ->default(false),

                            Forms\Components\Toggle::make('anonymous')
                                ->label('مجهول')
                                ->default(false),

                        ])
                        ->action(function (array $data, Supporter $record): void {
                            $record->transactions()->create($data);
                            Notification::make()
                                ->title('تم إضافة التحويل بنجاح')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\ViewAction::make()
                        ->visible(fn(Supporter $record) => auth()->user()->can('update', $record)),

                    Tables\Actions\EditAction::make()
                        ->visible(fn(Supporter $record) => auth()->user()->can('update', $record)),

                    Tables\Actions\DeleteAction::make()
                        ->visible(fn(Supporter $record) => auth()->user()->can('delete', $record)),
                ])
                    ->tooltip('الإجراءات')
                    ->icon('heroicon-m-ellipsis-vertical'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make(3)
                    ->schema([
                        // Main content (span 2 columns)
                        Section::make('بيانات الداعم')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('اسم الداعم')
                                    ->copyable()
                                    ->weight(FontWeight::Bold),

                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('phone')
                                            ->label('رقم الجوال')
                                            ->copyable()
                                            ->icon('heroicon-o-phone'),

                                        TextEntry::make('account')
                                            ->label('رقم الحساب')
                                            ->copyable()
                                            ->icon('heroicon-o-credit-card'),
                                    ]),

                                TextEntry::make('user.full_name')
                                    ->label('المستخدم المرتبط')
                                    ->placeholder('لا يوجد مستخدم مرتبط')
                                    ->url(fn(Supporter $record) => $record->user ? UserResource::getUrl('view', ['record' => $record->user->id]) : null)
                                    ->icon('heroicon-o-user'),
                            ])
                            ->columnSpan(2),

                        // Sidebar content (span 1 column)
                        Grid::make(1)
                            ->schema([
                                Section::make('ملخص البيانات')
                                    ->schema([
                                        TextEntry::make('transactions_count')
                                            ->label('عدد التحويلات')
                                            ->state(function (Supporter $record): int {
                                                return $record->transactions()->count();
                                            })
                                            ->icon('heroicon-o-calculator'),

                                        TextEntry::make('transactions_sum')
                                            ->label('إجمالي المبالغ')
                                            ->state(function (Supporter $record): float {
                                                return $record->transactions()->sum('amount');
                                            })
                                            ->numeric(0)
                                            ->riyal()
                                            ->color(fn($state) => $state > 0 ? 'success' : ($state < 0 ? 'danger' : 'gray'))
                                            ->icon('heroicon-o-currency-dollar'),

                                        TextEntry::make('last_transaction')
                                            ->label('آخر تحويل')
                                            ->state(function (Supporter $record) {
                                                $lastTransaction = $record->transactions()->latest('due_at')->first();
                                                return $lastTransaction ? $lastTransaction->due_at : null;
                                            })
                                            ->date()
                                            ->placeholder('لا توجد تحويلات')
                                            ->icon('heroicon-o-calendar'),

                                        Fieldset::make('معلومات النظام')
                                            ->schema([
                                                TextEntry::make('created_at')
                                                    ->label('تاريخ الإنشاء')
                                                    ->date()
                                                    ->dateTimeTooltip(),

                                                TextEntry::make('updated_at')
                                                    ->label('تاريخ التحديث')
                                                    ->date()
                                                    ->dateTimeTooltip(),
                                            ]),
                                    ]),
                            ])
                            ->columnSpan(1),
                    ])
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\TransactionsRelationManager::make(),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSupporters::route('/'),
            'create' => Pages\CreateSupporter::route('/create'),
            'view' => Pages\ViewSupporter::route('/{record}'),
            'edit' => Pages\EditSupporter::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withCount('transactions')
            ->withSum('transactions', 'amount');
    }
}
