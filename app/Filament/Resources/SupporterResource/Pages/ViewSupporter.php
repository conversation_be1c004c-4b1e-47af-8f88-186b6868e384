<?php

namespace App\Filament\Resources\SupporterResource\Pages;

use App\Filament\Resources\SupporterResource;
use Filament\Actions;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions\Action;

class ViewSupporter extends ViewRecord
{
    protected static string $resource = SupporterResource::class;

    protected function getHeaderActions(): array
    {
        return [
                Action::make('add_transaction')
                    ->label('إضافة تحويل جديد')
                    ->icon('heroicon-o-currency-dollar')
                    ->color('primary')
                    ->form([
                        Select::make('bank_account_id')
                            ->label('حساب المؤسسة')
                            ->placeholder('حساب المؤسسة')
                            ->options(function () {
                                $options = [];
                                $bankAccounts = \App\Models\BankAccount::where('is_enabled', true)
                                    ->get()
                                    ->groupBy('type');

                                foreach ($bankAccounts as $type => $accounts) {
                                    $optionGroup = [];
                                    foreach ($accounts as $account) {
                                        $optionGroup[$account->id] = $account->title;
                                    }
                                    $options[__("bankAccountColumns.types.$type")] = $optionGroup;
                                }

                                return $options;
                            })
                            ->required(),

                        TextInput::make('amount')
                            ->label(trans('bankAccountTransactionColumns.amount'))
                            ->placeholder(trans('bankAccountTransactionColumns.amount'))
                            ->numeric()
                            ->required(),

                        DatePicker::make('due_at')
                            ->label(trans('bankAccountTransactionColumns.due_at'))
                            ->placeholder(trans('bankAccountTransactionColumns.due_at')),

                        TextInput::make('reference')
                            ->label(trans('bankAccountTransactionColumns.reference'))
                            ->placeholder(trans('bankAccountTransactionColumns.reference'))
                            ->maxLength(191),

                        Toggle::make('is_refund')
                            ->label(trans('bankAccountTransactionColumns.is_refund'))
                            ->default(false),

                        Toggle::make('anonymous')
                            ->label('مجهول')
                            ->default(false),
                    ])
                    ->action(function (array $data): void {
                        $this->record->transactions()->create($data);
                        Notification::make()
                            ->title('تم إضافة التحويل بنجاح')
                            ->success()
                            ->send();
                    }),
            Actions\ActionGroup::make([
                Actions\EditAction::make()
                    ->label('تعديل بيانات الداعم'),

                Actions\DeleteAction::make()
                    ->label('حذف الداعم')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('هل أنت متأكد من حذف هذا الداعم؟')
                    ->modalDescription('سيتم حذف هذا الداعم وجميع التحويلات المرتبطة به بشكل نهائي.')
                    ->modalSubmitActionLabel('نعم، قم بالحذف')
                    ->modalCancelActionLabel('إلغاء')
                    ->successNotificationTitle('تم حذف الداعم بنجاح')
                    ->visible(fn() => auth()->user()->can('delete', $this->record))
                    ->action(function (): void {
                        // Get the supporter ID
                        $supporterId = $this->record->id;

                        // Use a database transaction to ensure all or nothing
                        \DB::transaction(function () use ($supporterId) {
                            // First delete all related transactions
                            \DB::table('bank_account_transactions')
                                ->where('supporter_id', $supporterId)
                                ->delete();

                            // Then delete the supporter directly
                            \DB::table('supporters')
                                ->where('id', $supporterId)
                                ->delete();
                        });

                        // Show success notification
                        Notification::make()
                            ->title('تم حذف الداعم بنجاح')
                            ->success()
                            ->send();

                        // Redirect to index page
                        $this->redirect(SupporterResource::getUrl());
                    }),
            ])
                ->tooltip('الإجراءات')
                ->icon('heroicon-m-ellipsis-vertical'),
        ];
    }
    protected bool $canCreate = true;

    public static function canCreate(): bool
    {
        return true;
    }

}
