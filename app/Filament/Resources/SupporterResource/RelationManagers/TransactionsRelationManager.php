<?php

namespace App\Filament\Resources\SupporterResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use App\Models\BankAccountTransaction;
use App\Models\BankAccount;

class TransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';

    protected static ?string $recordTitleAttribute = 'amount';
    protected static ?string $title = 'التحويلات';
    protected static ?string $modelLabel = 'تحويل';
    protected static ?string $pluralModelLabel = 'التحويلات';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('bank_account_id')
                    ->label('حساب المؤسسة')
                    ->placeholder('حساب المؤسسة')
                    ->options(function () {
                        $options = [];
                        $bankAccounts = BankAccount::where('is_enabled', true)
                            ->get()
                            ->groupBy('type');

                        foreach ($bankAccounts as $type => $accounts) {
                            $optionGroup = [];
                            foreach ($accounts as $account) {
                                $optionGroup[$account->id] = $account->title;
                            }
                            $options[__("bankAccountColumns.types.$type")] = $optionGroup;
                        }

                        return $options;
                    })
                    ->required(),

                Forms\Components\TextInput::make('reference')
                    ->label(trans('bankAccountTransactionColumns.reference'))
                    ->placeholder(trans('bankAccountTransactionColumns.reference'))
                    ->maxLength(191),

                Forms\Components\TextInput::make('amount')
                    ->label(trans('bankAccountTransactionColumns.amount'))
                    ->placeholder(trans('bankAccountTransactionColumns.amount'))
                    ->numeric()
                    ->required(),

                Forms\Components\DatePicker::make('due_at')
                    ->label(trans('bankAccountTransactionColumns.due_at'))
                    ->placeholder(trans('bankAccountTransactionColumns.due_at')),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('bank_account.title')
                    ->label(trans('bankAccountTransactionColumns.bank_account_id')),

                Tables\Columns\TextColumn::make('amount')
                    ->label(trans('bankAccountTransactionColumns.amount'))
                    ->numeric()
                    ->searchable()
                    ->copyable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('due_at')
                    ->label(trans('bankAccountTransactionColumns.due_at'))
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('reference')
                    ->label(trans('bankAccountTransactionColumns.reference'))
                    ->searchable()
                    ->copyable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_refund')
                    ->label(trans('bankAccountTransactionColumns.is_refund'))
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإضافة')
                    ->date()
                    ->dateTimeTooltip()
                    ->sortable(),
            ])
            ->filters([
                // Add any filters you need
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->authorize(fn() => auth()->user()->can('create', BankAccountTransaction::class))
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->authorize(fn (BankAccountTransaction $record) => auth()->user()->can('update', $record)), // Explicitly passing both user and record
                Tables\Actions\DeleteAction::make()
                    ->authorize(fn (BankAccountTransaction $record) => auth()->user()->can('delete', $record)), // Explicitly passing both user and record
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


}
