<?php

namespace App\Filament\Resources\ConsultationResource\RelationManagers;

use App\Filament\Actions\CopyFamilyUserIdsAction;
use App\Filament\Components\UserFilter;
use App\Filament\Components\UserSelect;
use App\Filament\Traits\UserSelectSearchHandler;
use App\Models\ConsultationUser;
use Archilex\AdvancedTables\AdvancedTables;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rules\Unique;

class ConsultantsRelationManager extends RelationManager
{
    use AdvancedTables;
    use UserSelectSearchHandler;

    protected static string $relationship = 'consultants';

    protected static ?string $title = 'المستشارين';

    protected static ?string $pluralTitle = 'المستشارين';

    protected static ?string $modelLabel = 'مستشار';

    protected static ?string $pluralModelLabel = 'مستشارين';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                UserSelect::make('user_id')
                    ->label(__('المستخدم'))
                    ->multiple()
                    ->unique(ignoreRecord: true, modifyRuleUsing: function (Unique $rule, $get) {
                        $rule->where('consultation_id', value: $this->ownerRecord->id);
                    })
                    ->searchable()
                    ->required()
                    ->disabledOn('edit'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('لا يوجد مستشارين'))
            ->emptyStateDescription('')
            ->recordTitleAttribute('user.full_name')
            ->columns([
                Tables\Columns\ImageColumn::make('user.profile_photo_url')
                    ->label('')
                    ->circular()
                    ->size(40),
                Tables\Columns\TextColumn::make('user.family_user_id')
                    ->label(__('consultationColumns.family_user_id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.full_name')
                    ->label(__('consultationColumns.consultant_name'))
                    ->url(fn($record) => route('filament.admin.resources.users.view', $record->user))
                    ->color('primary')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('user', function (Builder $query) use ($search) {
                            return full_name_filter($query, $search);
                        });
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->label(__('consultationColumns.email'))
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('user.phone')
                    ->label(__('consultationColumns.phone'))
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('consultationColumns.assigned_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                AdvancedFilter::make()->includeColumns()->filters([
                    UserFilter::make('user.full_name')
                        ->label(__('المستخدم'))
                        ->setUserRelationshipName('user'),
                ])->defaultFilters([]),
            ])
            ->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->action(function (array $data): void {
                        foreach ($data['user_id'] as $userId) {
                            $this->getRelationship()->create([
                                'user_id' => $userId,
                            ]);
                        }
                    })
                    ->label(__('إضافة مستشار')),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->label(__('تعديل')),
                    Tables\Actions\DeleteAction::make()
                        ->label(__('حذف')),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label(__('حذف المحدد')),
                ]),
                CopyFamilyUserIdsAction::make()
                    ->field('user.family_user_id'),
            ]);
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return parent::canViewForRecord($ownerRecord, $pageClass);
    }
}
