<?php

namespace App\Filament\Resources\HallReservationResource\Concerns;

use App\Enums\HallEventType;
use App\Enums\HallReservationStatus;
use Filament\Infolists;
use Filament\Infolists\Infolist;

trait HasReservationInfolist
{
    public function getReservationInfolistSchema(): array
    {
        return [
            Infolists\Components\Section::make('معلومات المستخدم')
                ->schema([
                    Infolists\Components\TextEntry::make('user.name')
                        ->label('المستخدم')
                        ->getStateUsing(fn($record): string => $record->user?->getFullName(1, true) ?? 'غير محدد')
                        ->url(fn($record) => $record->user ? route('filament.admin.resources.users.view', $record->user) : null)
                        ->color(fn($record) => $record->user ? 'primary' : 'gray'),

                    Infolists\Components\TextEntry::make('created_by.name')
                        ->label('أُنشئ بواسطة')
                        ->getStateUsing(fn($record): string => $record->created_by?->getFullName(1, true) ?? 'غير محدد')
                        ->url(fn($record) => $record->created_by ? route('filament.admin.resources.users.view', $record->created_by) : null)
                        ->color(fn($record) => $record->created_by ? 'primary' : 'gray'),
                ])
                ->columns(2),

            Infolists\Components\Section::make('تفاصيل الحجز')
                ->schema([
                    Infolists\Components\TextEntry::make('event_type')
                        ->label('نوع الفعالية')
                        ->formatStateUsing(fn(string $state): string => match ($state) {
                            HallEventType::Meeting => 'اجتماع',
                            HallEventType::Marriage => 'زواج',
                            default => $state,
                        })
                        ->badge()
                        ->color(fn(string $state): string => match ($state) {
                            HallEventType::Meeting => 'info',
                            HallEventType::Marriage => 'warning',
                            default => 'gray',
                        }),

                    Infolists\Components\TextEntry::make('start_at')
                        ->label('تاريخ ووقت البداية')
                        ->dateTime('d/m/Y - g:i A')
                        ->formatStateUsing(function ($state): string {
                            $formatted = $state->locale('ar')->isoFormat('DD/MM/YYYY - h:mm A');
                            return $formatted;
                        }),

                    Infolists\Components\TextEntry::make('end_at')
                        ->label('تاريخ ووقت النهاية')
                        ->dateTime('d/m/Y - g:i A')
                        ->formatStateUsing(function ($state): string {
                            $formatted = $state->locale('ar')->isoFormat('DD/MM/YYYY - h:mm A');
                            return $formatted;
                        }),

                    Infolists\Components\TextEntry::make('status')
                        ->label('الحالة')
                        ->formatStateUsing(fn(string $state): string => match ($state) {
                            HallReservationStatus::Pending => 'بالانتظار',
                            HallReservationStatus::Confirmed => 'مؤكد',
                            HallReservationStatus::Declined => 'مرفوض',
                            HallReservationStatus::Cancelled => 'ملغي',
                            HallReservationStatus::Completed => 'مكتمل',
                            default => $state,
                        })
                        ->badge()
                        ->color(fn(string $state): string => match ($state) {
                            HallReservationStatus::Pending => 'warning',
                            HallReservationStatus::Confirmed => 'success',
                            HallReservationStatus::Declined => 'danger',
                            HallReservationStatus::Cancelled => 'gray',
                            HallReservationStatus::Completed => 'primary',
                            default => 'gray',
                        }),

                    Infolists\Components\TextEntry::make('services.title')
                        ->label('الخدمات')
                        ->badge()
                        ->separator(', ')
                        ->color(fn($record): string => 'primary'),
                ])
                ->columns(2),

            Infolists\Components\Section::make('معلومات الموافقة')
                ->schema([
                    Infolists\Components\TextEntry::make('approved_by.name')
                        ->label('وافق عليه')
                        ->formatStateUsing(fn($record): string => $record->approved_by?->getFullName(1, true) ?? 'غير محدد')
                        ->placeholder('غير محدد'),

                    Infolists\Components\TextEntry::make('approved_at')
                        ->label('تاريخ الموافقة')
                        ->dateTime('d/m/Y H:i')
                        ->placeholder('غير محدد'),

                    Infolists\Components\TextEntry::make('notified_at')
                        ->label('تاريخ الإشعار')
                        ->dateTime('d/m/Y H:i')
                        ->placeholder('غير محدد'),
                ])
                ->columns(3)
                ->visible(fn($record): bool => in_array($record->status, [HallReservationStatus::Confirmed, HallReservationStatus::Completed]) || $record->approved_at),
        ];
    }
}
