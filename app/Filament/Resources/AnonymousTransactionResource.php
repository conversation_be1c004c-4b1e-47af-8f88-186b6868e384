<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AnonymousTransactionResource\Pages;
use App\Filament\Resources\AnonymousTransactionResource\RelationManagers;
use App\Models\BankAccountTransaction;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class AnonymousTransactionResource extends Resource
{
    protected static ?string $model = BankAccountTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'المعاملات المجهولة';

    protected static ?string $navigationGroup = 'الدعم';

    protected static ?string $modelLabel = 'معاملة مجهولة';

    protected static ?string $pluralModelLabel = 'المعاملات المجهولة';

    protected static ?int $navigationSort = 1;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('anonymous', true)
            ->where('is_verified', false);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('تفاصيل المعاملة')
                    ->schema([
                        Forms\Components\Select::make('bank_account_id')
                            ->label('الحساب البنكي')
                            ->relationship('bank_account', 'title')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\TextInput::make('reference')
                            ->label('المرجع')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('account')
                            ->label('رقم الحساب')
                            ->maxLength(255),

                        Forms\Components\DatePicker::make('due_at')
                            ->label('تاريخ الاستحقاق')
                            ->required(),

                        Forms\Components\TextInput::make('amount')
                            ->label('المبلغ')
                            ->riyal()
                            ->required()
                            ->suffix('ريال'),

                        Forms\Components\Select::make('user_id')
                            ->label('ربط بمستخدم (اختياري)')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->nullable(),

                        Forms\Components\Textarea::make('upgrade_notes')
                            ->label('ملاحظات')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('إعدادات المعاملة')
                    ->schema([
                        Forms\Components\Toggle::make('anonymous')
                            ->label('معاملة مجهولة')
                            ->default(true)
                            ->disabled(),

                        Forms\Components\Toggle::make('is_verified')
                            ->label('تم التحقق')
                            ->default(false),

                        Forms\Components\Toggle::make('is_refund')
                            ->label('عملية استرداد')
                            ->default(false),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('الرقم')
                    ->sortable(),

                TextColumn::make('bank_account.title')
                    ->label('الحساب البنكي')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('amount')
                    ->label('المبلغ')
                    ->money('SAR')
                    ->sortable(),

                TextColumn::make('reference')
                    ->label('المرجع')
                    ->searchable()
                    ->placeholder('-'),

                TextColumn::make('account')
                    ->label('رقم الحساب')
                    ->searchable()
                    ->placeholder('-'),

                TextColumn::make('due_at')
                    ->label('تاريخ الاستحقاق')
                    ->date()
                    ->sortable(),

                IconColumn::make('is_refund')
                    ->label('مسترد')
                    ->boolean(),

                TextColumn::make('user.name')
                    ->label('مرتبط بمستخدم')
                    ->placeholder('غير مرتبط')
                    ->url(fn (BankAccountTransaction $record) =>
                        $record->user ? UserResource::getUrl('view', ['record' => $record->user->id]) : null
                    )
                    ->color('primary'),

                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('bank_account_id')
                    ->label('الحساب البنكي')
                    ->relationship('bank_account', 'title')
                    ->searchable()
                    ->preload(),

                TernaryFilter::make('is_refund')
                    ->label('عملية استرداد'),

                TernaryFilter::make('user_id')
                    ->label('مرتبط بمستخدم')
                    ->nullable(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),

                Tables\Actions\EditAction::make()
                    ->label('تعديل'),

                Action::make('refund')
                    ->label('استرداد')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->visible(fn (BankAccountTransaction $record) => !$record->is_refund)
                    ->requiresConfirmation()
                    ->modalHeading('تأكيد عملية الاسترداد')
                    ->modalDescription('هل أنت متأكد من أنك تريد تحويل هذه المعاملة إلى عملية استرداد؟')
                    ->action(function (BankAccountTransaction $record) {
                        $record->update(['is_refund' => true]);

                        Notification::make()
                            ->title('تم تحويل المعاملة إلى عملية استرداد بنجاح')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\DeleteAction::make()
                    ->label('حذف'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CommentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAnonymousTransactions::route('/'),
            'create' => Pages\CreateAnonymousTransaction::route('/create'),
            'view' => Pages\ViewAnonymousTransaction::route('/{record}'),
            'edit' => Pages\EditAnonymousTransaction::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('supporters.create');
    }

    public static function canView(Model $record): bool
    {
        return auth()->user()->can('supporters.index');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->can('supporters.update');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('supporters.update') && !$record->is_refund;
    }
}
