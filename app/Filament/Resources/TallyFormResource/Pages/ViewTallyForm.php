<?php

namespace App\Filament\Resources\TallyFormResource\Pages;

use App\Exports\TallyFormSubmissionsExport;
use App\Filament\Resources\TallyFormResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Maatwebsite\Excel\Facades\Excel;

class ViewTallyForm extends ViewRecord
{
    protected static string $resource = TallyFormResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('تعديل'),
            Actions\Action::make('export')
                ->label('تصدير التقديمات')
                ->icon('heroicon-o-arrow-down-tray')
                ->action(function () {
                    return Excel::download(
                        new TallyFormSubmissionsExport($this->record),
                        $this->record->form_name . '.xlsx'
                    );
                })
        ];
    }

}
