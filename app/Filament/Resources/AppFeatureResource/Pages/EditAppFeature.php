<?php

namespace App\Filament\Resources\AppFeatureResource\Pages;

use App\Filament\Resources\AppFeatureResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Laravel\Pennant\Feature;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Cache;

class EditAppFeature extends EditRecord
{
  protected static string $resource = AppFeatureResource::class;

  protected function getHeaderActions(): array
  {
    return [
      Actions\ViewAction::make(),
      Actions\DeleteAction::make(),
    ];
  }

  protected function getRedirectUrl(): string
  {
    return $this->getResource()::getUrl('index');
  }

  protected function afterSave(): void
  {
    $scopes = $this->data['scopes'] ?? [];
    $slug = $this->record->slug;

    // Get all possible scopes
    $allScopes = [
      'guest',
      'auth',
      ...Role::pluck('id')->map(fn($k) => Role::class . "|$k")->toArray()
    ];

    // Activate feature for selected scopes
    foreach ($scopes as $scope) {
      Feature::for($scope)->activate($slug);
    }

    // Deactivate feature for unselected scopes
    foreach (collect($allScopes)->diff($scopes) as $scope) {
      Feature::for($scope)->deactivate($slug);
    }

    // Clear feature cache
    Cache::tags("features:role")->flush();
  }
}
