<?php

namespace App\Filament\Resources\ActivityMemberRoleResource\Pages;

use App\Filament\Resources\ActivityMemberRoleResource;
use App\Filament\Resources\ActivityMemberRoleResource\Widgets\ActivityMemberRoleStatsWidget;
use App\Filament\Resources\ActivityMemberRoleResource\Widgets\MostUsedRoleWidget;
use App\Filament\Resources\ActivityResource;
use Archilex\AdvancedTables\AdvancedTables;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListActivityMemberRoles extends ListRecords
{
    use AdvancedTables;
    protected static string $resource = ActivityMemberRoleResource::class;

    protected static ?string $navigationLabel = 'أدوار الأعضاء';


    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('إضافة دور جديد'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            ActivityMemberRoleStatsWidget::class,
        ];
    }

    public function getSubNavigation(): array
    {
        return ActivityResource::getRecordSubNavigation($this);
    }
}
