<?php

namespace App\Filament\Resources;

use App\Enums\Gender;
use App\Filament\Resources\BankAccountTransactionResource\Pages;
use App\Models\BankAccountTransaction;
use App\Models\User;
use Archilex\AdvancedTables\Filters\AdvancedFilter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\Grid;
use App\Filament\Actions\CopyFamilyUserIdsAction;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use function Amp\Dns\query;

class BankAccountTransactionResource extends Resource
{
    protected static ?string $model = BankAccountTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'الحوالات البنكية';
    protected static ?string $navigationGroup = 'المالية';
    protected static ?string $modelLabel = 'حوالة';
    protected static ?string $pluralModelLabel = 'الحوالات البنكية';


    public static function getParent(): ?string
    {
        return BankAccountTransactionUserResource::class;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('bank_account_id')
                    ->label('الحساب البنكي')
                    ->relationship('bank_account', 'title')
                    ->preload()
                    ->required()
                    ->searchable(),

                Forms\Components\Select::make('user_id')
                    ->label('المستخدم')
                    ->relationship('user', 'name')
                    ->nullable()
                    ->searchable(),

                Forms\Components\TextInput::make('amount')
                    ->label('المبلغ')
                    ->numeric()
                    ->required(),

                Forms\Components\DatePicker::make('due_at')
                    ->label('تاريخ الحوالة')
                    ->nullable(),

                Forms\Components\TextInput::make('reference')
                    ->label('المرجع')
                    ->maxLength(255)
                    ->nullable(),

                Forms\Components\TextInput::make('account')
                    ->label('رقم الحساب')
                    ->maxLength(255)
                    ->nullable(),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('amount')
                    ->label('المبلغ')
                    ->numeric(locale: 'en-US')
                    ->sortable(),

                Tables\Columns\TextColumn::make('bank_account.title')
                    ->label('الحساب البنكي')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('user.shortFullName')
                    ->label('الداعم')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        $userSubquery = User::query()
                            ->where(fn($q) => full_name_filter($q, $search))
                            ->limit(50)
                            ->pluck('id')
                            ->toArray();

                        return $query->whereIn('bank_account_transactions.user_id', $userSubquery);
                    })
                    ->url(fn(BankAccountTransaction $record) => UserResource::getUrl('view', ['record' => $record->user->id]))
                    ->color('primary')
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('account')
                    ->label('رقم حساب الداعم')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\IconColumn::make('is_refund')
                    ->label('عملية استرداد')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_verified')
                    ->label('تم التحقق')
                    ->boolean(),

                Tables\Columns\TextColumn::make('due_at')
                    ->label('تاريخ الحوالة')
                    ->date()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->date()
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                AdvancedFilter::make()->includeColumns([
                    'amount',
                    'bank_account.title',
                    'user.shortFullName',
                    'account',
                    'due_at',
                    'created_at',
                ])->filters([
                    Tables\Filters\TernaryFilter::make('is_verified')->label('تم التحقق'),
                    Tables\Filters\TernaryFilter::make('is_refund')->label('عملية استرداد'),
                    Tables\Filters\TrashedFilter::make()->label('سجل الحذف'),
                ])->defaultFilters([]),
            ])->filtersLayout(FiltersLayout::Modal)
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersTriggerAction(
                fn(\Filament\Tables\Actions\Action $action) => $action
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ViewAction::make()->label('عرض'),
                Tables\Actions\EditAction::make()->label('تعديل'),
                Tables\Actions\DeleteAction::make()->label('حذف'),
                Tables\Actions\RestoreAction::make()->label('استعادة'),
                Tables\Actions\ForceDeleteAction::make()->label('حذف نهائي'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()->label('حذف المحدد'),
                    Tables\Actions\RestoreBulkAction::make()->label('استعادة المحدد'),
                    Tables\Actions\ForceDeleteBulkAction::make()->label('حذف نهائي للمحدد'),
                ]),
                CopyFamilyUserIdsAction::make()->field('user.family_user_id'),

            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make(3)
                    ->schema([
                        // المحتوى الرئيسي (عمودين)
                        Section::make('تفاصيل الحوالة')
                            ->schema([
                                TextEntry::make('reference')
                                    ->label('رقم الحوالة المرجعي')
                                    ->copyable()
                                    ->icon('heroicon-o-tag')
                                    ->weight(FontWeight::Bold),

                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('bank_account.title')
                                            ->label('محولة على حساب')
                                            ->icon('heroicon-o-building-library'),

                                        TextEntry::make('amount')
                                            ->label('المبلغ')
                                            ->numeric(2, locale: 'en-US')
                                            ->icon('heroicon-o-currency-dollar'),
                                    ]),

                                Grid::make(2)
                                    ->schema([

                                        TextEntry::make('account')
                                            ->label('رقم حساب الداعم')
                                            ->placeholder('لا يوجد')
                                            ->copyable()
                                            ->icon('heroicon-o-credit-card'),
                                    ]),

                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('due_at')
                                            ->label('تاريخ الحوالة')
                                            ->date()
                                            ->placeholder('غير محدد')
                                            ->icon('heroicon-o-calendar-days'),

                                    ]),

                                Fieldset::make('خصائص إضافية')
                                    ->schema([

                                        TextEntry::make('is_refund')
                                            ->label('عملية استرداد')
                                            ->badge()
                                            ->formatStateUsing(fn($state) => $state ? 'نعم' : 'لا'),

                                        TextEntry::make('is_verified')
                                            ->label('تم التحقق')
                                            ->badge()
                                            ->formatStateUsing(fn($state) => $state ? 'نعم' : 'لا'),
                                    ]),
                            ])
                            ->columnSpan(2),

                        // الشريط الجانبي (عمود واحد)
                        Grid::make(1)
                            ->schema([
                                Section::make('معلومات إضافية')
                                    ->schema([

                                        TextEntry::make('user.full_name')
                                            ->label('المستخدم')
                                            ->state(fn($record) => ($record->user->full_name . ' #' . $record->user->family_user_id))
                                            ->placeholder('لا يوجد مستخدم مرتبط')
                                            ->url(fn(BankAccountTransaction $record) => $record->user ? UserResource::getUrl('view', ['record' => $record->user->id]) : null),
                                        TextEntry::make('bankAccount.transactions_count')
                                            ->label('عدد الحوالات للحساب')
                                            ->state(fn(BankAccountTransaction $record) => $record->bank_account->transactions()->count())
                                            ->icon('heroicon-o-calculator'),

                                        TextEntry::make('bankAccount.transactions_sum')
                                            ->label('إجمالي المبالغ للحساب')
                                            ->state(fn(BankAccountTransaction $record) => $record->bank_account->transactions()->sum('amount'))
                                            ->numeric(2, locale: 'en-US')
                                            ->color(fn($state) => $state > 0 ? 'success' : ($state < 0 ? 'danger' : 'gray'))
                                            ->icon('heroicon-o-banknotes'),

                                        TextEntry::make('bankAccount.last_transaction')
                                            ->label('آخر حوالة للحساب')
                                            ->state(function (BankAccountTransaction $record) {
                                                $lastTransaction = $record->bank_account->transactions()->latest('due_at')->first();
                                                return $lastTransaction?->due_at;
                                            })
                                            ->date()
                                            ->placeholder('لا توجد حوالات')
                                            ->icon('heroicon-o-clock'),

                                        Fieldset::make('معلومات النظام')
                                            ->schema([
                                                TextEntry::make('created_at')
                                                    ->label('تاريخ الإنشاء')
                                                    ->date()
                                                    ->dateTimeTooltip(),

                                                TextEntry::make('updated_at')
                                                    ->label('تاريخ التحديث')
                                                    ->date()
                                                    ->dateTimeTooltip(),

                                            ]),
                                    ]),
                            ])
                            ->columnSpan(1),
                    ])
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankAccountTransactions::route('/'),
            'create' => Pages\CreateBankAccountTransaction::route('/create'),
            'edit' => Pages\EditBankAccountTransaction::route('/{record}/edit'),
            'view' => Pages\ViewBankAccountTransaction::route('/{record}'),
        ];
    }
}
