<?php

use App\Models\User;
use App\Models\UserOTP;
use Illuminate\Support\Collection;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Auth;
use libphonenumber\PhoneNumberFormat;
use Spatie\Permission\Models\Permission;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Foundation\Auth\Access\Authorizable;

if (!function_exists('user')) {
    /**
     * get auth user.
     *
     * @return User|Authenticatable|null
     */
    function user($guard = null)
    {
        return Auth::user($guard);
    }
}

if (!function_exists('can')) {
    /**
     * @param iterable|string $abilities
     * @param array|mixed $arguments
     * @param null|Authorizable $user
     *
     * @return boolean
     */
    function can($abilities, $arguments = [], Authorizable $user = null)
    {
        if (is_null($user) && !Auth::check())
            return false;
        return (is_null($user) ? Auth::user() : $user)->can($abilities, $arguments);
    }
}

if (!function_exists('canAny')) {
    /**
     * @param iterable|string $abilities
     * @param array|mixed $arguments
     * @param null|Authorizable $user
     *
     * @return boolean
     */
    function canAny($abilities, $arguments = [], Authorizable $user = null)
    {
        if (is_null($user) && !Auth::check())
            return false;
        return (is_null($user) ? Auth::user() : $user)->canAny($abilities, $arguments);
    }
}

if (!function_exists('validate_otp')) {
    /**
     * @param $uuid
     * @param $otp
     *
     * @return false|string
     * @throws \App\Exceptions\OTPExpiredException
     * @throws \App\Exceptions\OTPInvalidException
     */
    function validate_otp($uuid, $otp)
    {
        $userOTP = UserOTP::query()->where('uuid', $uuid)->with('user')->first();
        if (is_null($otp) || "$userOTP->code" !== "$otp")
            throw new \App\Exceptions\OTPInvalidException();
        if ($userOTP->expired_at->isPast())
            throw new \App\Exceptions\OTPExpiredException();
        $userOTP->delete();
        return $userOTP->user;
    }
}

if (!function_exists('valid_phone_format')) {
    /**
     * @param ?string $phone
     * @param string|array $country
     *
     * @return false|string
     */
    function valid_phone_format(?string $phone, $country = null)
    {
        try {
            if (strlen($phone) >= 11 && substr($phone, 0, 3) === '965')
                $phone = "+{$phone}";
            else if (strlen($phone) >= 12 && substr($phone, 0, 1) !== '+')
                $phone = "+{$phone}";
            phone($phone, $country ?? allowed_iso_code(), PhoneNumberFormat::E164);
            return true;
        } catch (Exception $exception) {
            return false;
        }
    }
}

if (!function_exists('permissions_install')) {
    function permissions_install()
    {
        Permission::query()->where('guard_name', 'web')
            ->whereNotIn('name', get_permissions()->pluck('name'))->delete();
        $permissions = Permission::all();
        get_permissions()->each(function ($data) use ($permissions) {
            if ($permissions->filter(fn(Permission $permissions) => $permissions->name == $data['name'] && $permissions->guard_name == $data['guard_name'])->count() === 0)
                Permission::query()->create($data);
        });
        Role::query()->firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'web',
        ], [
            'title' => 'مدير النظام',
        ])->syncPermissions(get_permissions()->pluck('name'));
    }
}

if (!function_exists('get_permissions_classes')) {
    /**
     * get all permissions.
     *
     * @return Collection
     */
    function get_permissions_classes()
    {
        return collect(array_map(fn($file) => ('App\\Permissions\\' . basename($file, '.php')), glob(app_path('Permissions/*.php'))));
    }
}

if (!function_exists('get_permissions_constants')) {
    /**
     * get all permissions.
     *
     * @return Collection
     * @throws ReflectionException
     */
    function get_permissions_constants()
    {
        return get_permissions_classes()
            ->mapWithKeys(fn($class) => [$class => array_keys((new ReflectionClass($class))->getConstants())]);
    }
}

if (!function_exists('get_permission_class_constants')) {
    /**
     * get permission class constants.
     *
     * @param string $class
     *
     * @return array
     * @throws ReflectionException
     */
    function get_permission_class_constants($class)
    {
        return (new ReflectionClass($class))->getConstants();
    }
}

if (!function_exists('get_permissions')) {
    /**
     * get all permissions.
     *
     * @return Collection
     */
    function get_permissions()
    {
        return get_permissions_classes()
            ->map(fn($class) => array_values((new ReflectionClass($class))->getConstants()))
            ->collapse()->map(fn($value) => ['name' => $value, 'guard_name' => 'web']);
    }
}
