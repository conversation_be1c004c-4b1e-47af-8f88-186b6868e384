<?php

use Alkoumi\LaravelHijriDate\Hijri;
use Carbon\Carbon;
use GeoIp2\Database\Reader;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use libphonenumber\PhoneNumberFormat;

if (!function_exists('formatBytes')) {
    /**
     * Format bytes to kb, mb, gb, tb
     *
     * @param integer $size
     * @param integer $precision
     *
     * @return int|string
     */
    function formatBytes(int $size, int $precision = 2): int|string
    {
        if ($size > 0) {
            $base = log($size) / log(1024);
            $suffixes = [' bytes', ' KB', ' MB', ' GB', ' TB'];
            return round(pow(1024, $base - floor($base)), $precision) . $suffixes[floor($base)];
        } else {
            return $size;
        }
    }
}

if (!function_exists('formatMinutes')) {
    /**
     * Format bytes to kb, mb, gb, tb
     *
     * @param int $minutes
     * @param integer $precision
     *
     * @return integer|string
     */
    function formatMinutes(int $minutes, int $precision = 2): int|string
    {
        if ($minutes === 0)
            return 0;
        $hours = floor($minutes / 60);
        $minutes = $minutes % 60;
        $string = [];
        if ($hours > 0)
            $string[] = "$hours " . 'س';
        if ($minutes > 0)
            $string[] = "$minutes " . 'د';
        return implode(' ', $string);
    }
}

if (!function_exists('formatDistance')) {
    /**
     * Format bytes to kb, mb, gb, tb
     *
     * @param int $distance
     * @param integer $precision
     *
     * @return int|string
     */
    function formatDistance(int $distance, int $precision = 2): int|string
    {
        if ($distance > 0) {
            if ($distance > 1000)
                return sprintf("%s ك.م", number_format($distance / 1000, $precision));
            else
                return sprintf("%s م", number_format($distance, $precision));
        } else {
            return $distance;
        }
    }
}

if (!function_exists('hijri_format')) {
    /**
     * @param Carbon $date
     * @param string $format
     *
     * @return string
     */
    function hijri_format(Carbon $date, $format = 'l j F Y')
    {
        if (is_null($date))
            return '';
        try {
            return Hijri::Date($format, $date);
            ///** @var $Arabic Arabic */
            /*$Arabic = app(Arabic::class);
            $correction = $Arabic->dateCorrection($date->unix());
            //$correction = ($correction < -99 || $correction > 99) ? 0 : $correction;
            return $Arabic->date($format, $date->unix(), $correction);*/
        } catch (Exception $exception) {
            return 'خطأ !';
        }
    }
}

if (!function_exists('phone_format')) {
    /**
     * @param ?string $phone
     * @param null $country
     * @param bool $withPlus
     *
     * @return false|string
     */
    function phone_format(?string $phone, $country = null, bool $withPlus = false)
    {
        try {
            if (strlen($phone) >= 11 && substr($phone, 0, 3) === '965')
                $phone = "+{$phone}";
            else if (strlen($phone) >= 12 && substr($phone, 0, 1) !== '+')
                $phone = "+{$phone}";
            return substr(phone($phone, $country ?? allowed_iso_code(), PhoneNumberFormat::E164), $withPlus ? 0 : 1);
        } catch (Exception $exception) {
            /*if (config('app.debug'))
                \Illuminate\Support\Facades\Log::error($exception);*/
            return null;
        }
    }
}

if (!function_exists('clean_unicode')) {
    function clean_unicode($string)
    {
        return preg_replace('/[\x{200B}-\x{200D}\x{FEFF}\x{200E}\x{200F}\x{202A}-\x{202E}\x{2066}-\x{2069}]/u', '', trim($string));
    }
}

if (!function_exists('ip2country')) {
    function ip2country($ip)
    {
        try {
            $reader = new Reader(Storage::disk("central")->path("GeoLite2-Country.mmdb"));
            return $reader->country($ip)->country;
        } catch (Exception $exception) {
            return null;
        }
    }
}

if (!function_exists('riyal_format')) {
    function riyal_format($state)
    {
        return new HtmlString(number_format($state ? $state : 0) . ' <span class="icon-saudi_riyal"></span>');
    }
}

if (!function_exists('parseFloatInternational')) {
    function parseFloatInternational($string, $thousandSep = ',', $decimalSep = '.') {
        // Remove thousand separators
        $string = str_replace($thousandSep, '', $string);
        // Convert decimal separator to standard dot if needed
        if ($decimalSep !== '.') {
            $string = str_replace($decimalSep, '.', $string);
        }
        return floatval($string);
    }
}

