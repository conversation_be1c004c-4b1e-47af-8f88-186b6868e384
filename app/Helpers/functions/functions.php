<?php

use Carbon\Carbon;
use GuzzleHttp\Client;
use App\Models\HelpForm;
use App\Models\ZwajCase;
use App\Models\FamilyCase;
use App\Enums\HelpFormStatus;
use App\Models\DebtDischarge;
use App\Models\SupportService;
use App\Settings\GeneralSettings;
use Illuminate\Support\Facades\URL;
use App\Permissions\UserPermissions;
use Illuminate\Support\Facades\Http;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Relations\Relation;
use App\Http\Integrations\Daftra\Requests\InvoiceGetRequest;

if (!function_exists('audit_changes')) {
    /**
     * @param FamilyCase|ZwajCase|SupportService|DebtDischarge $model
     * @param array $oldValues
     * @param array $newValues
     *
     * @return array
     */
    function audit_changes($model, array $oldValues, array $newValues): array
    {
        return collect($oldValues)->map(function ($value, $key) use ($model, $newValues) {
            $newValue = model_cast_value($model, $key, $newValues[$key]);
            $oldValue = model_cast_value($model, $key, $value);
            return ($newValue !== $oldValue) ? ['old' => $oldValue, 'new' => $newValue] : null;
        })->filter()->toArray();
    }
}

if (!function_exists('model_cast_value')) {
    /**
     * @param FamilyCase|ZwajCase $model
     * @param string $attribute
     * @param $value
     *
     * @return string|null
     */
    function model_cast_value($model, string $attribute, $value): ?string
    {
        if (is_null($value))
            return null;

        if ($model->_isDateAttribute($attribute))
            return $model->_serializeDate($model->_asDateTime($value));

        if ($model->hasCast($attribute)) {
            $cast = $model->getCasts()[$attribute];

            if ($model->_isCustomDateTimeCast($cast)) {
                return $model->_asDateTime($value)->format(explode(':', $cast, 2)[1]);
            }

            return $model->_castAttribute($attribute, $value);
        }
        return $value;
    }
}

if (!function_exists('taqnyat_balance')) {
    /**
     * @return array
     * @throws GuzzleException
     */
    function taqnyat_balance(): array
    {
        $client = new Client();
        $res = $client->get('https://api.taqnyat.sa/account/balance', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Bearer ' . app(\App\Settings\ServicesSettings::class)->taqnyatKey,
            ],
        ]);
        return json_decode((string)$res->getBody(), true);
    }
}

if (!function_exists('sa_phone_validate_regex')) {
    /**
     * Get phone validation regex
     *
     * @return string
     */
    function sa_phone_validate_regex(): string
    {
        // 0,3,5 -> STC | 4,6 -> Mobily | 8,9 -> Zain | 1 -> Bravo
        // 0570,0571,0572 -> Virgin | 0576,0577,0578 -> Lebara
        return '/^(009665|09665|9665|\+9665|05|5)(0|3|5|6|4|9|8|7|1)([0-9]{7})$/';
    }
}

if (!function_exists('sa_phone_international')) {
    /**
     * Get phone validation regex
     *
     * @param $phone
     *
     * @return string|null
     */
    function sa_phone_international($phone): ?string
    {
        if (empty($phone))
            return null;
        if (!is_sa_phone($phone))
            return $phone;
        return preg_replace("/^(009665|09665|9665|\+9665|05|5)/", '9665', $phone);
    }
}

if (!function_exists('sa_phone_locally')) {
    /**
     * Get phone validation regex
     *
     * @param $phone
     *
     * @return string|null
     */
    function sa_phone_locally($phone): ?string
    {
        if (empty($phone))
            return null;
        if (!is_sa_phone($phone))
            return $phone;
        return preg_replace("/^(009665|09665|9665|\+9665|05|5)/", '05', $phone);
    }
}

if (!function_exists('signedRoute')) {
    /**
     * get auth user.
     *
     * @param string $name
     * @param mixed $parameters
     * @param DateTimeInterface|DateInterval|int|null $expiration
     * @param bool $absolute
     *
     * @return string
     */
    function signedRoute(string $name, $parameters = [], $expiration = null, bool $absolute = true)
    {
        return URL::signedRoute($name, $parameters, $expiration, $absolute);
    }
}

if (!function_exists('temporarySignedRoute')) {
    /**
     * get auth user.
     *
     * @param string $name
     * @param array $parameters
     * @param DateTimeInterface|DateInterval|int $expiration
     * @param bool $absolute
     *
     * @return string
     */
    function temporarySignedRoute(string $name, $expiration, array $parameters = [], bool $absolute = true)
    {
        return URL::temporarySignedRoute($name, $expiration, $parameters, $absolute);
    }
}

if (!function_exists('user_location')) {
    /**
     * get user location
     */
    function user_location()
    {
        return geoip(request()->ip());
    }
}

if (!function_exists('user_iso_code')) {
    /**
     * get user location
     */
    function user_iso_code()
    {
        $default_is_code = env('DEFAULT_ISO_CODE', 'SA');
        return optional(user_location())->iso_code ?: $default_is_code;
    }
}

if (!function_exists('guess_iso_code')) {
    /**
     * get user location
     */
    function guess_iso_code($phone)
    {
        /*$lib = PhoneNumberUtil::getInstance();
        try {
            $phone;
        } catch (\Exception $exception) {

        }*/
    }
}

if (!function_exists('allowed_iso_code')) {
    /**
     * get user location
     */
    function allowed_iso_code()
    {
        return ['SA', 'EG', 'KW', 'AE', 'IQ',];
    }
}

if (!function_exists('resolve_model')) {
    /**
     * @param string $type
     * @param $id
     *
     * @return false|string
     */
    function resolve_model(string $type, $id)
    {
        if (empty($type))
            return null;
        $type = Relation::getMorphedModel($type) ?? $type;
        if (!class_exists($type))
            return null;
        return resolve($type)->where('id', $id)->first();
    }
}

if (!function_exists('allowed_countries')) {
    /**
     * @return array
     */
    function allowed_countries($validation = false)
    {
        return array_filter(optional(\user())->hasPermissionTo(UserPermissions::internationalNumbers) ?
            [
                ($validation ? null : 'SA'),
                'AUTO',
            ] : ['SA']
        );
    }
}

if (!function_exists('age')) {
    /**
     * @return string
     */
    function age(?Carbon $dob)
    {
        if (is_null($dob))
            return null;
        $now = Carbon::now();
        $age = [];
        $diff = $now->diff($dob);
        if ($diff->y > 0)
            $age[] = implode(' ', [$diff->y, 'سنة']);
        if ($diff->m > 0)
            $age[] = implode(' ', [$diff->m, 'شهر']);
        if ($diff->d > 0 && $diff->y === 0 && $diff->m === 0)
            $age[] = implode(' ', [$diff->d, 'يوم']);
        return implode(', ', $age);
    }
}

if (!function_exists('raw_sql')) {
    /**
     * @return string
     */
    function raw_sql($query)
    {
        return sprintf('(%s)', vsprintf(str_replace(['?'], ['\'%s\''], $query->toSql()), $query->getBindings()));
    }
}

if (!function_exists('path_url')) {
    /**
     * @return string
     */
    function path_url($url)
    {
        return tenant_asset("{$url}");
    }
}

if (!function_exists('thumb_url')) {
    /**
     * @return string
     */
    function thumb_url($url)
    {
        return tenant_asset("{$url}");
    }
}

if (!function_exists('all_health_status')) {
    /**
     * @return array
     */
    function all_health_status()
    {
        return ['سليم', 'مريض بمرض مزمن', 'معاق'];
    }
}

if (!function_exists('all_educational_status')) {
    /**
     * @return array
     */
    function all_educational_status()
    {
        return ['طفل', 'ابتدائي', 'متوسط', 'دبلوم', 'ثانوي', 'جامعي', 'بكالوريوس', 'دكتوراه', 'ماجستير'];
    }
}

if (!function_exists('create_daftra_product')) {
    /**
     * check product publish for daftra or not
     *
     * @throws Exception
     */
    function create_daftra_product(\App\Models\Product $product)
    {
        if (!empty(config('daftra.api-path')) && is_null($product->daftra_id)) {
            $request = new \App\Http\Integrations\Daftra\Requests\ProductStoreRequest();
            $request->body()->merge([
                'Product' => [
                    'type' => 1,
                    'unit_price' => 1,
                    'track_stock' => 1,
                    'name' => $product->title,
                    'description' => $product->content,
                    'initial_stock_level' => $product->amount,
                ],
            ]);
            $json = $request->send()->json();
            if ($json['result'] === 'successful')
                $product->update(['daftra_id' => $json['id']]);
            else
                Log::error('Daftra Product Not Created', $json);
        }
    }
}

if (!function_exists('create_daftra_client')) {
    /**
     * check product publish for daftra or not
     */
    function create_daftra_client(\App\Models\User $user)
    {
        if (!empty(config('daftra.api-path')) && is_null($user->daftra_id)) {
            $connector = new \App\Http\Integrations\Daftra\APIConnector();
            $paginator = $connector->paginate(new \App\Http\Integrations\Daftra\Requests\ClientListRequest([
                'field_1' => $user->family_user_id,
            ]));
            $names = explode(' ', trim($user->full_name));
            $firstName = array_slice($names, 0, 1)[0];
            $lastName = (count($names) > 1) ? implode(' ', array_slice($names, 1)) : null;
            foreach ($paginator->items() as $client) {
                $_client = Arr::first($client);
                //"{$_client['CustomModel']['field_1']}" === "$user->family_user_id"
                if ($_client) {
                    $user->update(['daftra_id' => $_client['id']]);
                    if (empty($_client['first_name']))
                        $_client['first_name'] = $firstName;
                    if (empty($_client['last_name']))
                        $_client['last_name'] = $lastName;
                    if (empty($_client['gender']))
                        $_client['gender'] = $user->gender === \App\Enums\Gender::Male ? 1 : 2;
                    if (empty($_client['phone1']))
                        $_client['phone1'] = is_null($user->phone) ? null : phone_format($user->phone, ['SA'], true);
                    if (
                        empty($_client['first_name']) ||
                        empty($_client['last_name']) ||
                        empty($_client['gender']) ||
                        empty($_client['phone1'])
                    ) {
                        $request = new \App\Http\Integrations\Daftra\Requests\ClientUpdateRequest($_client['id']);
                        $request->body()->merge([
                            'Client' => Arr::except($_client, ['CustomModel']),
                            'CustomModel' => $_client['CustomModel'],
                        ]);
                        $request->send()->json();
                    }
                    return;
                }
            }
            $request = new \App\Http\Integrations\Daftra\Requests\ClientStoreRequest();
            $request->body()->merge([
                'Client' => [
                    'is_offline' => 1,
                    'type' => 2,
                    'business_name' => $user->full_name,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'phone1' => is_null($user->phone) ? null : phone_format($user->phone, ['SA'], true),
                    'gender' => $user->gender === \App\Enums\Gender::Male ? 1 : 2,
                ],
                'CustomModel' => [
                    'field_1' => [$user->family_user_id],
                ],
            ]);
            $json = $request->send()->json();
            if ($json['result'] === 'successful')
                $user->update(['daftra_id' => $json['id']]);
            else
                Log::error('Daftra Client Not Created', $json);
        }
    }
}

if (!function_exists('create_daftra_transaction_client')) {
    /**
     * check product publish for daftra or not
     *
     * @throws Exception
     */
    function create_daftra_transaction_client(\App\Models\ProductTransaction $transaction)
    {
        if (!empty(config('daftra.api-path')) && is_null($transaction->user)) {
            $names = explode(' ', trim($transaction->user_name));
            $firstName = array_slice($names, 0, 1)[0];
            $lastName = (count($names) > 1) ? implode(' ', array_slice($names, 1)) : null;
            $request = new \App\Http\Integrations\Daftra\Requests\ClientStoreRequest();
            $request->body()->merge([
                'Client' => [
                    'is_offline' => 1,
                    'type' => 2,
                    'business_name' => $transaction->user_name,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'phone1' => is_null($transaction->user_phone) ? null : phone_format($transaction->user_phone, ['SA'], true),
                    'gender' => 1,
                ],
                'CustomModel' => [
                    'field_1' => [null],
                ],
            ]);
            $json = $request->send()->json();
            if ($json['result'] === 'successful')
                $transaction->update(['user_daftra_id' => $json['id']]);
            else
                Log::error('Daftra Client Not Created', $json);
        } else
            create_daftra_client($transaction->user);
    }
}

if (!function_exists('fetch_daftra_pdf')) {
    /**
     * check product publish for daftra or not
     */
    function fetch_daftra_pdf(\App\Models\ProductTransaction &$transaction)
    {
        if (!empty(config('daftra.api-path')) && !is_null($transaction->daftra_id) && is_null($transaction->pdf_path)) {
            try {
                $data = (new InvoiceGetRequest($transaction->daftra_id))->send()->json();
                if (@$data['result'] === 'successful') {
                    $html_url = $data['data']['Invoice']['invoice_html_url'];
                    $pdf_url = $data['data']['Invoice']['invoice_pdf_url'];
                    $client = new Client([
                        'cookies' => true,
                    ]);
                    if (!Str::startsWith($pdf_url, 'https:///') || !empty(config('daftra.base-path'))) {
                        if (Str::startsWith($pdf_url, 'https:///'))
                            $pdf_url = Str::replaceFirst('https:///', config('daftra.base-path'), $pdf_url);
                        $client->get($pdf_url);
                        $pdfRequest = $client->get(Str::replace('invoices', 'client/invoices', $pdf_url));
                        if ($pdfRequest->getHeader('Content-Type')[0] === 'application/octet-stream') {
                            $filePath = "transactions/pdf/{$transaction->uuid}.pdf";
                            Storage::put("public/$filePath", (string)$pdfRequest->getBody());
                            $transaction->update(['pdf_path' => $filePath]);
                        }
                    }
                }
            } catch (Exception|GuzzleException $exception) {
                \Log::error($exception);
            }
        }
    }
}

if (!function_exists('nested_parents')) {
    /**
     * check product publish for daftra or not
     */
    function nested_parents(\App\Models\User $user)
    {
        $parents = [];
        if ($user->father) {
            $parents[] = $user->father;
            $parents = array_merge($parents, nested_parents($user->father));
        }
        return $parents;
    }
}

if (!function_exists('nested_parents_id')) {
    /**
     * check product publish for daftra or not
     */
    function nested_parents_id(\App\Models\User $user)
    {
        $parents = [];
        if ($user->father) {
            $parents[] = $user->father_id;
            $parents = array_merge($parents, nested_parents_id($user->father));
        }
        return array_values($parents);
    }
}

if (!function_exists('create_daftra_transaction_invoice')) {
    /**
     * check product publish for daftra or not
     */
    function create_daftra_transaction_invoice(\App\Models\ProductTransaction $transaction, $payment_method)
    {
        if (empty(config('daftra.api-path')))
            return;
        $settings = app(\App\Settings\FeatureSettings::class);
        $isDonation = in_array($transaction->product->type, [\App\Enums\ProductType::Case, \App\Enums\ProductType::Donation]);
        if (!is_null($transaction->daftra_id))
            return;
        if (is_null($transaction->product->daftra_id))
            create_daftra_product($transaction->product);
        if (!is_null($transaction->user) && is_null($transaction->user->daftra_id))
            create_daftra_client($transaction->user);
        $request = new \App\Http\Integrations\Daftra\Requests\InvoiceStoreRequest();
        $names = explode(' ', trim(is_null($transaction->user) ? $transaction->user_name : $transaction->user->full_name));
        $firstName = array_slice($names, 0, 1)[0];
        $lastName = (count($names) > 1) ? implode(' ', array_slice($names, 1)) : null;
        $request->body()->merge([
            'Invoice' => [
                'is_offline' => true,
                'client_id' => is_null($transaction->user) ? $transaction->user_daftra_id : $transaction->user->daftra_id,
                'client_business_name' => trim(is_null($transaction->user) ? $transaction->user_name : $transaction->user->full_name),
                'client_first_name' => $firstName,
                'client_last_name' => $lastName,
                'date' => $transaction->confirmed_at->toDateString(),
                'currency_code' => 'SAR',
                'payment_status' => 2,
            ],
            'InvoiceItem' => [
                [
                    'product_id' => $transaction->product->daftra_id,
                    'quantity' => $isDonation ? $transaction->amount : 1,
                    'unit_price' => $isDonation ? 1 : $transaction->amount,
                ],
            ],
            'Payment' => [
                [
                    'is_paid' => 1,
                    'amount' => $transaction->amount,
                    'transaction_id' => "#INV-{$transaction->invoice_id}",
                    'payment_method' => \Illuminate\Support\Str::replace([' ', '/'], '_', $payment_method),
                    'treasury_id' => $settings->daftraTreasuryId,
                ],
            ],
        ]);
        $json = $request->send()->json();
        if ($json['result'] === 'successful')
            $transaction->update(['daftra_id' => $json['id']]);
        else
            Log::error('Daftra Transaction Not Created', $json);
    }
}

if (!function_exists('get_domain')) {
    /**
     * @param string|null $url
     *
     * @return string
     */
    function get_domain(string|null $url)
    {
        $parsedUrl = parse_url($url);
        if (!isset($parsedUrl['host']))
            return null;
        $ip = $parsedUrl['host'];
        $port = (string)@$parsedUrl['port'];
        return ($ip . ($port ? (':' . $port) : ''));
    }
}

if (!function_exists('tz')) {
    /**
     * @param string|Carbon|null $date
     *
     * @return Carbon|null
     */
    function tz(string|Carbon|null $date)
    {
        try {
            if (is_string($date) && !empty($date))
                $date = Carbon::create($date);
        } catch (\Exception) {
        }
        if ($date instanceof Carbon)
            return $date->timezone(app("timezone"));
        return null;
    }
}

if (!function_exists('point2point_distance')) {
    /**
     * @param $lat1
     * @param $lon1
     * @param $lat2
     * @param $lon2
     * @param string $unit
     *
     * @return float
     */
    function point2point_distance($lat1, $lon1, $lat2, $lon2, string $unit = 'K')
    {
        $theta = $lon1 - $lon2;
        $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $dist = acos($dist);
        $dist = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;
        $unit = strtoupper($unit);

        if ($unit == "K")
            return ($miles * 1.609344);
        else if ($unit == "N")
            return ($miles * 0.8684);
        else
            return $miles;
    }
}

if (!function_exists('partykit_notify')) {
    /**
     * @param string $roomId
     * @param array $data
     */
    function partykit_notify(string $roomId, array $data)
    {
        try {
            Http::post(trim(env('PARTYKIT_HOST', 'https://s.altwijry.com'), '/') . "/party/{$roomId}", $data);
        } catch (\Exception $e) {
            \Log::error($e);
        }
    }
}

if (!function_exists('help_form_limit_allowed')) {
    /**
     * @return boolean
     */
    function help_form_limit_allowed(\App\Models\HelpFormTemplate $template, $user_id, $filterCompleted = false, $currentFormUUID = null)
    {
        $limited = false;
        if ($template->limit) {
            if ($template->limit_period === 'LIMIT') {
                if ($template->limit_count > 0)
                    $limited = $limited || HelpForm::query()
                            ->when($filterCompleted, fn($q) => $q->whereNotNull('completed_at'))
                            ->when($currentFormUUID, fn($q) => $q->where('uuid', '<>', $currentFormUUID))
                            ->where([
                                //'status' => HelpFormStatus::Approved,
                                'user_id' => $user_id,
                                'help_form_template_id' => $template->id,
                            ])->count() >= $template->limit_count;
            } else if ($template->limit_period === 'ANNUAL') {
                if ($template->limit_period_type === 'HIJRI') {
                    $year = \GeniusTS\HijriDate\Hijri::convertToHijri(now()->toDateString())->yearIso;
                    $hijriDate = \GeniusTS\HijriDate\Converter::hijriToJulian($year, 1, 1);
                    $nextHijriDate = \GeniusTS\HijriDate\Converter::hijriToJulian($year + 1, 1, 1);
                    $gregorianDate = \Carbon\Carbon::create(...array_values((array)\GeniusTS\HijriDate\Converter::julianToGregorian($hijriDate)));
                    $nextGregorianDate = \Carbon\Carbon::create(...array_values((array)\GeniusTS\HijriDate\Converter::julianToGregorian($nextHijriDate)))->subDay();

                    if ($template->limit_helping_value <= 0 || empty($template->limit_helping_value))
                        $limited = $limited || HelpForm::query()
                                ->when($filterCompleted, fn($q) => $q->whereNotNull('completed_at'))
                                ->when($currentFormUUID, fn($q) => $q->where('uuid', '<>', $currentFormUUID))
                                ->where([
                                    //'status' => HelpFormStatus::Approved,
                                    'user_id' => $user_id,
                                    'help_form_template_id' => $template->id,
                                ])->whereDateBetween('created_at', $gregorianDate, $nextGregorianDate)
                                ->count() >= ($template->limit_count ?: 1);
                } else if ($template->limit_period_type === 'GREGORIAN') {
                    if ($template->limit_helping_value <= 0 || empty($template->limit_helping_value))
                        $limited = $limited || HelpForm::query()
                                ->when($filterCompleted, fn($q) => $q->whereNotNull('completed_at'))
                                ->when($currentFormUUID, fn($q) => $q->where('uuid', '<>', $currentFormUUID))
                                ->where([
                                    //'status' => HelpFormStatus::Approved,
                                    'user_id' => $user_id,
                                    'help_form_template_id' => $template->id,
                                ])->whereDateBetween('created_at', now()->firstOfYear(), now()->endOfYear())
                                ->count() >= ($template->limit_count ?: 1);
                }
            }
        }
        return $limited;
    }
}

if (!function_exists('help_form_supporting_value_limit_allowed')) {
    /**
     * @return boolean
     */
    function help_form_supporting_value_limit_allowed(\App\Models\HelpFormTemplate $template, $user_id, $filterCompleted = false, $currentFormUUID = null)
    {
        $overAllowedSupport = false;
        if ($template->limit) {
            if ($template->limit_period === 'LIMIT') {
                if ($template->limit_helping_value > 0)
                    $overAllowedSupport = $overAllowedSupport || HelpForm::query()
                            ->when($filterCompleted, fn($q) => $q->whereNotNull('completed_at'))
                            ->when($currentFormUUID, fn($q) => $q->where('uuid', '<>', $currentFormUUID))
                            ->where([
                                'status' => HelpFormStatus::Approved,
                                'user_id' => $user_id,
                                'help_form_template_id' => $template->id,
                            ])->sum('helping_value') >= $template->limit_helping_value;
            } else if ($template->limit_period === 'ANNUAL') {
                if ($template->limit_period_type === 'HIJRI') {
                    $year = \GeniusTS\HijriDate\Hijri::convertToHijri(now()->toDateString())->yearIso;
                    $hijriDate = \GeniusTS\HijriDate\Converter::hijriToJulian($year, 1, 1);
                    $nextHijriDate = \GeniusTS\HijriDate\Converter::hijriToJulian($year + 1, 1, 1);
                    $gregorianDate = \Carbon\Carbon::create(...array_values((array)\GeniusTS\HijriDate\Converter::julianToGregorian($hijriDate)));
                    $nextGregorianDate = \Carbon\Carbon::create(...array_values((array)\GeniusTS\HijriDate\Converter::julianToGregorian($nextHijriDate)))->subDay();

                    if ($template->limit_helping_value > 0)
                        $overAllowedSupport = $overAllowedSupport || HelpForm::query()
                                ->when($filterCompleted, fn($q) => $q->whereNotNull('completed_at'))
                                ->when($currentFormUUID, fn($q) => $q->where('uuid', '<>', $currentFormUUID))
                                ->where([
                                    'status' => HelpFormStatus::Approved,
                                    'user_id' => $user_id,
                                    'help_form_template_id' => $template->id,
                                ])->whereDateBetween('created_at', $gregorianDate, $nextGregorianDate)
                                ->sum('helping_value') >= $template->limit_helping_value;
                } else if ($template->limit_period_type === 'GREGORIAN') {
                    if ($template->limit_helping_value > 0)
                        $overAllowedSupport = $overAllowedSupport || HelpForm::query()
                                ->when($filterCompleted, fn($q) => $q->whereNotNull('completed_at'))
                                ->when($currentFormUUID, fn($q) => $q->where('uuid', '<>', $currentFormUUID))
                                ->where([
                                    'status' => HelpFormStatus::Approved,
                                    'user_id' => $user_id,
                                    'help_form_template_id' => $template->id,
                                ])->whereDateBetween('created_at', now()->firstOfYear(), now()->endOfYear())
                                ->sum('helping_value') >= $template->limit_helping_value;
                }
            }
        }
        return $overAllowedSupport;
    }
}

if (!function_exists('is_waha_working')) {
    /**
     * @return boolean
     */
    function is_waha_working()
    {
        $appSettings = app(\App\Settings\AppSettings::class);
        return optional($appSettings->wahaSender?->metadata)['status'] === 'WORKING';
    }
}

if (!function_exists('is_demo')) {
    /**
     * @return boolean
     */
    function is_demo()
    {
        return app(GeneralSettings::class)->isDemo;
    }
}
