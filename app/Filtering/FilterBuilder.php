<?php

namespace App\Filtering;

use DateTime;
use App\Filtering\Support\Column;
use Illuminate\Database\Eloquent\Model;
use App\Filtering\Enums\FilteringFilterType;
use Illuminate\Support\Traits\ForwardsCalls;
use App\Filtering\Enums\FilteringTextFilterType;
use App\Filtering\Enums\FilteringDateFilterType;
use App\Filtering\Requests\FilteringQueryRequest;
use App\Filtering\Enums\FilteringNumberFilterType;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;

/**
 * @mixin EloquentBuilder
 */
class FilterBuilder
{
    use ForwardsCalls;

    protected array $params;
    protected array $filters = [];

    protected EloquentBuilder|Relation $subject;


    /**
     * @param EloquentBuilder|Relation|Model|class-string<Model> $subject
     */
    public function __construct(array $params, EloquentBuilder|Relation|Model|string $subject)
    {
        if (is_a($subject, Model::class, true)) {
            $subject = $subject::query();
        }

        $this->params = $params;
        $this->subject = $subject;
    }

    /**
     * Returns a new FilterBuilder for an FilteringQueryRequest.
     *
     * @param EloquentBuilder|Relation|Model|class-string<Model> $subject
     */
    public static function forRequest(FilteringQueryRequest $request, EloquentBuilder|Relation|Model|string $subject): FilterBuilder
    {
        return new FilterBuilder($request->validated(), $subject);
    }

    /**
     * Returns a new FilterBuilder for a selection.
     *
     * @param EloquentBuilder|Relation|Model|class-string<Model> $subject
     */
    public static function forSelection(array $selection, EloquentBuilder|Relation|Model|string $subject): FilterBuilder
    {
        return new FilterBuilder($selection, $subject);
    }

    public function getSubject(): Relation|EloquentBuilder
    {
        return $this->subject;
    }

    /**
     * Add custom filter handler for the give column.
     *
     * @param string $column
     *
     * @return $this
     */
    public function filterColumn($column, callable $callback): static
    {
        $this->filters[$column] = ['method' => $callback];

        return $this;
    }

    public function __call($name, $arguments)
    {
        $result = $this->forwardCallTo($this->subject, $name, $arguments);

        /*
         * If the forwarded method call is part of a chain we can return $this
         * instead of the actual $result to keep the chain going.
         */
        if ($result === $this->subject) {
            return $this;
        }

        return $result;
    }

    public function addFiltersToQuery(): self
    {
        if (!isset($this->params['filterModel'])) {
            return $this;
        }

        $this->subject->where(function ($q) {
            $this->addConditionsGroup($q, $this->params['filterModel'], 'and');
        });

        return $this;
    }

    protected function addConditionsGroup(EloquentBuilder|Relation $subject, array $rootFilterGroup, $operator = null)
    {
        if (empty($rootFilterGroup) || empty($rootFilterGroup['conditions']) || empty($rootFilterGroup['operator']))
            return;
        $index = 0;
        foreach ($rootFilterGroup['conditions'] as $condition) {
            if (isset($condition['field'])) {
                $column = Column::fromColId($this->subject, $condition['field']);

                if ($column->hasRelations()) {
                    $subject->whereHas($column->getDottedRelation(), function (EloquentBuilder $builder) use ($column, $condition, $rootFilterGroup, $index) {
                        $this->addFilterToQuery($builder, $column, $condition, $index === 0 ? ($operator ?? $rootFilterGroup['operator']) : $rootFilterGroup['operator']);
                    });
                } else {
                    $this->addFilterToQuery($subject, $column, $condition, $index === 0 ? ($operator ?? $rootFilterGroup['operator']) : $rootFilterGroup['operator']);
                }
            } else if (isset($condition['conditions'])) {
                $subject->where(fn($q) => $this->addConditionsGroup($q, $condition, ($operator ?? $rootFilterGroup['operator'])));
            }
            $index++;
        }
    }

    public function addFilterToQuery(EloquentBuilder|Relation $subject, Column $column, array $filter, $operator = 'and'): void
    {
        if (isset($filter['operator']) && isset($filter['conditions'])) {
            $subject->where(function (EloquentBuilder|Relation $query) use ($column, $filter) {
                foreach ($filter['conditions'] as $condition) {
                    $this->addFilterToQuery($query, $column, $condition, strtolower($filter['operator']));
                }
            });
        } else {
            $columnName = $column->getName();
            if ($this->hasFilterColumn($columnName)) {
                $query = $this->getBaseQueryBuilder($subject);
                $callback = $this->filters[$columnName]['method'];

                if ($this->subject instanceof EloquentBuilder)
                    $builder = $this->subject->newModelInstance()->newQuery();
                else
                    $builder = $this->subject->newQuery();

                $callback($this, $builder, $filter, $operator);

                /** @var QueryBuilder $baseQueryBuilder */
                $baseQueryBuilder = $this->getBaseQueryBuilder($builder);
                $query->addNestedWhereQuery($baseQueryBuilder, $operator);
            } else {
                $filterType = FilteringFilterType::from($filter['filterType']);
                match ($filterType) {
                    FilteringFilterType::Set => $this->addSetFilterToQuery($subject, $column, $filter, $operator),
                    FilteringFilterType::Text => $this->addTextFilterToQuery($subject, $column, $filter, $operator),
                    FilteringFilterType::Number => $this->addNumberFilterToQuery($subject, $column, $filter, $operator),
                    FilteringFilterType::Date => $this->addDateFilterToQuery($subject, $column, $filter, $operator),
                };
            }
        }
    }

    /**
     * Check if column has custom filter handler.
     */
    public function hasFilterColumn(string $columnName): bool
    {
        return isset($this->filters[$columnName]);
    }

    /**
     * Get the base query builder instance.
     *
     * @param QueryBuilder|EloquentBuilder|null $instance
     */
    protected function getBaseQueryBuilder($instance = null): QueryBuilder
    {
        if (!$instance) {
            $instance = $this->subject;
        }

        if ($instance instanceof EloquentBuilder) {
            return $instance->getQuery();
        }

        return $instance;
    }

    public function getQuery(): QueryBuilder
    {
        if ($this->subject instanceof EloquentBuilder) {
            return $this->subject->getQuery();
        }

        return $this->subject->getBaseQuery();
    }

    protected function addSetFilterToQuery(EloquentBuilder|Relation $subject, Column $column, array $filter, $operator = 'and'): void
    {
        // todo handle $operator
        $isJsonColumn = $column->isJsonColumn();
        $columnName = $column->getNameAsJsonPath();
        $values = $filter['values'];
        $all = $filter['all'] ?? false;
        $filteredValues = array_filter($values, fn($value) => !empty($value));

        if (!empty($filteredValues))
            $subject->where(function (EloquentBuilder $query) use ($column, $all, $columnName, $values, $filteredValues, $isJsonColumn) {
                if (count($filteredValues) !== count($values)) {
                    // there was a null in there
                    $query->whereNull($columnName);
                }

                if ($isJsonColumn) {
                    // TODO: this does not work at the moment because laravel has no support for the ?& and ?| operators
                    // TODO: find a workaround!
                    $query->orWhere(
                        $column->getNameAsJsonAccessor(),
                        $all ? '?&' : '?|', '{' . implode(',', $filteredValues) . '}',
                    );
                } else {
                    $query->orWhereIn($columnName, $filteredValues);
                }
            });
    }

    protected function addTextFilterToQuery(EloquentBuilder|Relation $subject, Column $column, array $filter, $operator = 'and'): void
    {
        $columnName = $column->getNameAsJsonPath();
        $value = $filter['filter'] ?? null;
        $type = FilteringTextFilterType::from($filter['type']);

        match ($type) {
            FilteringTextFilterType::Equals => $subject->where($columnName, '=', $value, boolean: $operator),
            FilteringTextFilterType::NotEqual => $subject->where($columnName, '!=', $value, boolean: $operator),
            FilteringTextFilterType::Contains => $subject->where($columnName, 'ilike', '%' . $value . '%', boolean: $operator),
            FilteringTextFilterType::NotContains => $subject->where($columnName, 'not ilike', '%' . $value . '%', boolean: $operator),
            FilteringTextFilterType::StartsWith => $subject->where($columnName, 'ilike', $value . '%', boolean: $operator),
            FilteringTextFilterType::EndsWith => $subject->where($columnName, 'ilike', '%' . $value, boolean: $operator),
            FilteringTextFilterType::Blank => $subject->whereNull($columnName, boolean: $operator),
            FilteringTextFilterType::NotBlank => $subject->whereNotNull($columnName, boolean: $operator),
        };
    }

    protected function addNumberFilterToQuery(EloquentBuilder|Relation $subject, Column $column, array $filter, $operator = 'and'): void
    {
        $columnName = $column->getNameAsJsonPath();
        $value = $filter['filter'] ?? null;
        $type = FilteringNumberFilterType::from($filter['type']);

        match ($type) {
            FilteringNumberFilterType::Equals => $subject->where($columnName, '=', $value, boolean: $operator),
            FilteringNumberFilterType::NotEqual => $subject->where($columnName, '!=', $value, boolean: $operator),
            FilteringNumberFilterType::GreaterThan => $subject->where($columnName, '>', $value, boolean: $operator),
            FilteringNumberFilterType::GreaterThanOrEqual => $subject->where($columnName, '>=', $value, boolean: $operator),
            FilteringNumberFilterType::LessThan => $subject->where($columnName, '<', $value, boolean: $operator),
            FilteringNumberFilterType::LessThanOrEqual => $subject->where($columnName, '<=', $value, boolean: $operator),
            FilteringNumberFilterType::InRange => $subject->where(fn($q) => $q->where($columnName, '>=', $value)->where($columnName, '<=', $filter['filterTo']), boolean: $operator),
            FilteringNumberFilterType::Blank => $subject->whereNull($columnName, boolean: $operator),
            FilteringNumberFilterType::NotBlank => $subject->whereNotNull($columnName, boolean: $operator),
        };
    }

    protected function addDateFilterToQuery(EloquentBuilder|Relation $subject, Column $column, array $filter, $operator = 'and'): void
    {
        $columnName = $column->getNameAsJsonPath();
        $dateFrom = isset($filter['dateFrom']) ? new DateTime($filter['dateFrom']) : null;
        $dateTo = isset($filter['dateTo']) ? new DateTime($filter['dateTo']) : null;

        if (is_null($dateFrom) && is_null($dateTo) && !empty($filter['filter'])) {
            [$dateFrom, $dateTo] = $filter['filter'];;
        }

        match (FilteringDateFilterType::from($filter['type'])) {
            FilteringDateFilterType::Equals => $subject->whereDate($columnName, '=', $dateFrom, boolean: $operator),
            FilteringDateFilterType::NotEqual => $subject->whereDate($columnName, '!=', $dateFrom, boolean: $operator),
            FilteringDateFilterType::GreaterThan => $subject->whereDate($columnName, '>=', $dateFrom, boolean: $operator),
            FilteringDateFilterType::LessThan => $subject->whereDate($columnName, '<=', $dateFrom, boolean: $operator),
            FilteringDateFilterType::InRange => $subject->where(fn($q) => $q->whereDate($columnName, '>=', $dateFrom)->whereDate($columnName, '<=', $dateTo), boolean: $operator),
            FilteringDateFilterType::Blank => $subject->whereNull($columnName, boolean: $operator),
            FilteringDateFilterType::NotBlank => $subject->whereNotNull($columnName, boolean: $operator),
        };
    }
}
