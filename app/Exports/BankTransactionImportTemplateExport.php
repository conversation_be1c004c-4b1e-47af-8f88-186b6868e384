<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class BankTransactionImportTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    /**
     * Return the array data for the template
     *
     * @return array
     */
    public function array(): array
    {
        return [
            // Sample data rows
            [
                '1000.00',      // Balance
                'CR',           // Transaction Type
                '500.00',       // Amount
                '',             // Empty column
                'تحويل من حساب رقم ********** - محمد أحمد علي', // Notes
                '',             // Empty column
                '14:30:00',     // Time
                '',             // Empty column
                '2024-01-15',   // Date
            ],
            [
                '1500.00',      // Balance
                'CR',           // Transaction Type
                '750.00',       // Amount
                '',             // Empty column
                'إيداع نقدي - فاطمة سعد محمد - رقم الحساب 9876543210', // Notes
                '',             // Empty column
                '09:15:00',     // Time
                '',             // Empty column
                '2024-01-16',   // Date
            ],
            [
                '2250.00',      // Balance
                'CR',           // Transaction Type
                '300.00',       // Amount
                '',             // Empty column
                'تحويل إلكتروني من 5555666677 - أحمد محمد سالم', // Notes
                '',             // Empty column
                '16:45:00',     // Time
                '',             // Empty column
                '2024-01-17',   // Date
            ],
        ];
    }

    /**
     * Return the headings for the template
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'الرصيد',           // Balance
            'نوع العملية',      // Transaction Type
            'المبلغ',           // Amount
            '',                // Empty column
            'البيان',           // Notes/Description
            '',                // Empty column
            'الوقت',           // Time
            '',                // Empty column
            'التاريخ',          // Date
        ];
    }

    /**
     * Apply styles to the worksheet
     *
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE6E6FA',
                    ],
                ],
            ],
            // Style data rows
            'A2:I100' => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            // Style the notes column (E) for right alignment (Arabic text)
            'E:E' => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_RIGHT,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }

    /**
     * Set column widths
     *
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 15,  // Balance
            'B' => 12,  // Transaction Type
            'C' => 15,  // Amount
            'D' => 5,   // Empty column
            'E' => 50,  // Notes (wider for Arabic text)
            'F' => 5,   // Empty column
            'G' => 12,  // Time
            'H' => 5,   // Empty column
            'I' => 15,  // Date
        ];
    }
}
