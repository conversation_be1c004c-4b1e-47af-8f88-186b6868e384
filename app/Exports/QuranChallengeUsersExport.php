<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\BeforeSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class QuranChallengeUsersExport implements FromCollection, WithHeadings, WithEvents, WithColumnWidths, WithColumnFormatting
{
    public function __construct(private readonly \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection $users)
    {
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users;
    }

    public function registerEvents(): array
    {
        return [
            BeforeSheet::class => function (BeforeSheet $event) {
                $event->getDelegate()->setRightToLeft(true);
            }
        ];
    }

    public function headings(): array
    {
        return [
            __('userColumns.name'),
            __('userColumns.family_user_id'),
            __('userColumns.gender'),
            __('userColumns.phone'),
            __('userColumns.age'),
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 35,
            'B' => 10,
            'C' => 10,
            'D' => 20,
            'E' => 20,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_TEXT,
            'D' => NumberFormat::FORMAT_TEXT,
        ];
    }
}
