<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use App\Models\BulkNotificationItem;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TaqnyatWebhookJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private $result,
    )
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            if (!isset($this->result['msgId']) || !isset($this->result['to']) || !isset($this->result['status']))
                return;
            $item = BulkNotificationItem::query()
                ->where('ref_id', $this->result['msgId'])
                ->where('phone', $this->result['to'])
                ->first();
            if (!is_null($item)) {
                $status = @[
                    'D' => 'delivered',
                    'S' => 'sent',
                ][$this->result['status']];
                if ($status) {
                    $item->update(['status' => $status]);
                    if ($this->result['status'] === 'D')
                        $item->update(['delivered_at' => now()]);
                    //deleteRedisJob('low', BulkNotification::class . ':' . $item->bulkNotification_id);
                    BulkNotificationUpdateStatusJob::dispatch($item->notification)->onQueue('low')->delay(now()->addMinutes());
                }
            }
        } catch (\Exception $exception) {

        }
    }
}
