<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use App\Models\StravaActivity;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;

class StravaActivityLocation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        #[WithoutRelations]
        private readonly StravaActivity $stravaActivity
    )
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->stravaActivity->start_latlng) {
            $locationData = (new \App\Services\LocationService())->getCityFromCoordinates(...$this->stravaActivity->start_latlng);
            if ($locationData) {
                $this->stravaActivity->update([
                    'location' => implode(', ', [
                        $locationData['city'],
                        $locationData['state'],
                        $locationData['country'],
                    ]),
                    'location_data' => $locationData,
                ]);
            }
        }
    }
}
