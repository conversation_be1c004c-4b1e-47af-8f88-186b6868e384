<?php

namespace App\Policies;

use App\Models\FamilyGraphShare;
use App\Models\User;
use App\Permissions\FamilyGraphSharePermissions;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;

class FamilyGraphSharePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User $user
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(FamilyGraphSharePermissions::index);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User $user
     * @param FamilyGraphShare $familyGraphShare
     * @return bool
     */
    public function view(User $user, FamilyGraphShare $familyGraphShare)
    {
        return $user->hasPermissionTo(FamilyGraphSharePermissions::index);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param User $user
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(FamilyGraphSharePermissions::create);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User $user
     * @param FamilyGraphShare $familyGraphShare
     * @return bool
     */
    public function update(User $user, FamilyGraphShare $familyGraphShare)
    {
        return $user->hasPermissionTo(FamilyGraphSharePermissions::update);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param User $user
     * @param FamilyGraphShare $familyGraphShare
     * @return bool
     */
    public function delete(User $user, FamilyGraphShare $familyGraphShare)
    {
        return $user->hasPermissionTo(FamilyGraphSharePermissions::delete);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param User $user
     * @param FamilyGraphShare $familyGraphShare
     * @return bool
     */
    public function restore(User $user, FamilyGraphShare $familyGraphShare)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param User $user
     * @param FamilyGraphShare $familyGraphShare
     * @return bool
     */
    public function forceDelete(User $user, FamilyGraphShare $familyGraphShare)
    {
        return $user->hasPermissionTo(FamilyGraphSharePermissions::delete);
    }
}
