<?php

namespace App\Policies;

use App\Models\HallReservation;
use App\Models\User;
use App\Permissions\HallReservationPermissions;
use Illuminate\Auth\Access\HandlesAuthorization;

class HallReservationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User $user
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(HallReservationPermissions::index);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User $user
     * @param HallReservation $hallReservation
     * @return bool
     */
    public function view(User $user, HallReservation $hallReservation)
    {
        return $user->hasPermissionTo(HallReservationPermissions::view);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param User $user
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(HallReservationPermissions::create);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User $user
     * @param HallReservation $hallReservation
     * @return bool
     */
    public function update(User $user, HallReservation $hallReservation)
    {
        return $user->hasPermissionTo(HallReservationPermissions::update);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param User $user
     * @param HallReservation $hallReservation
     * @return bool
     */
    public function delete(User $user, HallReservation $hallReservation)
    {
        return !$hallReservation->trashed() && $user->hasPermissionTo(HallReservationPermissions::delete);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param User $user
     * @param HallReservation $trashedHallReservation
     * @return bool
     */
    public function restore(User $user, HallReservation $trashedHallReservation)
    {
        return $trashedHallReservation->trashed() && $user->hasPermissionTo(HallReservationPermissions::restore);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param User $user
     * @param HallReservation $trashedHallReservation
     * @return bool
     */
    public function forceDelete(User $user, HallReservation $trashedHallReservation)
    {
        return false;
    }
}
