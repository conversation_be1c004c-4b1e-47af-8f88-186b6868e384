<?php

namespace App\Policies;

use App\Models\Consultation;
use App\Models\User;
use App\Permissions\ConsultationPermissions;
use Illuminate\Auth\Access\HandlesAuthorization;

class ConsultationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(ConsultationPermissions::index);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Consultation $consultation)
    {
        return $user->hasPermissionTo(ConsultationPermissions::index);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(ConsultationPermissions::create);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Consultation $consultation)
    {
        return $user->hasPermissionTo(ConsultationPermissions::update);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Consultation $consultation)
    {
        return $user->hasPermissionTo(ConsultationPermissions::delete);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Consultation $consultation)
    {
        return $user->hasPermissionTo(ConsultationPermissions::restore);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Consultation $consultation)
    {
        return $user->hasPermissionTo(ConsultationPermissions::delete);
    }
}
