<?php

namespace App\Policies;

use App\Models\User;
use App\Models\UserVoluntary;
use App\Permissions\VoluntaryPermissions;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserVoluntaryPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User $user
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(VoluntaryPermissions::index);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User $user
     * @param UserVoluntary $userVoluntary
     * @return bool
     */
    public function view(User $user, UserVoluntary $userVoluntary)
    {
        return $user->hasPermissionTo(VoluntaryPermissions::view);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param User $user
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(VoluntaryPermissions::create);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User $user
     * @param UserVoluntary $userVoluntary
     * @return bool
     */
    public function update(User $user, UserVoluntary $userVoluntary)
    {
        return $user->hasPermissionTo(VoluntaryPermissions::update);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param User $user
     * @param UserVoluntary $userVoluntary
     * @return bool
     */
    public function delete(User $user, UserVoluntary $userVoluntary)
    {
        return !$userVoluntary->trashed() && $user->hasPermissionTo(VoluntaryPermissions::delete);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param User $user
     * @param UserVoluntary $trashedUserVoluntary
     * @return bool
     */
    public function restore(User $user, UserVoluntary $trashedUserVoluntary)
    {
        return $trashedUserVoluntary->trashed() && $user->hasPermissionTo(VoluntaryPermissions::restore);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param User $user
     * @param UserVoluntary $userVoluntary
     * @return bool
     */
    public function forceDelete(User $user, UserVoluntary $userVoluntary)
    {
        return false;
    }
}
