<?php

namespace App\Policies;

use App\Models\Branch;
use App\Models\User;
use App\Permissions\BranchPermissions;
use Illuminate\Auth\Access\HandlesAuthorization;

class BranchPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User $user
     * @return boolean
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(BranchPermissions::index);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User $user
     * @param Branch $branch
     * @return boolean
     */
    public function view(User $user, Branch $branch)
    {
        return $user->hasPermissionTo(BranchPermissions::index);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param User $user
     * @return boolean
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(BranchPermissions::create);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User $user
     * @param Branch $branch
     * @return boolean
     */
    public function update(User $user, Branch $branch)
    {
        return $user->hasPermissionTo(BranchPermissions::update);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param User $user
     * @param Branch $branch
     * @return boolean
     */
    public function delete(User $user, Branch $branch)
    {
        return $user->hasPermissionTo(BranchPermissions::delete);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param User $user
     * @param Branch $branch
     * @return boolean
     */
    public function restore(User $user, Branch $branch)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param User $user
     * @param Branch $branch
     * @return boolean
     */
    public function forceDelete(User $user, Branch $branch)
    {
        return $user->hasPermissionTo(BranchPermissions::delete);
    }
}
