<?php

namespace App\Policies;

use App\Models\BankAccount;
use App\Models\User;
use App\Permissions\BankAccountPermissions;
use Illuminate\Auth\Access\HandlesAuthorization;

class BankAccountPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User $user
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(BankAccountPermissions::index);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User $user
     * @param BankAccount $bankAccount
     * @return bool
     */
    public function view(User $user, BankAccount $bankAccount)
    {
        return $user->hasPermissionTo(BankAccountPermissions::index);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param User $user
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(BankAccountPermissions::create);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User $user
     * @param BankAccount $bankAccount
     * @return bool
     */
    public function update(User $user, BankAccount $bankAccount)
    {
        return $user->hasPermissionTo(BankAccountPermissions::update);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param User $user
     * @param BankAccount $bankAccount
     * @return bool
     */
    public function delete(User $user, BankAccount $bankAccount)
    {
        return $user->hasPermissionTo(BankAccountPermissions::delete);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param User $user
     * @param BankAccount $bankAccount
     * @return bool
     */
    public function restore(User $user, BankAccount $bankAccount)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param User $user
     * @param BankAccount $bankAccount
     * @return bool
     */
    public function forceDelete(User $user, BankAccount $bankAccount)
    {
        return $user->hasPermissionTo(BankAccountPermissions::delete);
    }
}
