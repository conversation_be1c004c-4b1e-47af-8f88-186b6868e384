<?php

namespace App\Policies;

use App\Models\TallyForm;
use App\Models\User;
use App\Permissions\TallyFormPermissions;
use Illuminate\Auth\Access\HandlesAuthorization;

class TallyFormPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(TallyFormPermissions::index);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User $user
     * @param TallyForm $tallyForm
     * @return mixed
     */
    public function view(User $user, TallyForm $tallyForm)
    {
        return $user->hasPermissionTo(TallyFormPermissions::view);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param User $user
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(TallyFormPermissions::create);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User $user
     * @param TallyForm $tallyForm
     * @return mixed
     */
    public function update(User $user, TallyForm $tallyForm)
    {
        return $this->view($user, $tallyForm) && $user->hasPermissionTo(TallyFormPermissions::update);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param User $user
     * @param TallyForm $tallyForm
     * @return mixed
     */
    public function delete(User $user, TallyForm $tallyForm)
    {
        return $user->hasPermissionTo(TallyFormPermissions::delete);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param User $user
     * @param TallyForm $tallyForm
     * @return mixed
     */
    public function restore(User $user, TallyForm $tallyForm)
    {
        return $user->hasPermissionTo(TallyFormPermissions::restore);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param User $user
     * @param TallyForm $tallyForm
     * @return mixed
     */
    public function forceDelete(User $user, TallyForm $tallyForm)
    {
        return false;
    }
}
