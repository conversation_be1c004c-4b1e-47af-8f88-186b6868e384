<?php

namespace App\Tasks;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Tasks\SwitchTenantTask;
use Spatie\Multitenancy\Concerns\UsesMultitenancyConfig;
use Spatie\Multitenancy\Exceptions\InvalidConfiguration;
use Spatie\Multitenancy\Models\Tenant;

class SwitchTenantDatabaseTask implements SwitchTenantTask
{
    use UsesMultitenancyConfig;

    public function makeCurrent(Tenant|\App\Models\Tenant $tenant): void
    {
        $this->setTenantConnectionDatabaseName($tenant->getDatabaseName(), $tenant->getDatabaseScheme());
    }

    public function forgetCurrent(): void
    {
        $this->setTenantConnectionDatabaseName(null, null);
    }

    protected function setTenantConnectionDatabaseName(?string $databaseName, ?string $databaseScheme)
    {
        $tenantConnectionName = $this->tenantDatabaseConnectionName();

        if ($tenantConnectionName === $this->landlordDatabaseConnectionName()) {
            throw InvalidConfiguration::tenantConnectionIsEmptyOrEqualsToLandlordConnection();
        }

        if (is_null(config("database.connections.{$tenantConnectionName}"))) {
            throw InvalidConfiguration::tenantConnectionDoesNotExist($tenantConnectionName);
        }

        config([
            "database.connections.{$tenantConnectionName}.database" => $databaseName,
            "database.connections.{$tenantConnectionName}.search_path" => $databaseScheme,
            "database.connections.shurl.search_path" => $databaseScheme,
            "telescope.storage.database" => $tenantConnectionName,
        ]);

        app('db')->extend($tenantConnectionName, function ($config, $name) use ($databaseName, $databaseScheme) {
            $config['database'] = $databaseName;
            $config['search_path'] = $databaseScheme;

            return app('db.factory')->make($config, $name);
        });

        app('db')->extend('shurl', function ($config, $name) use ($databaseScheme) {
            //$config['database'] = $databaseName;
            $config['search_path'] = $databaseScheme;

            return app('db.factory')->make($config, $name);
        });

        DB::purge($tenantConnectionName);
        DB::purge('shurl');

        // Octane will have an old `db` instance in the Model::$resolver.
        Model::setConnectionResolver(app('db'));
    }
}
