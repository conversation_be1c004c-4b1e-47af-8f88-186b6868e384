<?php

namespace App\Console\Commands;

use Validator;
use App\Models\User;
use App\Enums\Gender;
use Illuminate\Console\Command;
use Spatie\Activitylog\Facades\LogBatch;
use Illuminate\Validation\ValidationException;
use Symfony\Component\Console\Command\Command as CommandAlias;

class InvalidUsersFamilyIdFix extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invalid:family_id:fix
                            {--tenants=* : The tenant(s) to run the command for. Default: all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix InvalidUsersFamilyId';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        tenancy()->runForMultiple($this->option('tenants'), function ($tenant) {
            $users = User::query()->get();
            LogBatch::startBatch();
            $users->each(function (User $user) {
                $p1 = substr($user->family_user_id, 0, 2);
                $p2 = substr($user->family_user_id, 2, 1);
                $p3 = substr($user->family_user_id, -2);
                try {
                    Validator::validate(compact(['p1', 'p2', 'p3']), [
                        'p1' => 'integer|between:10,99',
                        'p2' => 'integer|between:' . ($user->gender === Gender::Male ? '5,9' : '0,4'),
                        'p3' => 'integer|between:10,99',
                    ]);
                } catch (ValidationException $exception) {
                    if (count($exception->errors()) === 1 && isset($exception->errors()['p2'])) {
                        do {
                            $familyUserId = rand(10, 99) . ($user->gender === Gender::Male ? rand(5, 9) : rand(0, 4)) . rand(10, 99);
                        } while (User::withTrashed()->withoutGlobalScopes(['family_users'])->where('family_user_id', $familyUserId)->exists());
                        $user->update(['family_user_id' => $familyUserId]);
                    }
                }
            });
            LogBatch::endBatch();
        });
        return CommandAlias::SUCCESS;
    }
}
