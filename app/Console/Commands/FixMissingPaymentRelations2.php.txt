<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use App\Models\MyfatoorahPayment;
use App\Models\ProductTransaction;
use Illuminate\Support\Facades\Log;
use MyFatoorah\Library\PaymentMyfatoorahApiV2;
use Symfony\Component\Console\Command\Command as CommandAlias;

class FixMissingPaymentRelations2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payments:fix-missing-relations
                            {--tenants=* : The tenant(s) to run the command for. Default: all}
                            {--limit=100 : Number of records to process per batch}
                            {--dry-run : Run without making changes}
                            {--force : Skip confirmation prompt}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix missing MyfatoorahPayment relations for confirmed transactions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        tenancy()->runForMultiple($this->option('tenants'), function ($tenant) {
            $this->info("Processing tenant: {$tenant->id}");
            return $this->processTenant();
        });
        return CommandAlias::SUCCESS;
    }

    /**
     * Process transactions for a single tenant
     */
    private function processTenant()
    {
        $limit = $this->option('limit');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('Searching for transactions with missing payment relations...');

        // Find transactions that are confirmed but don't have payment relations
        $query = ProductTransaction::whereNotNull('confirmed_at')
            ->whereNull('payment_id')
            ->whereNotNull('invoice_id');

        $totalCount = $query->count();

        if ($totalCount === 0) {
            $this->info('No transactions found that need fixing.');
            return 0;
        }

        $this->info("Found {$totalCount} transactions that need fixing.");

        if (!$force && !$dryRun) {
            if (!$this->confirm("Do you want to proceed with fixing {$totalCount} transactions?")) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $processed = 0;
        $fixed = 0;
        $errors = 0;

        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->start();

        // Process in batches
        $query->chunk($limit, function ($transactions) use ($dryRun, &$processed, &$fixed, &$errors, $progressBar) {
            foreach ($transactions as $transaction) {
                $processed++;

                try {
                    if ($this->fixTransactionPayment($transaction, $dryRun)) {
                        $fixed++;
                    }
                } catch (Exception $e) {
                    $errors++;
                    Log::error("Failed to fix transaction {$transaction->uuid}: " . $e->getMessage());
                    $this->error("Failed to fix transaction {$transaction->uuid}: " . $e->getMessage());
                }

                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine(2);

        // Summary
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Processed', $processed],
                ['Successfully Fixed', $fixed],
                ['Errors', $errors],
            ]
        );

        if ($dryRun) {
            $this->info('Dry run completed. No actual changes were made.');
        } else {
            $this->info('Command completed successfully!');
        }

        return CommandAlias::SUCCESS;
    }

    /**
     * Fix a single transaction's payment relation
     *
     * @param ProductTransaction $transaction
     * @param bool $dryRun
     * @return bool
     * @throws Exception
     */
    private function fixTransactionPayment(ProductTransaction $transaction, bool $dryRun = false): bool
    {
        if (!$transaction->invoice_id) {
            $this->warn("Transaction {$transaction->uuid} has no invoice_id, skipping...");
            return false;
        }

        try {
            // Initialize MyFatoorah API
            $mfObj = new PaymentMyfatoorahApiV2(
                config('myfatoorah.api_key'),
                config('myfatoorah.country_iso'),
                config('myfatoorah.test_mode')
            );

            // Get payment status from MyFatoorah
            $paymentStatus = $mfObj->getPaymentStatus($transaction->invoice_id, 'invoiceid');

            if (!$paymentStatus || !$paymentStatus->focusTransaction) {
                $this->warn("No payment status found for transaction {$transaction->uuid}");
                return false;
            }

            $focusTransaction = $paymentStatus->focusTransaction;

            // Prepare payment data based on actual MyFatoorah response structure
            $paymentData = [
                'paid_at' => $transaction->confirmed_at,
                'payment_method' => $focusTransaction->PaymentGateway ?? 'Unknown',
                'payment_id' => $focusTransaction->PaymentId ?? null,
                'reference_id' => $focusTransaction->ReferenceId ?? null,
                'track_id' => $focusTransaction->TrackId ?? null,
                'status' => 'paid',
                'due_deposit' => $paymentStatus->DueDeposit ?? 0,
                'service_charge' => $focusTransaction->TotalServiceCharge ?? 0,
                'vat_amount' => $focusTransaction->VatAmount ?? 0,
                'card_number' => $focusTransaction->CardNumber ?? null,
                'ip' => $focusTransaction->IpAddress ?? null,
                'country' => $focusTransaction->Country ?? null,
            ];

            if ($dryRun) {
                $this->line("Would fix transaction {$transaction->uuid} with payment method: {$paymentData['payment_method']}");
                return true;
            }

            // Check if payment record exists
            $payment = MyfatoorahPayment::where('product_transaction_uuid', $transaction->uuid)->first();

            if ($payment) {
                // Update existing payment record
                $payment->update($paymentData);
                $this->line("Updated existing payment for transaction {$transaction->uuid}");
            } else {
                // Create new payment record
                $payment = MyfatoorahPayment::create([
                    'product_transaction_uuid' => $transaction->uuid,
                    'session_id' => $transaction->session_id,
                    'apple_session_id' => $transaction->apple_session_id,
                    ...$paymentData
                ]);
                $this->line("Created new payment for transaction {$transaction->uuid}");
            }

            // Associate payment with transaction if not already associated
            if (!$transaction->payment_id) {
                $transaction->payment()->associate($payment);
                $transaction->save();
            }

            return true;

        } catch (Exception $e) {
            Log::error("Error fixing transaction {$transaction->uuid}: " . $e->getMessage(), [
                'transaction_uuid' => $transaction->uuid,
                'invoice_id' => $transaction->invoice_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
