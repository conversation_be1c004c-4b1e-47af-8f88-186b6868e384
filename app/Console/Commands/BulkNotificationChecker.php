<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\BulkNotification;
use Spatie\Activitylog\Facades\LogBatch;
use Symfony\Component\Console\Command\Command as CommandAlias;

class BulkNotification<PERSON><PERSON>cker extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:bulk-notification-checker {--longer : Longer check}
                            {--tenants=* : The tenant(s) to run the command for. Default: all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $longer = $this->option('longer') === true;
        tenancy()->runForMultiple($this->option('tenants'), function ($tenant) use ($longer) {
            LogBatch::startBatch();
            $query = BulkNotification::query()
                ->where('hidden', false)
                ->where(fn($q) => $q->whereNull('sent_at')->orWhereNull('delivered_at')->orWhereNull('read_at'))
                ->where(fn($q) => $q->whereNull('scheduled_at')->orWhere('scheduled_at', '<', now()))
                ->when(
                    $longer, fn($q) => $q
                    ->where('created_at', '>', now()->subDays(14))
                    ->where('created_at', '<', now()->subDays())
                )
                ->when(!$longer, fn($q) => $q->where('created_at', '>', now()->subDays()));
            bulkNotificationChecker($query);
            LogBatch::endBatch();
        });
        return CommandAlias::SUCCESS;
    }
}
