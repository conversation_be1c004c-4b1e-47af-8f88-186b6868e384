<?php

namespace App\Exceptions;

use Exception;

class OTPException extends Exception
{
    /**
     * Create a new OTP exception instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = '', int $code = 400, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
