<?php

namespace App\Services;

use App\Helpers\SmartDateParser;
use App\Models\BankTransactionImport;
use App\Models\BankTransactionImportItem;
use App\Models\BankAccountTransaction;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class BankTransactionImportService
{
    /**
     * Process uploaded Excel file and create import record
     *
     * @param UploadedFile $file
     * @param int $bankAccountId
     * @param int $userId
     * @return array
     */
    public function processFileUpload(UploadedFile $file, int $bankAccountId, int $userId): array
    {
        try {
            // Check for duplicate file
            $fileHash = md5_file($file->getRealPath());
            if (BankTransactionImport::where('file_hash', $fileHash)->exists()) {
                return [
                    'success' => false,
                    'error' => 'الملف مرفوع مسبقاً',
                    'error_code' => 'DUPLICATE_FILE'
                ];
            }

            // Parse Excel file
            $parseResult = $this->parseExcelFile($file);
            if (!$parseResult['success']) {
                return $parseResult;
            }

            DB::beginTransaction();

            // Store file
            $path = $file->store('excel-files');
            
            // Create import record
            $bankTransactionImport = BankTransactionImport::create([
                'bank_account_id' => $bankAccountId,
                'user_id' => $userId,
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $path,
                'file_size' => $file->getSize(),
                'file_hash' => md5_file(Storage::path($path)),
            ]);

            // Process transaction data
            $this->processTransactionData($parseResult['data'], $bankTransactionImport);

            // Update counts and sums
            $this->updateImportStatistics($bankTransactionImport);

            DB::commit();

            return [
                'success' => true,
                'import' => $bankTransactionImport,
                'message' => 'تم رفع الملف ومعالجته بنجاح'
            ];

        } catch (Exception $exception) {
            DB::rollBack();
            Log::error('BankTransactionImportService@processFileUpload', [
                'message' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => 'خطأ في معالجة الملف',
                'error_code' => 'PROCESSING_ERROR'
            ];
        }
    }

    /**
     * Parse Excel file and extract transaction data
     *
     * @param UploadedFile $file
     * @return array
     */
    public function parseExcelFile(UploadedFile $file): array
    {
        try {
            $bankFileData = (new \App\Imports\BankTransactionImport())->toCollection($file)->first();
            
            // Extract transaction data (skip header row, filter non-empty amounts)
            $transactionData = $bankFileData->skip(1)->where(fn($d) => !empty($d[3]))->values();
            
            if ($transactionData->count() === 0) {
                return [
                    'success' => false,
                    'error' => 'لا يوجد عمليات في الملف',
                    'error_code' => 'NO_TRANSACTIONS'
                ];
            }

            return [
                'success' => true,
                'data' => $transactionData,
                'count' => $transactionData->count()
            ];

        } catch (Exception $exception) {
            Log::error('BankTransactionImportService@parseExcelFile', [
                'message' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => 'خطأ في قراءة الملف',
                'error_code' => 'FILE_PARSE_ERROR'
            ];
        }
    }

    /**
     * Process transaction data and create import items
     *
     * @param \Illuminate\Support\Collection $transactionData
     * @param BankTransactionImport $bankTransactionImport
     * @return void
     */
    protected function processTransactionData($transactionData, BankTransactionImport $bankTransactionImport): void
    {
        $transactionData->each(function ($item) use ($bankTransactionImport) {
            $processedItem = $this->processTransactionItem($item, $bankTransactionImport);
            $bankTransactionImport->items()->create($processedItem);
        });
    }

    /**
     * Process individual transaction item
     *
     * @param array $item
     * @param BankTransactionImport $bankTransactionImport
     * @return array
     */
    protected function processTransactionItem(array $item, BankTransactionImport $bankTransactionImport): array
    {
        // Parse date
        $date = $this->parseTransactionDate($item);
        
        // Parse amount
        $amount = $this->parseAmount($item[3]);
        
        // Parse balance
        $balance = $this->parseAmount($item[1]);
        
        // Extract notes
        $notes = trim($item[4]);
        
        // Generate hashes for duplicate detection
        $hash = sha1(implode('_', [
            $notes,
            $date->format('Y-m-d|H:i:s'),
            $amount,
            $balance,
        ]));
        
        $noteHash = sha1($notes);
        
        // Extract account number from notes
        preg_match_all('/\d{7,}/', $notes, $accountMatches);
        $accountId = $accountMatches[0] ? $accountMatches[0][0] : null;
        $accountHash = $accountId ? sha1($accountId) : null;
        
        // Check for duplicates
        $hasError = $this->checkForDuplicates($hash, $bankTransactionImport->uuid);
        
        // Find existing user based on note or account hash
        $userId = $hasError ? $this->findExistingUser($noteHash, $accountHash) : null;

        return [
            'notes' => $notes,
            'amount' => $amount,
            'error' => $hasError,
            'due_at' => $date,
            'hash' => $hash,
            'account' => $accountId,
            'note_hash' => $noteHash,
            'account_hash' => $accountHash,
            'user_id' => $userId,
        ];
    }

    /**
     * Parse transaction date from Excel data
     *
     * @param array $item
     * @return Carbon
     */
    protected function parseTransactionDate(array $item): Carbon
    {
        $date = now()->toDateString();
        
        try {
            // Parse date (column 8)
            $date = is_string($item[8]) 
                ? SmartDateParser::parse($item[8]) 
                : new Carbon(Date::excelToDateTimeObject($item[8]));
        } catch (Exception $exception) {
            $date = now();
        }
        
        try {
            // Parse time (column 6) and set it on the date
            $excelTime = is_string($item[6]) 
                ? SmartDateParser::parse($item[6]) 
                : new Carbon(Date::excelToDateTimeObject($item[6]));
            $date->setTimeFrom($excelTime);
        } catch (Exception $exception) {
            // Keep the date without time modification
        }

        return $date;
    }

    /**
     * Parse amount from string
     *
     * @param string $amountString
     * @return float
     */
    protected function parseAmount(string $amountString): float
    {
        return (float) str($amountString)
            ->replace('٫', '.')
            ->replace(',', '')
            ->replace('ر.س', '')
            ->replace(' ', '')
            ->toString();
    }

    /**
     * Check for duplicate transactions
     *
     * @param string $hash
     * @param string $currentImportUuid
     * @return string|null
     */
    protected function checkForDuplicates(string $hash, string $currentImportUuid): ?string
    {
        $exists = BankTransactionImportItem::query()
            ->whereHas('bank_transaction_import')
            ->where('bank_transaction_import_uuid', '!=', $currentImportUuid)
            ->where('hash', $hash)
            ->exists();

        return $exists ? 'عملية مكررة' : null;
    }

    /**
     * Find existing user based on note or account hash
     *
     * @param string $noteHash
     * @param string|null $accountHash
     * @return int|null
     */
    protected function findExistingUser(string $noteHash, ?string $accountHash): ?int
    {
        return BankTransactionImportItem::query()
            ->where(function ($q) use ($noteHash, $accountHash) {
                $q->where('note_hash', $noteHash);
                if ($accountHash) {
                    $q->orWhere('account_hash', $accountHash);
                }
            })
            ->whereNotNull('user_id')
            ->value('user_id');
    }

    /**
     * Update import statistics
     *
     * @param BankTransactionImport $import
     * @return void
     */
    protected function updateImportStatistics(BankTransactionImport $import): void
    {
        $import->loadCount('items');
        $import->loadSum('items', 'amount');
        
        $import->update([
            'items_count' => $import->items_count,
            'items_sum_amount' => $import->items_sum_amount,
        ]);
    }
}
