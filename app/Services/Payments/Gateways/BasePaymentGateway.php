<?php

namespace App\Services\Payments\Gateways;

use App\Models\ProductTransaction;

/**
 * Abstract base class for payment gateways
 */
abstract class BasePaymentGateway implements PaymentGatewayInterface
{
    /**
     * By default, gateways don't support recurring payments
     */
    public function supportsRecurring(): bool
    {
        return false;
    }

    /**
     * Create a payment detail record
     *
     * @param ProductTransaction $transaction
     * @param string             $gateway
     * @param array              $gatewayData
     * @param string|null        $paymentMethod
     * @return PaymentDetail
     */
    protected function createPaymentDetail(
        ProductTransaction $transaction,
        string             $gateway,
        array              $gatewayData,
        ?string            $paymentMethod = null
    ): PaymentDetail
    {
        $paymentDetail = new PaymentDetail();
        $paymentDetail->payable_type = get_class($transaction);
        $paymentDetail->payable_id = $transaction->uuid;
        $paymentDetail->gateway = $gateway;
        $paymentDetail->gateway_data = $gatewayData;
        $paymentDetail->payment_method = $paymentMethod;
        $paymentDetail->save();

        return $paymentDetail;
    }
}
