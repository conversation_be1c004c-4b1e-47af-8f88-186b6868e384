<?php

namespace App\Services;

use App\Models\BankTransactionImport;
use App\Models\BankAccountTransaction;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BankTransactionConfirmationService
{
    /**
     * Confirm bank transaction import and create actual transactions
     *
     * @param BankTransactionImport $import
     * @return array
     */
    public function confirmImport(BankTransactionImport $import): array
    {
        if (!is_null($import->completed_at)) {
            return [
                'success' => false,
                'error' => 'الاستيراد مكتمل مسبقاً',
                'error_code' => 'ALREADY_COMPLETED'
            ];
        }

        if (!$this->canConfirmImport($import)) {
            return [
                'success' => false,
                'error' => 'لا يمكن تأكيد الاستيراد - يوجد عناصر غير مكتملة',
                'error_code' => 'INCOMPLETE_ITEMS'
            ];
        }

        try {
            DB::beginTransaction();

            $processedCount = $this->processImportItems($import);
            
            $import->update([
                'completed_at' => now(),
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'تم تأكيد بيانات الملف بنجاح',
                'processed_count' => $processedCount
            ];

        } catch (Exception $exception) {
            DB::rollBack();
            Log::error('BankTransactionConfirmationService@confirmImport', [
                'import_uuid' => $import->uuid,
                'message' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => 'خطأ غير متوقع أثناء التأكيد',
                'error_code' => 'CONFIRMATION_ERROR'
            ];
        }
    }

    /**
     * Cancel/delete bank transaction import
     *
     * @param BankTransactionImport $import
     * @return array
     */
    public function cancelImport(BankTransactionImport $import): array
    {
        if (!is_null($import->completed_at)) {
            return [
                'success' => false,
                'error' => 'لا يمكن حذف استيراد مكتمل',
                'error_code' => 'CANNOT_DELETE_COMPLETED'
            ];
        }

        try {
            $import->delete();

            return [
                'success' => true,
                'message' => 'تم حذف بيانات الملف بنجاح'
            ];

        } catch (Exception $exception) {
            Log::error('BankTransactionConfirmationService@cancelImport', [
                'import_uuid' => $import->uuid,
                'message' => $exception->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'خطأ في حذف الملف',
                'error_code' => 'DELETE_ERROR'
            ];
        }
    }

    /**
     * Check if import can be confirmed
     *
     * @param BankTransactionImport $import
     * @return bool
     */
    public function canConfirmImport(BankTransactionImport $import): bool
    {
        // Load items if not already loaded
        if (!$import->relationLoaded('items')) {
            $import->load('items');
        }

        // Check if there are any valid items (no errors and either has user_id or is anonymous)
        $validItems = $import->items->filter(function ($item) {
            return empty($item->error) && (!empty($item->user_id) || $item->anonymous);
        });

        // Check if there are any incomplete items (no error, not anonymous, and no user_id)
        $incompleteItems = $import->items->filter(function ($item) {
            return empty($item->error) && !$item->anonymous && empty($item->user_id);
        });

        return $validItems->count() > 0 && $incompleteItems->count() === 0;
    }

    /**
     * Process import items and create bank account transactions
     *
     * @param BankTransactionImport $import
     * @return int Number of processed items
     */
    protected function processImportItems(BankTransactionImport $import): int
    {
        $processedCount = 0;

        // Get items that should be processed (have user_id or are anonymous, and no errors)
        $items = $import->items()
            ->where(function ($query) {
                $query->whereNotNull('user_id')
                      ->orWhere('anonymous', true);
            })
            ->whereNull('error')
            ->get();

        foreach ($items as $item) {
            $transaction = BankAccountTransaction::create([
                'bank_account_id' => $import->bank_account_id,
                'amount' => $item->amount,
                'due_at' => $item->due_at,
                'reference' => $item->reference,
                'account' => $item->account,
                'user_id' => $item->user_id,
                'anonymous' => $item->anonymous && is_null($item->user_id),
                'is_verified' => !is_null($item->user_id),
                'upgrade_notes' => $item->notes,
            ]);

            // Associate the transaction with the import item
            $item->transaction()->associate($transaction)->save();
            $processedCount++;
        }

        return $processedCount;
    }

    /**
     * Get import confirmation status and statistics
     *
     * @param BankTransactionImport $import
     * @return array
     */
    public function getConfirmationStatus(BankTransactionImport $import): array
    {
        if (!$import->relationLoaded('items')) {
            $import->load('items');
        }

        $totalItems = $import->items->count();
        $itemsWithErrors = $import->items->where('error', '!=', null)->count();
        $validItems = $import->items->where('error', null)->count();
        $assignedItems = $import->items->where('error', null)->whereNotNull('user_id')->count();
        $anonymousItems = $import->items->where('error', null)->where('anonymous', true)->count();
        $unassignedItems = $import->items->where('error', null)->where('anonymous', false)->whereNull('user_id')->count();

        $canConfirm = $this->canConfirmImport($import);

        return [
            'total_items' => $totalItems,
            'items_with_errors' => $itemsWithErrors,
            'valid_items' => $validItems,
            'assigned_items' => $assignedItems,
            'anonymous_items' => $anonymousItems,
            'unassigned_items' => $unassignedItems,
            'can_confirm' => $canConfirm,
            'is_completed' => !is_null($import->completed_at),
            'completion_percentage' => $totalItems > 0 ? round(($assignedItems + $anonymousItems) / $totalItems * 100, 2) : 0,
        ];
    }

    /**
     * Update import item with user assignment or other details
     *
     * @param BankTransactionImport $import
     * @param array $itemsData
     * @return array
     */
    public function updateImportItems(BankTransactionImport $import, array $itemsData): array
    {
        if (!is_null($import->completed_at)) {
            return [
                'success' => false,
                'error' => 'لا يمكن تعديل استيراد مكتمل',
                'error_code' => 'CANNOT_EDIT_COMPLETED'
            ];
        }

        try {
            DB::beginTransaction();

            $updatedCount = 0;
            foreach (array_filter($itemsData) as $itemData) {
                $item = $import->items()->find($itemData['id']);
                if ($item) {
                    $item->update([
                        'user_id' => $itemData['user_id'] ?? null,
                        'account' => $itemData['account'] ?? $item->account,
                        'reference' => $itemData['reference'] ?? $item->reference,
                        'anonymous' => $itemData['anonymous'] ?? false,
                    ]);
                    $updatedCount++;
                }
            }

            DB::commit();

            return [
                'success' => true,
                'message' => "تم تحديث {$updatedCount} عنصر بنجاح",
                'updated_count' => $updatedCount
            ];

        } catch (Exception $exception) {
            DB::rollBack();
            Log::error('BankTransactionConfirmationService@updateImportItems', [
                'import_uuid' => $import->uuid,
                'message' => $exception->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'خطأ في تحديث العناصر',
                'error_code' => 'UPDATE_ERROR'
            ];
        }
    }
}
