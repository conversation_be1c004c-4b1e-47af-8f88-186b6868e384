<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserOTP;
use Carbon\Carbon;
use App\Settings\AppSettings;
use Illuminate\Support\Facades\Log;
use Random\RandomException;

class OTPService
{
    /**
     * Create a new OTP record
     *
     * @param User $user
     * @param string $method
     * @param string $authMethod
     * @param array $metadata
     * @return UserOTP
     * @throws RandomException
     */
    public function create(User $user, string $method, string $authMethod, array $metadata): UserOTP
    {
        if (\RateLimiter::tooManyAttempts("otp-methods:u:{$user->id}", 4)) {
            Log::channel('slack')->error(sprintf('محاولة طلب كود مؤقت أكثر من مرة رقم التعريفي: #%s الطريقة: %s', $user->family_user_id, $method));
            response()->json([
                'success' => false,
                'message' => ['Too Many Attempts.'],
            ], 429)->send();
            exit;
        }
        \RateLimiter::increment("otp-methods:u:{$user->id}");

        $inReview = $user->is_apple_reviewer && app(AppSettings::class)->reviewMode;
        $otp = $inReview ? '1379' : (app()->environment(['production']) ? $this->generateOTPCode() : '1436');

        $ipLocation = request()->ip() ? ip2country(request()->ip()) : null;
        return $user->otp()->create([
            'code' => $otp,
            'method' => $method,
            'ip' => request()->ip(),
            'country_iso_code' => $ipLocation?->isoCode,
            'country_name' => $ipLocation?->name,
            'metadata' => $metadata,
            'auth_method' => $authMethod,
            'expired_at' => Carbon::now()->addMinutes(config('otp.timeout') ?? 3),
        ]);
    }

    /**
     * Generate OTP code
     *
     * @return string
     * @throws RandomException
     */
    private function generateOTPCode(): string
    {
        return (string)random_int(1000, 9999);
    }

    /**
     * Verify OTP code
     *
     * @param string $uuid
     * @param string $code
     * @return bool
     */
    public function verify(string $uuid, string $code): bool
    {
        $otp = UserOTP::where('uuid', $uuid)
            ->where('code', $code)
            ->where('expires_at', '>', now())
            ->first();

        if (!$otp) {
            return false;
        }

        $otp->update(['verified_at' => now()]);
        return true;
    }
}
