<?php

namespace App\Models;

use App\Observers\ChildFormObserver;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;

#[ObservedBy(ChildFormObserver::class)]
class ChildForm extends Model
{
    use SoftDeletes, HasUuids;

    protected $primaryKey = 'uuid';
    protected $guarded = [];
    protected $casts = [
        'dob' => 'datetime',
        'reviewed_at' => 'datetime',
    ];

    public function father()
    {
        return $this->belongsTo(User::class, 'father_id')->withTrashed();
    }

    public function mother()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function user_region()
    {
        return $this->belongsTo(UserRegion::class);
    }
}
