<?php

namespace App\Models;

use App\Observers\GroupReportObserver;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;

#[ObservedBy([GroupReportObserver::class])]
class GroupReport extends Model
{
    use LogsActivity, HasFactory;

    protected $fillable = [
        'report_id',
        'group_id',
        'group_date_id',
        'user_id',
        'year',
        'key',
        'title',
        'location',
        'discussions',
        'decisions',
    ];

    protected $casts = [
        'discussions' => 'array',
        'decisions' => 'array',
    ];

    protected $appends = ['name'];

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function group_date()
    {
        return $this->belongsTo(GroupDate::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function getNameAttribute()
    {
        return implode(' ', [
            'رقم',
            $this->getAttribute('key'),
            'لعام',
            $this->getAttribute('year'),
        ]);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded()
            ->logExcept(['created_at', 'updated_at'])
            ->dontSubmitEmptyLogs()
            ->logOnlyDirty();
    }
}
