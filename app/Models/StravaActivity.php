<?php

namespace App\Models;

use App\Data\StravaActivityData;
use Illuminate\Database\Eloquent\Model;

class StravaActivity extends Model
{
    protected $guarded = [];
    protected $casts = [
        'map' => 'json',
        'photos' => 'json',
        'laps' => 'json',
        'start_latlng' => 'json',
        'end_latlng' => 'json',
        'start_date' => 'datetime',
        'start_date_local' => 'datetime',
        'data' => StravaActivityData::class . ':default',
    ];

    public function strava_user()
    {
        return $this->belongsTo(StravaUser::class)->withTrashed();
    }

    public function user()
    {
        return $this->hasOneThrough(
            User::class,
            StravaUser::class,
            'id', // Foreign key on the cars table...
            'id', // Foreign key on the owners table...
            'strava_user_id', // Local key on the mechanics table...
            'user_id' // Local key on the cars table...
        )->withTrashed();
    }
}
