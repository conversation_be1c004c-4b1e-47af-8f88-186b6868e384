<?php

namespace App\Models;

use Exception;
use App\Settings\AppSettings;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Support\Facades\Schema;
use App\Services\Payments\Gateways\MyfatoorahGateway;

class ProductTransaction extends Model
{
    use SoftDeletes, HasUuids;

    protected $guarded = [];
    protected $casts = [
        'amount' => 'int',
        'confirmed_at' => 'datetime',
        'refunded_at' => 'datetime',
        'transaction_metadata' => 'array',
        'processing_lock' => 'datetime',
        'processing_status' => 'string', // Explicitly cast processing_status
    ];

    protected $primaryKey = 'uuid';

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id')->withTrashed();
    }

    public function documents()
    {
        return $this->hasMany(ProductTransactionDocument::class, 'product_transaction_uuid');
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed()->withoutGlobalScope('family_users');
    }

    public function refunded_by()
    {
        return $this->belongsTo(User::class)->withTrashed()->withoutGlobalScope('family_users');
    }

    public function payment()
    {
        return $this->morphTo('payment');
    }

    public function getSmsUrlAttribute()
    {
        return trim($this->getAttribute('domain') ?: app(AppSettings::class)->appUrl, '/') . '/Invoice/' . $this->getAttribute('slug');
    }

    public function getPdfUrlAttribute()
    {
        return is_null($this->pdf_path) ? null : (is_valid_url($this->pdf_path) ? $this->pdf_path : tenant_asset($this->pdf_path));
    }

    /**
     * @throws Exception
     */
    public function gateway()
    {
        return match ($this->attributes['payment_type']) {
            MyfatoorahPayment::class => app(MyfatoorahGateway::class),
            default => 'unknown',
        };
    }

    // ==========================================
    // Generic Transaction Metadata Methods
    // ==========================================

    /**
     * Get specific metadata value
     *
     * @param string $key The metadata key to retrieve
     * @param mixed $default Default value if key doesn't exist
     * @return mixed
     */
    public function getMetadata(string $key, $default = null)
    {
        return $this->transaction_metadata[$key] ?? $default;
    }

    /**
     * Set specific metadata value
     *
     * @param string $key The metadata key to set
     * @param mixed $value The value to set
     * @return void
     */
    public function setMetadata(string $key, $value): void
    {
        $metadata = $this->transaction_metadata ?? [];
        $metadata[$key] = $value;
        $this->transaction_metadata = $metadata;
    }

    /**
     * Check if metadata key exists
     *
     * @param string $key The metadata key to check
     * @return bool
     */
    public function hasMetadata(string $key): bool
    {
        return isset($this->transaction_metadata[$key]);
    }

    /**
     * Check if transaction is of specific type
     *
     * @param string $type The transaction type to check
     * @return bool
     */
    public function isTransactionType(string $type): bool
    {
        return $this->getMetadata('transaction_type') === $type;
    }

    /**
     * Get all transaction metadata
     *
     * @return array
     */
    public function getAllMetadata(): array
    {
        return $this->transaction_metadata ?? [];
    }

    /**
     * Check if transaction has any metadata
     *
     * @return bool
     */
    public function hasAnyMetadata(): bool
    {
        return !is_null($this->transaction_metadata) && !empty($this->transaction_metadata);
    }

    // ==========================================
    // Transaction Processing Lock Methods
    // ==========================================

    /**
     * Attempt to acquire processing lock for this transaction
     *
     * @param int $lockTimeoutMinutes How long the lock should be held (default: 10 minutes)
     * @return bool True if lock was acquired, false if already locked
     */
    public function acquireProcessingLock(int $lockTimeoutMinutes = 10): bool
    {
        // Safety check: if fields don't exist, return false (migration not run)
        /*if (!Schema::hasColumn('product_transactions', 'processing_lock') ||
            !Schema::hasColumn('product_transactions', 'processing_status')) {
            \Log::warning('Processing lock fields do not exist. Please run the migration.');
            return false;
        }*/

        $lockExpiry = now()->addMinutes($lockTimeoutMinutes);

        // Try to acquire lock using database-level locking
        $updated = static::where('uuid', $this->uuid)
            ->where(function ($query) use ($lockExpiry) {
                $query->whereNull('processing_lock')
                      ->orWhere('processing_lock', '<', now()->subMinutes(10)); // Expired locks
            })
            ->update([
                'processing_lock' => $lockExpiry,
                'processing_status' => 'processing',
            ]);

        if ($updated > 0) {
            $this->refresh();
            return true;
        }

        return false;
    }

    /**
     * Release the processing lock
     *
     * @param string $finalStatus Final status to set (completed, failed, etc.)
     * @return void
     */
    public function releaseProcessingLock(string $finalStatus = 'completed'): void
    {
        // Safety check: if fields don't exist, skip the update
        /*if (!Schema::hasColumn('product_transactions', 'processing_lock') ||
            !Schema::hasColumn('product_transactions', 'processing_status')) {
            \Log::warning('Processing lock fields do not exist. Please run the migration.');
            return;
        }*/

        $this->update([
            'processing_lock' => null,
            'processing_status' => $finalStatus,
        ]);
    }

    /**
     * Check if transaction is currently being processed
     *
     * @return bool
     */
    public function isBeingProcessed(): bool
    {
        // Safety check: if fields don't exist, assume not being processed
        if (!$this->hasAttribute('processing_lock') || !$this->hasAttribute('processing_status')) {
            return false;
        }

        return !is_null($this->processing_lock) &&
               $this->processing_lock > now() &&
               $this->processing_status === 'processing';
    }

    /**
     * Check if transaction processing is completed
     *
     * @return bool
     */
    public function isProcessingCompleted(): bool
    {
        // Safety check: if processing_status field doesn't exist, assume not completed
        if (!$this->hasAttribute('processing_status')) {
            return false;
        }

        return $this->processing_status === 'completed';
    }

    /**
     * Check if transaction processing failed
     *
     * @return bool
     */
    public function isProcessingFailed(): bool
    {
        // Safety check: if processing_status field doesn't exist, assume not failed
        if (!$this->hasAttribute('processing_status')) {
            return false;
        }

        return $this->processing_status === 'failed';
    }



    // ==========================================
    // Query Scopes (Generic)
    // ==========================================

    /**
     * Scope for transactions with metadata
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithMetadata($query)
    {
        return $query->whereNotNull('transaction_metadata');
    }

    /**
     * Scope for transactions without metadata
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithoutMetadata($query)
    {
        return $query->whereNull('transaction_metadata');
    }

    /**
     * Scope for specific transaction type
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, string $type)
    {
        return $query->whereJsonContains('transaction_metadata->transaction_type', $type);
    }


}
