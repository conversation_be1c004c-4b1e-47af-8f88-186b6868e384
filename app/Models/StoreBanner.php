<?php

namespace App\Models;

use Spatie\Activitylog\LogOptions;
use App\Settings\SettingsCasts\DiskUrlCast;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Container\ContainerExceptionInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StoreBanner extends Model
{
    use SoftDeletes, HasFactory, LogsActivity;

    protected $guarded = [];
    protected $appends = ['image_url'];
    protected $casts = [
        'image' => 'json',
        'data' => 'json',
    ];

    public function getImageUrlAttribute()
    {
        try {
            if (!empty($this->getAttribute('image')))
                return (string)app(DiskUrlCast::class)->get($this->getAttribute('image'));
        } catch (\Exception $exception) {
        } catch (NotFoundExceptionInterface $e) {
        } catch (ContainerExceptionInterface $e) {
        }
        return null;
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded()
            ->logExcept(['created_at', 'updated_at'])
            ->dontSubmitEmptyLogs()
            ->logOnlyDirty();
    }
}
