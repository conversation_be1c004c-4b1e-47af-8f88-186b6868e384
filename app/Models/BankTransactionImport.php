<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BankTransactionImport extends Model
{
    use HasUuids, SoftDeletes;

    protected $primaryKey = 'uuid';

    protected $table = 'bank_transaction_import';

    protected $guarded = [];

    protected $casts = [
        'completed_at' => 'datetime',
    ];

    protected $appends = ['formatted_size'];

    public function getFormattedSizeAttribute()
    {
        return formatBytes($this->getAttribute('file_size'));
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function bank_account(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(BankTransactionImportItem::class, 'bank_transaction_import_uuid', 'uuid');
    }
}
