<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BankTransactionImportItem extends Model
{
    protected $guarded = [];
    protected $casts = [
        'due_at' => 'datetime',
        'anonymous' => 'boolean',
    ];

    public function supporter()
    {
        return $this->belongsTo(Supporter::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function bank_transaction_import()
    {
        return $this->belongsTo(BankTransactionImport::class, 'bank_transaction_import_uuid', 'uuid');
    }

    public function transaction()
    {
        return $this->belongsTo(BankAccountTransaction::class, 'transaction_id');
    }
}
