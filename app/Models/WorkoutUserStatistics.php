<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WorkoutUserStatistics extends Model
{
    protected $guarded = [];
    protected $casts = [
        'success' => 'boolean',
        'warn' => 'boolean',
        'fail' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function strava_user()
    {
        return $this->hasOneThrough(
            StravaUser::class,
            WorkoutProgramStatistics::class,
            'id', // Foreign key on the cars table...
            'id', // Foreign key on the owners table...
            'workout_program_statistics_id', // Local key on the mechanics table...
            'strava_user_id' // Local key on the cars table...
        )->withTrashed();
    }

    public function daily_statistics()
    {
        return $this->hasMany(DailyUserStatistics::class);
    }

    public function workout_program_statistics()
    {
        return $this->belongsTo(WorkoutProgramStatistics::class);
    }
}
