<?php

namespace App\Models;

use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Activity extends Model
{
    use HasFactory, LogsActivity, SoftDeletes;

    protected $guarded = [];
    protected $casts = [
        'start_at' => 'date',
        'end_at' => 'date',
        'hide_members_list' => 'boolean',
        'requests_enabled' => 'boolean',
        'requests_allowable_from' => 'datetime',
        'requests_allowable_to' => 'datetime',
        'requests_male' => 'boolean',
        'requests_female' => 'boolean',
        'requests_paid' => 'boolean',
        'invisible' => 'boolean',
        'accessible' => 'boolean',
        'terms_and_conditions' => 'json',
        'terms_and_conditions_required' => 'boolean',
    ];
    protected $appends = ['cover_url', 'cover_thumb_url'];

    public function getCoverUrlAttribute()
    {
        if (!empty($this->getAttribute('cover_path')))
            return tenant_asset("{$this->getAttribute('cover_path')}");
        return null;
    }

    public function getCoverThumbUrlAttribute()
    {
        if (!empty($this->getAttribute('cover_thumb_path')))
            return tenant_asset("{$this->getAttribute('cover_thumb_path')}");
        return null;
    }

    public function getAcceptingRequestsAttribute()
    {
        return $this->requests_enabled && ((
                    is_null($this->requests_allowable_from) &&
                    is_null($this->requests_allowable_to)
                ) || (
                    is_null($this->requests_allowable_from) &&
                    now()->lessThan($this->requests_allowable_to)
                ) || (
                    is_null($this->requests_allowable_to) &&
                    now()->greaterThan($this->requests_allowable_from)
                ) || (
                    now()->greaterThan($this->requests_allowable_from) &&
                    now()->lessThan($this->requests_allowable_to)
                ));
    }

    public function members()
    {
        return $this->hasMany(ActivityUser::class);
    }

    public function sponsors()
    {
        return $this->hasMany(ActivitySponsor::class);
    }

    public function notifications()
    {
        return $this->hasMany(ActivityNotification::class);
    }

    public function requests()
    {
        return $this->hasMany(ActivityRequest::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded()
            ->dontSubmitEmptyLogs()
            ->logOnlyDirty();
    }
}
