<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Staudenmeir\EloquentJsonRelations\HasJsonRelationships;

class EventAllowable extends Model
{
    use HasJsonRelationships;

    protected $table = 'event_allowable';
    protected $guarded = [];
    protected $casts = [
        'guests' => 'json',
        'with_child' => 'boolean',
        'with_nested_child' => 'boolean',
        'males' => 'boolean',
        'females' => 'boolean',
        'with_hosting' => 'boolean',
        'hosting_repeat' => 'boolean',
    ];

    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function member(): MorphTo
    {
        return $this->morphTo('member');
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(EventInvitation::class);
    }

    public function users()
    {
        return $this->belongsTo<PERSON>son(User::class, 'guests');
    }
}
