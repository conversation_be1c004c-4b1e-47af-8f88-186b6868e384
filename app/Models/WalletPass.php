<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WalletPass extends Model
{
    use SoftDeletes;

    protected $guarded = [];
    protected $casts = [
        'expired_at' => 'datetime',
        'content' => 'json',
    ];

    public function owner()
    {
        return $this->morphTo('owner');
    }

    public function devices()
    {
        return $this->hasMany(WalletDevice::class);
    }
}
