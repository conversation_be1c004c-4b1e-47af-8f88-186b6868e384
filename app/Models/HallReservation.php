<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Observers\HallReservationObserver;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;

#[ObservedBy([HallReservationObserver::class])]
class HallReservation extends Model
{
    use SoftDeletes, HasFactory;

    protected $guarded = [];
    protected $casts = [
        'start_at' => 'datetime',
        'end_at' => 'datetime',
        'notify_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class)->withoutGlobalScopes(['family_users'])->withTrashed();
    }

    public function created_by()
    {
        return $this->belongsTo(User::class, 'created_by_id')->withTrashed();
    }

    public function services()
    {
        return $this->belongsToMany(Service::class)->withTrashed()->withPivot(['notes']);
    }

    public function getEventTypeLocaleAttribute()
    {
        return trans("hallReservationColumns.event_type.{$this->attributes['event_type']}");
    }

    public function getStatusLocaleAttribute()
    {
        return trans("hallReservationColumns.status.{$this->attributes['status']}");
    }

    public function approved_by()
    {
        return $this->belongsTo(User::class, 'approved_by_id')->withTrashed();
    }
}
