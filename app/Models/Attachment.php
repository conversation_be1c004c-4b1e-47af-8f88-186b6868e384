<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Attachment extends Model
{
    use SoftDeletes;

    protected $fillable = ['user_id', 'type', 'file_type', 'title', 'path', 'thumb_path'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function owner(): MorphTo
    {
        return $this->morphTo('owner');
    }

    public function getFileTitleAttribute()
    {
        switch ($this->getAttribute('type')) {
            case 'custom':
                return $this->getAttribute('title');
            default:
                return trans('attachments.types.' . $this->getAttribute('type'));
        }
    }

    public function getUrlAttribute()
    {
        $path = $this->getAttribute('path');
        if (is_null($path)) {
            return null;
        }

        try {
            // If it's already a full URL, return as is
            if (is_valid_url($path)) {
                return $path;
            }

            // Handle paths with "tenancy/assets/" prefix (new format)
            if (str_starts_with($path, 'tenancy/assets/')) {
                $cleanPath = str_replace('tenancy/assets/', '', $path);

                // Generate URL for storage path using tenant_asset if available
                if (function_exists('tenant_asset')) {
                    return tenant_asset($cleanPath);
                }

                return asset('storage/' . $cleanPath);
            }

            // Handle legacy paths without prefix (backward compatibility)
            // These are direct storage paths like "documents/filename.ext"
            if (function_exists('tenant_asset')) {
                return tenant_asset($path);
            }

            return asset('storage/' . $path);

        } catch (\Exception $e) {
            \Log::error("Error generating URL for attachment path '{$path}': " . $e->getMessage());
            return null;
        }
    }

    public function getThumbUrlAttribute()
    {
        if ($this->getAttribute('file_type') !== 'image') {
            return '';
        }

        $thumbPath = $this->getAttribute('thumb_path');
        if (is_null($thumbPath)) {
            return '';
        }

        try {
            // If it's already a full URL, return as is
            if (is_valid_url($thumbPath)) {
                return $thumbPath;
            }

            // Handle paths with "tenancy/assets/" prefix (new format)
            if (str_starts_with($thumbPath, 'tenancy/assets/')) {
                $cleanPath = str_replace('tenancy/assets/', '', $thumbPath);

                // Generate URL for storage path using tenant_asset if available
                if (function_exists('tenant_asset')) {
                    return tenant_asset($cleanPath);
                }

                return asset('storage/' . $cleanPath);
            }

            // Handle legacy paths without prefix (backward compatibility)
            // These are direct storage paths like "documents/thumbs/filename.ext"
            if (function_exists('tenant_asset')) {
                return tenant_asset($thumbPath);
            }

            return asset('storage/' . $thumbPath);

        } catch (\Exception $e) {
            \Log::error("Error generating thumb URL for attachment path '{$thumbPath}': " . $e->getMessage());
            return '';
        }
    }
}
