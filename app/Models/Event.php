<?php

namespace App\Models;

use App\Enums\Gender;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;
use Staudenmeir\EloquentJsonRelations\HasJsonRelationships;

class Event extends Model
{
    use SoftDeletes, HasJsonRelationships, HasRelationships;

    protected $guarded = [];
    protected $casts = [
        'start_at' => 'datetime',
        'end_at' => 'datetime',
        'self_checkin' => 'boolean',
        'responsible_users' => 'json',
    ];

    protected $appends = ['cover_url', 'cover_thumb_url'];

    public function getCoverUrlAttribute()
    {
        if (!empty($this->getAttribute('cover_path')))
            return tenant_asset("{$this->getAttribute('cover_path')}");
        return null;
    }

    public function getCoverThumbUrlAttribute()
    {
        if (!empty($this->getAttribute('cover_thumb_path')))
            return tenant_asset("{$this->getAttribute('cover_thumb_path')}");
        return null;
    }

    public function invitations()
    {
        return $this->hasMany(EventInvitation::class);
    }

    public function invited_users()
    {
        return $this->hasManyThrough(User::class, EventInvitation::class, 'event_id', 'id', 'id', 'user_id');
    }

    public function responsible_users_list()
    {
        return $this->belongsToJson(User::class, 'responsible_users');
    }

    public function attendances()
    {
        return $this->hasMany(EventAttendance::class);
    }

    public function notifications()
    {
        return $this->morphMany(BulkNotification::class, 'owner');
    }

    public function poll_notifications()
    {
        return $this->morphMany(BulkNotification::class, 'owner')
            ->where('type', 'poll');
    }

    public function attendance_users()
    {
        return $this->hasManyThrough(User::class, EventAttendance::class, 'event_id', 'id', 'id', 'user_id');
    }

    public function getIsRunningAttribute()
    {
        return (new Carbon(now()->timezone("Asia/Riyadh")->toDateTimeString()))
            ->between($this->getAttribute('start_at'), $this->getAttribute('end_at'));
    }

    public function getIsPastAttribute()
    {
        return (new Carbon(now()->timezone("Asia/Riyadh")->toDateTimeString()))
            ->isAfter($this->getAttribute('end_at'));
    }

    public function allowable()
    {
        return $this->hasMany(EventAllowable::class);
    }

    public function guests()
    {
        return $this->hasManyThroughJson(User::class, EventAllowable::class, 'event_id', 'id', 'id', 'guests');
    }

    public function isUserAllowable(User $user)
    {
        return $this->allowable()->whereJsonContains('guests', $user->id)->exists() ||
            $this->allowable()->where('type', 'ALL')->where($user->gender === Gender::Male ? 'males' : 'females', true)->exists();
    }

    public function isUserInvited(User $user)
    {
        return $this->invitations()->where('user_id', $user->id)->exists();
    }

    public function getUserInvitation(User $user)
    {
        return $this->invitations()->where('user_id', $user->id)->first();
    }

    public function getAllowableForUser(User $user)
    {
        return $this->allowable()->whereJsonContains('guests', $user->id)->first() ??
            $this->allowable()->where('type', 'ALL')
                ->where($user->gender === Gender::Male ? 'males' : 'females', true)->first();
    }
}
