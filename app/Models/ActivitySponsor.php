<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ActivitySponsor extends Model
{
    use HasFactory;

    public $timestamps = null;
    protected $guarded = [];
    protected $casts = ['confidential' => 'boolean'];

    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }

    public function sponsor()
    {
        return $this->morphTo('sponsor');
    }
}
