<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HelpFormTemplate extends Model
{
    protected $guarded = [];

    protected $casts = [
        'active' => 'bool',
        'limit' => 'bool',
        'is_required_fields' => 'json',
        'is_enabled_fields' => 'json',
        'family_details_required' => 'bool',
        'has_invoices' => 'bool',
    ];

    public function sections()
    {
        return $this->hasMany(HelpFormTemplateSection::class);
    }

    public function sections_items()
    {
        return $this->hasManyThrough(HelpFormTemplateItem::class, HelpFormTemplateSection::class);
    }

    public function help_forms()
    {
        return $this->hasMany(HelpForm::class);
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeOnlyActive(Builder $query): void
    {
        $query->where('active', true);
    }

    public function getRequiredFieldsAttribute()
    {
        return optional((object)$this->getAttribute('is_required_fields'));
    }

    public function getEnabledFieldsAttribute()
    {
        return optional((object)$this->getAttribute('is_enabled_fields'));
    }
}
