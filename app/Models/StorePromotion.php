<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StorePromotion extends Model
{
    use SoftDeletes, HasFactory;

    protected $guarded = [];
    
    protected $casts = [
        'banners' => 'array',
    ];

    public function interests()
    {
        return $this->hasMany(StorePromotionInterest::class, 'store_promotion_id');
    }

    public function getLogoUrlAttribute()
    {
        try {
            if (!empty($this->getAttribute('logo_path')))
                return tenant_asset("{$this->getAttribute('logo_path')}");
        } catch (\Exception $exception) {
        }
        return null;
    }
    
    public function getBannerUrlsAttribute()
    {
        if (empty($this->banners)) {
            return [];
        }
        
        return collect($this->banners)->map(function ($banner) {
            try {
                return tenant_asset($banner);
            } catch (\Exception $exception) {
                return null;
            }
        })->filter()->values()->toArray();
    }
}
