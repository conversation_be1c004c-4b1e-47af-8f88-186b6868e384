<?php

namespace App\Models;

use App\Enums\Gender;
use App\Enums\ResidenceType;
use App\Enums\MarriageStatus;
use App\Scopes\DateBetweenScope;
use App\Enums\FamilyRelationType;
use App\Settings\GeneralSettings;
use App\Observers\HelpFormObserver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;

#[ObservedBy([HelpFormObserver::class])]
class HelpForm extends Model
{
    use HasUuids, SoftDeletes, DateBetweenScope;

    protected $primaryKey = 'uuid';
    protected $guarded = [];
    protected $appends = ['payment_receipt_url'];
    protected $casts = [
        'completed_at' => 'datetime',
        'closed_at' => 'datetime',
        'payment_at' => 'date:Y-m-d',
        'residence_services' => 'array',
        'data' => 'array',
        'is_employee' => 'bool',
        'has_social_support' => 'bool',
        'has_citizen_account' => 'bool',
        'breadwinner_required' => 'bool',
    ];

    public function callbackIsRentResidence()
    {
        return $this->getAttribute('residence') === ResidenceType::Rent;
    }

    public function callbackIsEmployee()
    {
        return $this->getAttribute('is_employee') === true;
    }

    public function callbackHasDebts()
    {
        return $this->getAttribute('debts') > 0;
    }

    public function _calculated_restricted_value()
    {
        $region = $this->user_region;
        $calculator = $region?->calculator;
        if ($calculator && !empty($calculator->restricted_values)) {
            $restricted_values = $calculator->restricted_values;
        } else {
            $restricted_values = [
                0,      // not reachable
                3300,   //1
                3800,   //2
                4300,   //3
                4800,   //4
                5100,   //5
                5400,   //6
                5700,   //7
                6000,   //8
                6350,   //9
                6600,   //10
                6850,   //11
                7100,   //12
                7350,   //13
                7600,   //14
                7850,   //15
                8100,   //16
                8350,   //17
                8550,   //18
                8850,   //19
                9250,   //20
            ];
        }

        if ($this->user->gender === Gender::Male)
            $count = 1 +
                $this->family_members()->active()->whereHas('member', fn($q) => $q->whereNull('dod')->where('is_dead', false))->where('relation', FamilyRelationType::Child)->count() +
                $this->user->wives()->whereHas('wife', fn($q) => $q->whereNull('dod')->where('is_dead', false))->where('status', MarriageStatus::Active)->count();
        else
            $count = 1 +
                $this->family_members()->active()->whereHas('member', fn($q) => $q->whereNull('dod')->where('is_dead', false))->where('relation', FamilyRelationType::Child)->count() +
                $this->user->husbands()->whereHas('husband', fn($q) => $q->whereNull('dod')->where('is_dead', false))->where('status', MarriageStatus::Active)->count();
        return max($restricted_values[$count] ?? $restricted_values[20], 0);

    }

    public function family_members()
    {
        return $this->hasMany(HelpFormFamilyMember::class, 'help_form_uuid');
    }

    public function active_family_members()
    {
        return $this->hasMany(HelpFormFamilyMember::class, 'help_form_uuid')
            ->where('is_active', true);
    }

    public function _calculated_income_value()
    {
        $income = $this->getAttribute('salary') + $this->getAttribute('social_support') +
            $this->getAttribute('citizen_account') + $this->getAttribute('spouses_salary');
        switch ($this->template->type) {
            case 'ZAKAT_SUPPORT':
                $allowed_debts = 0;
                if ($this->debts > 0)
                    $allowed_debts += intval($income * 0.33);
                $generalSettings = app(GeneralSettings::class);
                if ($generalSettings->zakatType === 'PERCENTAGE')
                    $allowed_debts += intval($income * ($generalSettings->zakatValue / 100));
                elseif ($generalSettings->zakatType === 'STATIC')
                    $allowed_debts += intval($generalSettings->zakatValue);
                $final = intval($income - $allowed_debts);
                return max($final, 0);
            default:
                return 0;
        }
    }

    public function _calculated_annual_helping()
    {
        $region = $this->user_region;
        $calculator = $region?->calculator;
        $perCase = $calculator?->per_case ?: 5400;
        $maleChildren = $calculator?->male_children ?: 1800;
        $femaleChildren = $calculator?->female_children ?: 1800;
        $wivesValue = $calculator?->wives ?: 1800;
        $familyHusband = $calculator?->family_husband ?: 1800;
        $nonFamilyHusband = $calculator?->non_family_husband ?: 1800;
        $rentValue = $calculator?->rent ?: 1000;
        $debitValue = $calculator?->debit ?: 1000;

        $value = 0;
        $caption = [
            $perCase . ' رب الأسرة'
        ];
        $value += $perCase;
        $childCount = $this->family_members()->active()
            ->when($this->breadwinner_id, fn($q) => $q->where('member_id', '!=', $this->breadwinner_id))
            ->whereHas('member', fn($q) => $q->whereNull('family_title_id')->whereNull('dod')->where('is_dead', false))
            ->where('relation', FamilyRelationType::Child)->count();
        if ($childCount > 0) {
            $value += $childCount * $maleChildren;
            $caption[] = $childCount . ' أطفال بمقدار ' . ($childCount * $maleChildren);
        }
        if ($this->user->gender === Gender::Male) {
            $liveWivesCount = $this->user->wives()
                ->when($this->breadwinner_id, fn($q) => $q->where('wife_id', '!=', $this->breadwinner_id))
                ->whereHas('wife', fn($q) => $q->whereNull('dod')->where('is_dead', false))
                ->where('status', MarriageStatus::Active)->count();
            if ($liveWivesCount > 0) {
                if ($this->user->is_user_dead)
                    $liveWivesCount -= 1;
                $value += $liveWivesCount * $wivesValue;
                if ($liveWivesCount > 0)
                    $caption[] = $liveWivesCount . ' زوجات بمقدار ' . ($liveWivesCount * $wivesValue);
            }
        } else {
            if (!is_null($this->user->active_husband) && $this->user->active_husband->husband->is_family && (
                    is_null($this->breadwinner_id) ||
                    $this->breadwinner_id !== $this->user->active_husband->husband->id
                )) {
                $value += $familyHusband;
                $caption[] = 'دعم الزوج من داخل العائلة بمقدار ' . $familyHusband;
            }
        }
        if ($this->debts > 0) {
            $value += $debitValue;
            $caption[] =  'دعم الديون ' . $debitValue;
        }
        if ($this->residence === ResidenceType::Rent) {
            $value += $rentValue;
            $caption[] = 'دعم الإيجار ' . $rentValue;
        }
        return [
            'value' => max($value, 0),
            'caption' => implode(' | ', $caption),
        ];
    }

    public function template()
    {
        return $this->belongsTo(HelpFormTemplate::class, 'help_form_template_id');
    }

    public function file_items()
    {
        return $this->items()->where('type', 'FILE');
    }

    public function items()
    {
        return $this->hasMany(HelpFormItem::class, 'help_form_uuid');
    }

    public function multiple_file_items()
    {
        return $this->items()->where('type', 'MULTIPLE_FILE');
    }

    public function user_region()
    {
        return $this->belongsTo(UserRegion::class, 'user_region_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id')->withTrashed();
    }

    public function breadwinner()
    {
        return $this->belongsTo(User::class, 'breadwinner_id')
            ->withTrashed()
            ->withoutGlobalScopes(['family_users']);
    }

    public function created_by()
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public function responsible_user()
    {
        return $this->belongsTo(User::class, 'responsible_user_id');
    }

    public function invoices()
    {
        return $this->hasMany(HelpFormInvoice::class, 'help_form_uuid');
    }

    /**
     * get a list of comments added
     */
    public function comments()
    {
        return $this->hasMany(HelpFormComment::class, 'help_form_uuid');
    }

    public function old_comments()
    {
        return $this->hasMany(HelpFormComment::class, 'help_form_uuid')->whereNull('status');
    }

    /**
     * get a list of reasons added
     */
    public function reasons()
    {
        return $this->hasMany(HelpFormComment::class, 'help_form_uuid')->whereNotNull('status');
    }

    /**
     * get a list of shares
     */
    public function shares()
    {
        return $this->hasMany(HelpFormShare::class, 'help_form_uuid');
    }

    /**
     * get family case audits
     *
     * @return MorphMany
     */
    public function audits(): MorphMany
    {
        return $this->morphMany(ModelAudit::class, 'auditable');
    }

    public function getPaymentReceiptUrlAttribute(): string|null
    {
        $path = $this->getAttribute('payment_receipt_path');
        if ($path)
            return tenant_asset($path);
        return null;
    }

    public function filamentComments(): HasMany
    {
        return $this
            ->hasMany(HelpFormComment::class, 'help_form_uuid')
            ->latest();
    }
}
