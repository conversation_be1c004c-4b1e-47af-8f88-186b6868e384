<?php

namespace App\Models;

use App\Enums\StatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class QuranChallenge extends Model
{
    use LogsActivity;

    protected $guarded = [];

    public function competition()
    {
        return $this->belongsTo(QuranCompetition::class, 'quran_competition_id');
    }

    public function activity_member_role()
    {
        return $this->belongsTo(ActivityMemberRole::class);
    }

    public function users()
    {
        return $this->hasMany(QuranUser::class);
    }

    public function requests()
    {
        return $this->hasMany(QuranUser::class)->whereNull('status');
    }

    public function accepted_users()
    {
        return $this->hasMany(QuranUser::class)->where('status', StatusEnum::Approved);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded()
            ->logExcept(['created_at', 'updated_at'])
            ->dontSubmitEmptyLogs()
            ->logOnlyDirty();
    }
}
