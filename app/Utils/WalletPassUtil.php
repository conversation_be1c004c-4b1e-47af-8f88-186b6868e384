<?php

namespace App\Utils;

use ArPHP\I18N\Arabic;
use BaconQrCode\Writer;
use GeniusTS\HijriDate\Hijri;
use App\Settings\AppSettings;
use Intervention\Image\ImageManager;
use BaconQrCode\Renderer\ImageRenderer;
use Intervention\Image\Typography\FontFactory;
use BaconQrCode\Renderer\Image\ImagickImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use App\Http\Resources\PassDefinitions\EventInvitationResource;
use App\Models\EventInvitation;
use App\Models\WalletPass;
use Illuminate\Support\Facades\Log;
use Str;
use Thenextweb\PassGenerator;

class WalletPassUtil extends UserWalletPassUtil
{
    public function passResponse(WalletPass $walletPass)
    {
        if ($walletPass->owner instanceof EventInvitation)
            return self::eventInvitationPassResponse($walletPass->owner);
        return null;
    }

    public function eventInvitationPassResponse(EventInvitation $eventInvitation)
    {
        $pass = self::eventInvitationPass($eventInvitation);
        $pkPass = $pass->create();
        return response($pkPass, 200, [
            'Content-Transfer-Encoding' => 'binary',
            'Content-Description' => 'File Transfer',
            'Content-Disposition' => 'attachment; filename="' . $eventInvitation->uuid . '.pkpass"',
            'Content-length' => strlen($pkPass),
            'Content-Type' => PassGenerator::getPassMimeType(),
            'Pragma' => 'no-cache',
        ]);
    }

    public function eventInvitationPass(EventInvitation $eventInvitation)
    {
        $passIdentifier = "EventInvitation-{$eventInvitation->uuid}";  // This, if set, it would allow for retrieval later on of the created Pass
        $authenticationToken = $eventInvitation->pass?->auth_token ?: Str::random(64);
        if ($eventInvitation->pass)
            $serialNumber = $eventInvitation->pass->serial;
        else
            do {
                $serialNumber = Str::random(16);
            } while (
                WalletPass::query()
                ->withTrashed()
                ->where('serial', $serialNumber)->exists()
            );


        $pass = new PassGenerator($passIdentifier, true);
        $passDefinition = json_decode((new EventInvitationResource($eventInvitation, $serialNumber, $authenticationToken))->toJson(), true);
        if ($eventInvitation->pass)
            $eventInvitation->pass->update([
                'content' => $passDefinition,
                'expired_at' => $eventInvitation->expired_at,
            ]);
        else
            $eventInvitation->pass()->create([
                'pass_identifier' => $passIdentifier,
                'serial' => $serialNumber,
                'content' => $passDefinition,
                'expired_at' => $eventInvitation->expired_at,
            ]);
        $pass->setPassDefinition($passDefinition);

        $pass->addAsset(storage_path('app/wallet/assets/logo.png'), 'icon.png');
        //$pass->addAsset(storage_path('app/wallet/assets/logo.png'), '<EMAIL>');

        $pass->addAsset(storage_path('app/wallet/assets/logo.png'));
        //$pass->addAsset(storage_path('app/wallet/assets/<EMAIL>'));

        $pass->addAsset(storage_path('app/wallet/assets/background.png'));
        $pass->addAsset(storage_path('app/wallet/assets/<EMAIL>'));
        $pass->addAsset(storage_path('app/wallet/assets/<EMAIL>'));

        /*if ($eventInvitation->user->profile_photo_path) {
            $pass->addAsset(Storage::path("public/user-pic/{$eventInvitation->user->profile_photo_path}"), 'thumbnail.png');
            $pass->addAsset(Storage::path("public/user-pic/{$eventInvitation->user->profile_photo_path}"), '<EMAIL>');
            $pass->addAsset(Storage::path("public/user-pic/{$eventInvitation->user->profile_photo_path}"), '<EMAIL>');
        }*/

        return $pass;
    }

    public function pass(WalletPass $walletPass)
    {
        if ($walletPass->owner instanceof EventInvitation)
            return self::eventInvitationPass($walletPass->owner);
        return null;
    }

    public function eventInvitationGooglePassResponse(EventInvitation $eventInvitation)
    {
        $pass = self::eventInvitationPass($eventInvitation);
        $pkPass = $pass->create();
        return response($pkPass, 200, [
            'Content-Transfer-Encoding' => 'binary',
            'Content-Description' => 'File Transfer',
            'Content-Disposition' => 'attachment; filename="' . $eventInvitation->uuid . '.pkpass"',
            'Content-length' => strlen($pkPass),
            'Content-Type' => PassGenerator::getPassMimeType(),
            'Pragma' => 'no-cache',
        ]);
    }

    public function eventInvitationPassAsImage(EventInvitation $eventInvitation)
    {
        Log::info(storage_path('app/wallet/assets/event.jpg'));

        $renderer = new ImageRenderer(
            new RendererStyle(350),
            new ImagickImageBackEnd()
        );
        $writer = new Writer($renderer);
        $qrCode = $writer->writeString("EventInvitation|{$eventInvitation->uuid}");

        $manager = ImageManager::gd();
        $image = $manager->read(storage_path('app/wallet/assets/event.jpg'));
        //$image->resize(1024, 1640);
        $titleColor = app(AppSettings::class)->primaryColor ?? '#FF000000';

        $Arabic = (new Arabic());
        $fontPath = \Storage::disk('central')->path('fonts/Tajawal-Bold.ttf');
        $_fontFile = fn(FontFactory $font) => $font->file($fontPath)->size(54)->color($titleColor)->lineHeight(2);
        $fontFile = fn(FontFactory $font) => $_fontFile($font)->align('right');

        $title = $Arabic->utf8Glyphs($eventInvitation->event->title, max_chars: 25, forcertl: true);
        $image->text($title, 1024 / 2, Str::contains($title, "\n") ? 450 : 400, fn($font) => $_fontFile($font)->align('center')->color($titleColor)->size(72));

        $_guestName = $eventInvitation->user?->getFullName(1, true, false) ?: $eventInvitation->guest_name;
        //$guestName = null;
        $guestName = $_guestName ? $Arabic->utf8Glyphs($_guestName, max_chars: 30, forcertl: true) : '';
        if (!is_null($eventInvitation->host_user)) {
            // add host and guest name
            //$guestName = $_guestName ? $Arabic->utf8Glyphs(($Arabic->isFemale($_guestName) ? 'ضيفتنا' : 'ضيفنا') . '/ ' . $_guestName, max_chars: 30, forcertl: true) : '';
            $hostUserName = $eventInvitation->host_user->getFullName(1, true, false);
            $image->text(
                (
                    $Arabic->utf8Glyphs($hostUserName)
                    . ' /' .
                    $Arabic->utf8Glyphs(!empty($hostUserName) && $Arabic->isFemale($hostUserName) ? 'ضيفة' : 'ضيف')
                ),
                1024 / 2,
                1700 - 200,
                fn($font) => $fontFile($font)->size(48)->align('center')
            );
        } else if ($eventInvitation->user) {
            // add invited user name
            //$guestName = $_guestName ? $Arabic->utf8Glyphs($_guestName, max_chars: 30, forcertl: true) : '';
        }
        if ($guestName)
            $image->text(
                $guestName,
                1024 / 2,
                (Str::contains($guestName, "\n") ? 735 : 700),
                fn($font) => $fontFile($font)->size(64)->align('center')
            );

        $location = $Arabic->utf8Glyphs($eventInvitation->event->location_description, max_chars: 15, forcertl: true);
        $image->text($location, 820, (Str::contains($location, "\n") ? 1125 : 1075) - 200, fn($font) => $fontFile($font)->align('center'));

        $dataG = $Arabic->utf8Glyphs('م') . ' ' . $eventInvitation->event->start_at->format('Y/m/d');
        $dataH = $Arabic->utf8Glyphs('هـ') . ' ' . Hijri::convertToHijri(now()->toDateString())->format('Y/m/d');
        $image->text($dataH, 350, 1050 - 200, fn($font) => $fontFile($font));
        $image->text($dataG, 350, 1125 - 200, fn($font) => $fontFile($font));

        $image->place($manager->read($qrCode), 'top', 0, 1175 - 200);

        return $image->toPng();
    }
}
