<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;

trait DateBetweenScope
{
    /**
     * Scope a query to only include the last n days records
     *
     * @param Builder $query
     * @param $fieldName
     * @param $fromDate
     * @param $toDate
     * @return Builder
     */
    public function scopeWhereDateBetween(Builder $query, $fieldName, $fromDate, $toDate): Builder
    {
        return $query->where(fn($_q) => $_q->whereDate($fieldName, '>=', $fromDate)
            ->whereDate($fieldName, '<=', $toDate));
    }

    /**
     * Scope a query to only include the last n days records
     *
     * @param Builder $query
     * @param $fieldName
     * @param $fromDate
     * @param $toDate
     * @return Builder
     */
    public function scopeOrWhereDateBetween(Builder $query, $fieldName, $fromDate, $toDate): Builder
    {
        return $query->orWhere(fn($_q) => $_q->whereDate($fieldName, '>=', $fromDate)
            ->whereDate($fieldName, '<=', $toDate));
    }
}

