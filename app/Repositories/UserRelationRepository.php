<?php

namespace App\Repositories;

use App\Models\User;
use App\Enums\Gender;
use Illuminate\Support\Collection;

class UserRelationRepository
{
    public function findRelationBetweenUsers(User $user1, User $user2)
    {
        // Check if users are the same
        if ($user1->id === $user2->id) {
            return ['relation' => 'same_user', 'description' => 'Same Person'];
        }

        // Direct parent-child relationship
        if ($user1->id === $user2->father_id) {
            return [
                'relation' => 'parent_child',
                'description' => $user2->gender === Gender::Male
                    ? 'Father of User'
                    : 'Mother of User'
            ];
        }

        if ($user2->id === $user1->father_id) {
            return [
                'relation' => 'child',
                'description' => $user1->gender === Gender::Male
                    ? 'Son'
                    : 'Daughter'
            ];
        }

        // Siblings check
        $siblings = $this->findSiblingRelation($user1, $user2);
        if ($siblings) {
            return $siblings;
        }

        // Spouse check
        $spouse = $this->findSpouseRelation($user1, $user2);
        if ($spouse) {
            return $spouse;
        }

        // Grandparent check
        $grandparent = $this->findGrandparentRelation($user1, $user2);
        if ($grandparent) {
            return $grandparent;
        }

        // Uncle/Aunt check
        $uncle = $this->findUncleAuntRelation($user1, $user2);
        if ($uncle) {
            return $uncle;
        }

        // Cousin check
        $cousin = $this->findCousinRelation($user1, $user2);
        if ($cousin) {
            return $cousin;
        }

        // No direct relation found
        return null;
    }

    private function findSiblingRelation(User $user1, User $user2)
    {
        // Check if they have the same father
        if ($user1->father_id && $user1->father_id === $user2->father_id) {
            return [
                'relation' => 'sibling',
                'description' => $user1->gender === Gender::Male
                    ? ($user2->gender === Gender::Male ? 'Brother' : 'Brother of Sister')
                    : ($user2->gender === Gender::Male ? 'Sister of Brother' : 'Sister')
            ];
        }

        // Check if they have the same mother but different fathers
        if ($user1->mother_id && $user1->mother_id === $user2->mother_id && $user1->father_id !== $user2->father_id) {
            return [
                'relation' => 'half_sibling',
                'description' => $user1->gender === Gender::Male
                    ? ($user2->gender === Gender::Male ? 'Half-Brother' : 'Half-Brother of Sister')
                    : ($user2->gender === Gender::Male ? 'Half-Sister of Brother' : 'Half-Sister')
            ];
        }

        return null;
    }

    private function findSpouseRelation(User $user1, User $user2)
    {
        // Check if user1 is husband of user2
        $husbandWife1 = $user1->wives()->where('wife_id', $user2->id)->first();
        if ($husbandWife1) {
            return [
                'relation' => 'spouse',
                'description' => 'Husband'
            ];
        }

        // Check if user1 is wife of user2
        $husbandWife2 = $user1->husbands()->where('husband_id', $user2->id)->first();
        if ($husbandWife2) {
            return [
                'relation' => 'spouse',
                'description' => 'Wife'
            ];
        }

        return null;
    }

    private function findGrandparentRelation(User $user1, User $user2)
    {
        // Check if user1 is grandparent of user2
        if ($user1->id === $user2->father->father_id) {
            return [
                'relation' => 'grandparent',
                'description' => $user1->gender === Gender::Male ? 'Grandfather' : 'Grandmother'
            ];
        }

        // Check if user2 is grandparent of user1
        if ($user2->id === $user1->father->father_id) {
            return [
                'relation' => 'grandchild',
                'description' => $user1->gender === Gender::Male ? 'Grandson' : 'Granddaughter'
            ];
        }

        return null;
    }

    private function findUncleAuntRelation(User $user1, User $user2)
    {
        // Check if user1 is uncle/aunt of user2
        if ($user1->father_id && $user2->father && $user1->father_id === $user2->father->father_id) {
            return [
                'relation' => 'uncle_aunt',
                'description' => $user1->gender === Gender::Male ? 'Paternal Uncle' : 'Paternal Aunt'
            ];
        }

        // Check if user2 is uncle/aunt of user1
        if ($user2->father_id && $user1->father && $user2->father_id === $user1->father->father_id) {
            return [
                'relation' => 'nephew_niece',
                'description' => $user1->gender === Gender::Male ? 'Nephew of Uncle' : 'Niece of Uncle'
            ];
        }

        // Check maternal uncle/aunt
        if ($user1->father_id && $user2->mother && $user1->father_id === $user2->mother->father_id) {
            return [
                'relation' => 'uncle_aunt',
                'description' => $user1->gender === Gender::Male ? 'Maternal Uncle' : 'Maternal Aunt'
            ];
        }

        return null;
    }

    private function findCousinRelation(User $user1, User $user2)
    {
        // Check if they have different fathers but grandfathers are the same
        if ($user1->father && $user2->father &&
            $user1->father->father_id && $user2->father->father_id &&
            $user1->father->father_id === $user2->father->father_id &&
            $user1->father_id !== $user2->father_id
        ) {
            return [
                'relation' => 'cousin',
                'description' => $user1->gender === Gender::Male
                    ? ($user2->gender === Gender::Male ? 'Male Cousin' : 'Cousin of Female')
                    : ($user2->gender === Gender::Male ? 'Female Cousin of Male' : 'Female Cousin')
            ];
        }

        return null;
    }

    /**
     * Find all users between two specific users in the family tree
     *
     * @param User $startUser
     * @param User $endUser
     * @return Collection
     */
    public function findUsersBetween(User $startUser, User $endUser): Collection
    {
        $usersBetween = collect();

        // Find the paths to common ancestors
        $startAncestors = $this->getAllAncestors($startUser);
        $endAncestors = $this->getAllAncestors($endUser);

        // Find the common ancestors
        $commonAncestors = $startAncestors->intersect($endAncestors);

        // If no common ancestors, return empty collection
        if ($commonAncestors->isEmpty()) {
            return $usersBetween;
        }

        // Get the closest common ancestor
        $closestCommonAncestor = $commonAncestors->first();

        // Trace path from start user to common ancestor
        $currentUser = $startUser;
        while ($currentUser->id !== $closestCommonAncestor->id) {
            // Move up the family tree
            $parent = $this->getParent($currentUser);
            if (!$parent) break;

            $usersBetween->push($parent);
            $currentUser = $parent;
        }

        // Trace path from end user to common ancestor
        $currentUser = $endUser;
        while ($currentUser->id !== $closestCommonAncestor->id) {
            // Move up the family tree
            $parent = $this->getParent($currentUser);
            if (!$parent) break;

            $usersBetween->push($parent);
            $currentUser = $parent;
        }

        // Remove duplicates and sort by some criteria (e.g., generation)
        return $usersBetween->unique('id');
    }

    /**
     * Find all grandparents for a given user
     *
     * @param User $user
     * @return Collection
     */
    public function findAllGrandparents(User $user): Collection
    {
        $grandparents = collect();

        // Paternal Grandparents
        if ($user->father && $user->father->father) {
            $grandparents->push([
                'user' => $user->father->father,
                'relation' => $user->father->father->gender === Gender::Male ? 'Paternal Grandfather' : 'Paternal Grandmother'
            ]);
        }

        // Paternal Grandparents' Spouse
        if ($user->father && $user->father->father) {
            $paternalGrandparentSpouse = $this->findSpouse($user->father->father);
            if ($paternalGrandparentSpouse) {
                $grandparents->push([
                    'user' => $paternalGrandparentSpouse,
                    'relation' => $paternalGrandparentSpouse->gender === Gender::Male ? 'Paternal Grandfather' : 'Paternal Grandmother'
                ]);
            }
        }

        // Maternal Grandparents
        if ($user->mother && $user->mother->father) {
            $grandparents->push([
                'user' => $user->mother->father,
                'relation' => $user->mother->father->gender === Gender::Male ? 'Maternal Grandfather' : 'Maternal Grandmother'
            ]);
        }

        // Maternal Grandparents' Spouse
        if ($user->mother && $user->mother->father) {
            $maternalGrandparentSpouse = $this->findSpouse($user->mother->father);
            if ($maternalGrandparentSpouse) {
                $grandparents->push([
                    'user' => $maternalGrandparentSpouse,
                    'relation' => $maternalGrandparentSpouse->gender === Gender::Male ? 'Maternal Grandfather' : 'Maternal Grandmother'
                ]);
            }
        }

        return $grandparents;
    }

    /**
     * Get the parent of a user
     *
     * @param User $user
     * @return User|null
     */
    private function getParent(User $user)
    {
        return $user->father ?? $user->mother;
    }

    /**
     * Get all ancestors of a user
     *
     * @param User $user
     * @return Collection
     */
    private function getAllAncestors(User $user): Collection
    {
        $ancestors = collect([$user]);
        $currentUser = $user;

        while ($parent = $this->getParent($currentUser)) {
            $ancestors->push($parent);
            $currentUser = $parent;
        }

        return $ancestors;
    }

    /**
     * Find spouse of a user
     *
     * @param User $user
     * @return User|null
     */
    private function findSpouse(User $user)
    {
        // Check wives
        $wife = $user->wives()->first();
        if ($wife) return $wife;

        // Check husbands
        $husband = $user->husbands()->first();
        if ($husband) return $husband;

        return null;
    }



    public function findRelationBetweenUsersV2(User $user1, User $user2)
    {
        // Check if users are the same
        if ($user1->id === $user2->id) {
            return [
                'relation' => 'same_user',
                'description' => 'Same Person',
                'users_between' => [],
                'relation_path' => []
            ];
        }

        // Existing relation checks with added users_between and relation_path
        $relationChecks = [
            $this->findDirectParentChildRelation($user1, $user2),
            $this->findSiblingRelation($user1, $user2),
            $this->findSpouseRelation($user1, $user2),
            $this->findGrandparentRelation($user1, $user2),
            $this->findUncleAuntRelation($user1, $user2),
            $this->findCousinRelation($user1, $user2)
        ];

        // Filter out null results and find the first valid relation
        $relation = collect($relationChecks)->filter()->first();

        if ($relation) {
            return $relation;
        }

        // If no direct relation found, perform a comprehensive search
        return $this->findComplexRelation($user1, $user2);
    }

    private function findComplexRelation(User $user1, User $user2)
    {
        // Find all ancestors for both users
        $user1Ancestors = $this->getAllAncestorsWithPath($user1);
        $user2Ancestors = $this->getAllAncestorsWithPath($user2);

        // Find common ancestors
        $commonAncestors = $user1Ancestors->filter(function ($ancestorInfo1) use ($user2Ancestors) {
            return $user2Ancestors->contains('user.id', $ancestorInfo1['user']->id);
        });

        // If no common ancestors found
        if ($commonAncestors->isEmpty()) {
            return [
                'relation' => 'unknown',
                'description' => 'No direct relation found',
                'users_between' => [],
                'relation_path' => []
            ];
        }

        // Get the closest common ancestor
        $closestCommonAncestor = $commonAncestors->first();

        // Trace paths to the common ancestor
        $path1 = $this->getPathToAncestor($user1, $closestCommonAncestor['user']);
        $path2 = $this->getPathToAncestor($user2, $closestCommonAncestor['user']);

        // Combine and remove duplicates
        $usersBetween = collect(array_merge($path1, $path2))
            ->unique('id')
            ->values();

        // Determine relation description based on path length
        $relationDescription = $this->determineRelationDescription(
            count($path1),
            count($path2),
            $user1->gender,
            $user2->gender
        );

        return [
            'relation' => 'complex_relation',
            'description' => $relationDescription,
            'users_between' => $usersBetween,
            'relation_path' => [
                'user1_path' => $path1,
                'common_ancestor' => $closestCommonAncestor['user'],
                'user2_path' => $path2
            ]
        ];
    }

    private function getAllAncestorsWithPath(User $user)
    {
        $ancestors = collect();
        $currentUser = $user;
        $generation = 0;

        while ($currentUser) {
            $ancestors->push([
                'user' => $currentUser,
                'generation' => $generation
            ]);

            $currentUser = $this->getParent($currentUser);
            $generation++;
        }

        return $ancestors;
    }

    private function getPathToAncestor(User $startUser, User $targetAncestor)
    {
        $path = [];
        $currentUser = $startUser;

        while ($currentUser && $currentUser->id !== $targetAncestor->id) {
            $parent = $this->getParent($currentUser);
            if (!$parent) break;

            $path[] = $parent;
            $currentUser = $parent;
        }

        return $path;
    }

    private function determineRelationDescription($path1Length, $path2Length, $gender1, $gender2)
    {
        // This is a simplified relation description logic
        // You might want to expand this based on your specific genealogy requirements
        if ($path1Length === 1 && $path2Length === 1) return 'Sibling';
        if ($path1Length === 2 && $path2Length === 2) return 'First Cousins';
        if ($path1Length === 3 && $path2Length === 3) return 'Second Cousins';

        return 'Distant Relation';
    }

    private function findDirectParentChildRelation(User $user1, User $user2)
    {
        // Check if user1 is parent of user2
        if ($user1->id === $user2->father_id) {
            return [
                'relation' => 'parent_child',
                'description' => $user2->gender === Gender::Male
                    ? 'Father of User'
                    : 'Mother of User',
                'users_between' => [],
                'relation_path' => [
                    'parent' => $user1,
                    'child' => $user2
                ]
            ];
        }

        // Check if user2 is parent of user1
        if ($user2->id === $user1->father_id) {
            return [
                'relation' => 'child',
                'description' => $user1->gender === Gender::Male
                    ? 'Son'
                    : 'Daughter',
                'users_between' => [],
                'relation_path' => [
                    'parent' => $user2,
                    'child' => $user1
                ]
            ];
        }

        return null;
    }


    //V3
    public function findRelationBetweenUsersV3(User $user1, User $user2)
    {
        // Check if users are the same
        if ($user1->id === $user2->id) {
            return [
                'relation' => 'same_user',
                'description' => 'Same Person',
                'users_between' => [],
                'relation_graph' => $this->createSingleUserGraph($user1)
            ];
        }

        // Existing relation checks
        $relationChecks = [
            $this->findDirectParentChildRelation($user1, $user2),
            $this->findSiblingRelation($user1, $user2),
            $this->findSpouseRelation($user1, $user2),
            $this->findGrandparentRelation($user1, $user2),
            $this->findUncleAuntRelation($user1, $user2),
            $this->findCousinRelation($user1, $user2)
        ];

        // Filter out null results and find the first valid relation
        $relation = collect($relationChecks)->filter()->first();

        if ($relation) {
            return $relation;
        }

        // If no direct relation found, perform a comprehensive search
        return $this->findComplexRelationV3($user1, $user2);
    }

    private function findComplexRelationV3(User $user1, User $user2)
    {
        // Find all ancestors for both users
        $user1Ancestors = $this->getAllAncestorsWithPath($user1);
        $user2Ancestors = $this->getAllAncestorsWithPath($user2);

        // Find common ancestors
        $commonAncestors = $user1Ancestors->filter(function ($ancestorInfo1) use ($user2Ancestors) {
            return $user2Ancestors->contains('user.id', $ancestorInfo1['user']->id);
        });

        // If no common ancestors found
        if ($commonAncestors->isEmpty()) {
            return [
                'relation' => 'unknown',
                'description' => 'No direct relation found',
                'users_between' => [],
                'relation_graph' => $this->createUnrelatedUsersGraph($user1, $user2)
            ];
        }

        // Get the closest common ancestor
        $closestCommonAncestor = $commonAncestors->first();

        // Create a comprehensive relation graph
        $relationGraph = $this->createRelationGraph($user1, $user2, $closestCommonAncestor['user']);

        // Determine relation description based on graph
        $relationDescription = $this->determineRelationDescription(
            $relationGraph['paths']['user1_path'],
            $relationGraph['paths']['user2_path'],
            $user1->gender,
            $user2->gender
        );

        return [
            'relation' => 'complex_relation',
            'description' => $relationDescription,
            'users_between' => $relationGraph['nodes'],
            'relation_graph' => $relationGraph
        ];
    }

    private function createRelationGraph(User $user1, User $user2, User $commonAncestor)
    {
        // Trace paths to the common ancestor
        $path1 = $this->getDetailedPathToAncestor($user1, $commonAncestor);
        $path2 = $this->getDetailedPathToAncestor($user2, $commonAncestor);

        // Combine all unique nodes
        $allNodes = collect(array_merge(
            [$user1],
            $path1['path'],
            [$commonAncestor],
            $path2['path'],
            [$user2]
        ))->unique('id')->values();

        // Create edges (connections between nodes)
        $edges = $this->createEdges($user1, $user2, $path1, $path2, $commonAncestor);

        return [
            'nodes' => $allNodes,
            'edges' => $edges,
            'paths' => [
                'user1_path' => $path1,
                'common_ancestor' => $commonAncestor,
                'user2_path' => $path2
            ],
            'start_node' => $user1,
            'end_node' => $user2
        ];
    }

    private function createEdges(User $user1, User $user2, array $path1, array $path2, User $commonAncestor)
    {
        $edges = [];

        // Add edges for path1
        $previousNode = $user1;
        foreach ($path1['path'] as $node) {
            $edges[] = [
                'from' => $previousNode->id,
                'to' => $node->id,
                'type' => $this->determineEdgeType($previousNode, $node)
            ];
            $previousNode = $node;
        }

        // Add edge to common ancestor
        if (!empty($path1['path'])) {
            $edges[] = [
                'from' => $previousNode->id,
                'to' => $commonAncestor->id,
                'type' => $this->determineEdgeType($previousNode, $commonAncestor)
            ];
        }

        // Add edges for path2
        $previousNode = $commonAncestor;
        foreach ($path2['path'] as $node) {
            $edges[] = [
                'from' => $previousNode->id,
                'to' => $node->id,
                'type' => $this->determineEdgeType($previousNode, $node)
            ];
            $previousNode = $node;
        }

        // Final edge to user2
        $edges[] = [
            'from' => $previousNode->id,
            'to' => $user2->id,
            'type' => $this->determineEdgeType($previousNode, $user2)
        ];

        return $edges;
    }

    private function getDetailedPathToAncestor(User $startUser, User $targetAncestor)
    {
        $path = [];
        $currentUser = $startUser;
        $pathDetails = [];

        while ($currentUser && $currentUser->id !== $targetAncestor->id) {
            $parent = $this->getParent($currentUser);
            if (!$parent) break;

            $pathDetails[] = [
                'from' => $currentUser,
                'to' => $parent,
                'relation_type' => $this->determineParentChildRelationType($parent, $currentUser)
            ];

            $path[] = $parent;
            $currentUser = $parent;
        }

        return [
            'path' => array_reverse($path),
            'details' => array_reverse($pathDetails)
        ];
    }

    private function determineParentChildRelationType(User $parent, User $child)
    {
        if ($parent->id === $child->father_id) {
            return $parent->gender === Gender::Male ? 'father' : 'mother';
        }
        return 'parent';
    }

    private function determineEdgeType(User $fromUser, User $toUser)
    {
        // Determine the type of relationship between two users
        if ($fromUser->id === $toUser->father_id) return 'parent_to_child';
        if ($toUser->id === $fromUser->father_id) return 'child_to_parent';

        // Check for spouse relationship
        $spouseRelation = $this->findSpouseRelation($fromUser, $toUser);
        if ($spouseRelation) return 'spouse';

        return 'family';
    }

    private function createSingleUserGraph(User $user)
    {
        return [
            'nodes' => [$user],
            'edges' => [],
            'start_node' => $user,
            'end_node' => $user
        ];
    }

    private function createUnrelatedUsersGraph(User $user1, User $user2)
    {
        return [
            'nodes' => [$user1, $user2],
            'edges' => [],
            'start_node' => $user1,
            'end_node' => $user2
        ];
    }






// Additional method to enhance node information
    public function enrichNodeData($nodes)
    {
        return $nodes->map(function ($node) {
            // Add additional information to each node
            $node->full_name = $node->name;
            $node->gender_icon = $node->gender === Gender::Male ? '♂' : '♀';

            // You can add more details like age, birthdate, etc.
            return $node;
        });
    }

// Enhanced relation finding method
    public function findDetailedRelation(User $user1, User $user2)
    {
        $relationGraph = $this->findRelationBetweenUsersV3($user1, $user2)['relation_graph'];

        // Enrich node data
        $relationGraph['nodes'] = $this->enrichNodeData($relationGraph['nodes']);

        // Generate SVG
        $svg = $this->generateRelationGraphSVG($relationGraph);

        return [
            'relation_graph' => $relationGraph,
            'svg' => $svg
        ];
    }

    public function generateRelationGraphSVG($relationGraph)
    {
        $width = 1000;
        $height = 600;
        $nodeRadius = 40;
        $nodes = $relationGraph['nodes']->all();
        $edges = $relationGraph['edges'];

        $svg = '<?xml version="1.0" encoding="UTF-8"?>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ' . $width . ' ' . $height . '" width="' . $width . '" height="' . $height . '">
        <style>
            .node {
                fill: #4a90e2;
                stroke: #2c3e50;
                stroke-width: 3;
                transition: all 0.3s ease;
            }
            .node:hover {
                fill: #3498db;
                transform: scale(1.1);
            }
            .node-label {
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                fill: white;
            }
            .node-sublabel {
                font-family: Arial, sans-serif;
                font-size: 10px;
                fill: #ecf0f1;
            }
            .edge {
                stroke: #34495e;
                stroke-width: 2;
                marker-end: url(#arrowhead);
            }
            .edge-parent-child {
                stroke: #2ecc71;
            }
            .edge-spouse {
                stroke: #e74c3c;
                stroke-dasharray: 5,5;
            }
            .relation-description {
                font-family: Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                fill: #2c3e50;
            }
        </style>

        <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7"
            refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
            </marker>
        </defs>';

        // Calculate node positions using a more dynamic layout
        $numNodes = count($nodes);
        $centerX = $width / 2;
        $centerY = $height / 2;
        $radius = min($width, $height) * 0.3;

        // Draw nodes in a circular layout
        foreach ($nodes as $index => $node) {
            $angle = (2 * M_PI * $index) / $numNodes;
            $x = $centerX + $radius * cos($angle);
            $y = $centerY + $radius * sin($angle);

            // Main node circle
            $svg .= '<circle cx="' . $x . '" cy="' . $y . '" r="' . $nodeRadius . '" class="node"/>';

            // Node name
            $svg .= '<text x="' . $x . '" y="' . $y . '" text-anchor="middle" class="node-label">'
                . htmlspecialchars($node->name ?? $node->id) . '</text>';

            // Node additional info (gender, age, etc.)
            $svg .= '<text x="' . $x . '" y="' . ($y + 20) . '" text-anchor="middle" class="node-sublabel">'
                . htmlspecialchars($node->gender ?? 'Unknown') . '</text>';
        }

        // Draw edges
        foreach ($edges as $edge) {
            $fromNode = $this->findNodeById($nodes, $edge['from']);
            $toNode = $this->findNodeById($nodes, $edge['to']);

            if ($fromNode && $toNode) {
                $fromIndex = array_search($fromNode, $nodes);
                $toIndex = array_search($toNode, $nodes);

                $fromAngle = (2 * M_PI * $fromIndex) / $numNodes;
                $toAngle = (2 * M_PI * $toIndex) / $numNodes;

                $fromX = $centerX + $radius * cos($fromAngle);
                $fromY = $centerY + $radius * sin($fromAngle);
                $toX = $centerX + $radius * cos($toAngle);
                $toY = $centerY + $radius * sin($toAngle);

                $edgeClass = 'edge ' . ($edge['type'] === 'spouse' ? 'edge-spouse' : 'edge-parent-child');
                $svg .= '<line x1="' . $fromX . '" y1="' . $fromY . '" x2="' . $toX . '" y2="' . $toY . '" class="' . $edgeClass . '"/>';
            }
        }

        // Add relation description
        $svg .= '<text x="' . $centerX . '" y="' . ($height - 50) . '" text-anchor="middle" class="relation-description">'
            . htmlspecialchars($relationGraph['description'] ?? 'Family Relation') . '</text>';

        $svg .= '</svg>';

        return $svg;
    }

    private function findNodeById($nodes, $id)
    {
        foreach ($nodes as $node) {
            if ($node->id === $id) {
                return $node;
            }
        }
        return null;
    }
}
