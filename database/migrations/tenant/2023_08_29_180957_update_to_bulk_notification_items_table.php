<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bulk_notification_items', function (Blueprint $table) {
            $table->string('status')->nullable()->after('recipient_type');
            $table->string('ref_id')->nullable()->after('id');
            $table->dateTime('sent_at')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bulk_notification_items', function (Blueprint $table) {
            $table->dropColumn(['status', 'ref_id']);
            $table->dropColumn('sent_at');
        });
    }
};
