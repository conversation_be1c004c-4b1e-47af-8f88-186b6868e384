<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('help_form_template_items', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\HelpFormTemplate::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(\App\Models\HelpFormTemplateSection::class)->constrained()->cascadeOnDelete();
            $table->enum('type', [
                'STRING', 'TEXT', 'NUMERIC', 'CHECKBOX', 'DATE', 'HIJRI_DATE', 'SELECT',
                'ATTACHMENT', 'MULTIPLE_ATTACHMENT', 'IMAGE', 'MULTIPLE_IMAGE', 'PDF', 'MULTIPLE_PDF', 'BLADE',
            ])->nullable();
            $table->boolean('required')->default(true);
            $table->string('title')->nullable();
            $table->string('placeholder')->nullable();
            $table->string('icon')->nullable();
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('help_form_template_items');
    }
};
