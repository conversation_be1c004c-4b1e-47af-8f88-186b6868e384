<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('ALTER TABLE "help_form_items" DROP CONSTRAINT "help_form_items_type_check"');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('ALTER TABLE "help_form_items" ADD CONSTRAINT "help_form_items_type_check" CHECK (type::text = ANY (ARRAY[\'TEXT\'::character varying::text, \'FILE\'::character varying::text, \'MULTIPLE_FILE\'::character varying::text]))');
    }
};
