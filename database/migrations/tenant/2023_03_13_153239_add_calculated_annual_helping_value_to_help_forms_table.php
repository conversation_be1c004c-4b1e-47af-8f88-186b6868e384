<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('help_forms', function (Blueprint $table) {
            $table->integer('calculated_annual_helping_value')->nullable()->after('calculated_income_value');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('help_forms', function (Blueprint $table) {
            $table->dropColumn('calculated_annual_helping_value');
        });
    }
};
