<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('debt_discharge_invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('debt_discharge_id')->constrained();
            $table->string('creditor_name')->nullable();
            $table->string('creditor_phone')->nullable();
            $table->string('invoice_id')->nullable();
            $table->string('amount')->nullable();
            $table->string('due_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('debt_discharge_invoices');
    }
};
