<?php

use App\Models\GroupReport;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('group_reports', function (Blueprint $table) {
            $table->unsignedSmallInteger('year')->after('user_id');
        });
        GroupReport::query()->with(['group_date'])->get()->each(function(GroupReport $report){
            $year = ($report->group_date->schedule_at ?? $report->created_at ?? now())->year;
            $report->update([
                'year' => $year
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('group_reports', function (Blueprint $table) {
            $table->dropColumn('year');
        });
    }
};
