<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quran_competitions', function (Blueprint $table) {
            $table->foreignIdFor(\App\Models\Activity::class)
                ->nullable()->after('closed')->constrained()->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quran_competitions', function (Blueprint $table) {
            $table->dropConstrainedForeignIdFor(\App\Models\Activity::class);
        });
    }
};
