<?php

use App\Enums\SupportServiceStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('kin_charities', function (Blueprint $table) {
            $table->id();
            $table->string('case_id')->unique()->nullable();
            $table->string('name');
            $table->string('national_id')->nullable();
            $table->string('phone')->nullable();
            $table->integer('family_members')->nullable();

            $table->string('family_card_image_url')->nullable();

            $table->string('family_card_image_thumb_url')->nullable();

            $table->enum('status', [SupportServiceStatus::CaseA, SupportServiceStatus::CaseB, SupportServiceStatus::CaseC, SupportServiceStatus::Declined])->nullable();

            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('responsible_user_id')->nullable()->constrained('users')->nullOnDelete();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('kin_charities');
    }
};
