<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_transactions', function (Blueprint $table) {
            $table->timestamp('processing_lock')->nullable()->after('confirmed_at')
                ->comment('Timestamp when transaction processing started to prevent duplicate processing');
            $table->string('processing_status', 50)->default('pending')->after('processing_lock')
                ->comment('Current processing status: pending, processing, completed, failed');
            
            // Add index for efficient querying
            $table->index(['processing_status', 'processing_lock'], 'idx_processing_status_lock');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_transactions', function (Blueprint $table) {
            $table->dropIndex('idx_processing_status_lock');
            $table->dropColumn(['processing_lock', 'processing_status']);
        });
    }
};
