<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('package_feature', function (Blueprint $table) {
            $table->foreignIdFor(\App\Models\Package::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(\App\Models\MembershipFeature::class)->constrained('features')->cascadeOnDelete();
            $table->mediumInteger('sort_order')->unsigned()->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('package_feature');
    }
};
