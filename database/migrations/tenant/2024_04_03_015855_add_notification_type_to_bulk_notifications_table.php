<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bulk_notifications', function (Blueprint $table) {
            $table->string('notification_type')->nullable()->after('broadcast_channel_sender_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bulk_notifications', function (Blueprint $table) {
            $table->dropColumn('notification_type');
        });
    }
};
