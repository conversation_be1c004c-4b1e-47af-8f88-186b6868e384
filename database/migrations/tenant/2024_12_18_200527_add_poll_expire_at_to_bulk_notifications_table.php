<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bulk_notifications', function (Blueprint $table) {
            $table->timestamp('poll_expire_at')->nullable()->after('poll_options');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bulk_notifications', function (Blueprint $table) {
            $table->dropColumn('poll_expire_at');
        });
    }
};
