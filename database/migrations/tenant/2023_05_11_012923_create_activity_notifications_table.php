<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activity_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Activity::class)->constrained()->cascadeOnDelete();
            $table->json('user_ids');
            $table->longText('content');
            $table->dateTime('completed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('activity_notifications');
    }
};
