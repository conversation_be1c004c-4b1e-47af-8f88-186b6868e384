<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Event::class)->constrained()->cascadeOnDelete();
            $table->foreignId('event_allowable_id')->nullable()->constrained('event_allowable')->nullOnDelete();
            $table->foreignUuid('event_invitation_uuid')->nullable()->constrained('event_invitations', 'uuid')->nullOnDelete();
            $table->foreignIdFor(\App\Models\User::class)->constrained()->cascadeOnDelete();
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->dateTime('checkout_at')->nullable();
            $table->string('via'); // ['QRCODE', 'APPLE_PASS', 'GPS']
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_attendances');
    }
};
