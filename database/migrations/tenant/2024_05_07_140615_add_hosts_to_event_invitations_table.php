<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('event_invitations', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable()->change();
            $table->string('guest_name')->nullable()->after('user_id');
            $table->foreignIdFor(\App\Models\User::class, 'host_user_id')->nullable()->after('guest_name')->constrained('users')->restrictOnDelete();
            $table->foreignIdFor(\App\Models\EventAllowable::class)->nullable()->after('host_user_id')->constrained('event_allowable')->restrictOnDelete();
            $table->foreignUuid('host_invitation_uuid')->nullable()->after('event_allowable_id')->constrained('event_invitations', 'uuid')->restrictOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('event_invitations', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->change();
            $table->dropForeign('event_invitations_host_invitation_uuid_foreign');
            $table->dropConstrainedForeignId('host_user_id');
            $table->dropConstrainedForeignId('event_allowable_id');
            $table->dropColumn('host_invitation_uuid');
            $table->dropColumn('guest_name');
        });
    }
};
