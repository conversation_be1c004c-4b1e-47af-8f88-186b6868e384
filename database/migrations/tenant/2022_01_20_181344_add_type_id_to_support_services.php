<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('support_services', function (Blueprint $table) {
            $table->foreignId('support_service_type_id')->nullable()->after('case_id')->constrained();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('support_services', function (Blueprint $table) {
            $table->dropConstrainedForeignId('support_service_type_id');
        });
    }
};
