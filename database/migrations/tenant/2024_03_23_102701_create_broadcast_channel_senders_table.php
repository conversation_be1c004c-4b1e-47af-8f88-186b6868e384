<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('broadcast_channel_senders', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\BroadcastChannel::class);
            $table->string('sender_name');
            $table->time('available_from')->nullable();
            $table->time('available_to')->nullable();
            $table->boolean('default')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('broadcast_channel_senders');
    }
};
