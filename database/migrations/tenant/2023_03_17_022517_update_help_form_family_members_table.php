<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('help_form_family_members', function (Blueprint $table) {
            $table->string('first_name')->nullable()->after('member_id');
            $table->date('dob')->nullable()->after('name');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('help_form_family_members', function (Blueprint $table) {
            $table->dropColumn(['first_name', 'dob']);
        });
    }
};
