<?php

use App\Models\BankAccountTransaction;
use App\Models\Supporter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bank_transaction_import_items', function (Blueprint $table) {
            $table->id();
            $table->uuid('bank_transaction_import_uuid');
            $table->string('notes');
            $table->decimal('amount')->unsigned();
            $table->dateTime('due_at');
            $table->string('hash');
            $table->foreignIdFor(Supporter::class)->nullable()->constrained()->nullOnDelete();
            $table->foreignIdFor(BankAccountTransaction::class, 'transaction_id')->nullable()->constrained('bank_account_transactions')->restrictOnDelete();
            $table->string('error')->nullable();
            $table->timestamps();

            $table->foreign('bank_transaction_import_uuid', 'import_uuid_foreign_key')
                ->on('bank_transaction_import')
                ->references('uuid')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bank_transaction_import_items');
    }
};
