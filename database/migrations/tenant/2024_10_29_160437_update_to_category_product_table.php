<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('category_product', function (Blueprint $table) {
            $table->foreign('category_id')->on('categories')->references('id')->cascadeOnDelete();
            $table->nullableMorphs('model');
            $table->unsignedBigInteger('product_id')->nullable()->change();
            $table->dropUnique(['category_id', 'product_id']);
            $table->unique(['category_id', 'model_type', 'model_id']);
        });
        DB::table('category_product')->get()->each(function ($i) {
            DB::table('category_product')->where([
                'product_id' => $i->product_id,
                'category_id' => $i->category_id,
            ])->update([
                'model_type' => \App\Models\Product::class,
                'model_id' => $i->product_id,
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('category_product', function (Blueprint $table) {
            $table->unique(['category_id', 'product_id']);
            $table->dropUnique(['category_id', 'model_type', 'model_id']);
            $table->dropForeign('category_product_category_id_foreign');
            $table->dropMorphs('model');
            $table->unsignedBigInteger('product_id')->nullable(false)->change();
        });
    }
};
