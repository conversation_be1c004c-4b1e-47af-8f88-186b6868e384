<?php

use App\Models\Product;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('activities', function (Blueprint $table) {
            $table->integer('requests_price')->nullable()->after('requests_paid');
            $table->foreignIdFor(Product::class)->nullable()->after('requests_price')
                ->constrained()->nullOnDelete();
            $table->foreignId('requests_role_id')->nullable()->after('requests_female')
                ->constrained('activity_member_roles')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('activities', function (Blueprint $table) {
            $table->dropConstrainedForeignIdFor(Product::class);
            $table->dropConstrainedForeignId('requests_role_id');
            $table->dropColumn('requests_price');
        });
    }
};
