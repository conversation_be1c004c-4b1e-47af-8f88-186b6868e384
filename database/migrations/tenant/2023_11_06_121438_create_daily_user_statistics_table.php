<?php

use App\Models\StravaActivity;
use App\Models\WorkoutChallenge;
use App\Models\WorkoutUserStatistics;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_user_statistics', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(WorkoutUserStatistics::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(WorkoutChallenge::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(StravaActivity::class, 'success_activity_id')
                ->nullable()->constrained((new StravaActivity())->getTable())->cascadeOnDelete();
            $table->date('date');
            $table->json('activities');
            $table->boolean('success')->default(false);
            $table->boolean('warn')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_user_statistics');
    }
};
