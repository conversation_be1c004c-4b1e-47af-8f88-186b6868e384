<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('myfatoorah_payments', function (Blueprint $table) {
            $table->after('invoice_number', function (Blueprint $table) {
                $table->decimal('due_deposit', 8, 4)->nullable();
                $table->decimal('service_charge', 8, 4)->nullable();
                $table->decimal('vat_amount', 8, 4)->nullable();
                $table->string('card_number')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('myfatoorah_payments', function (Blueprint $table) {
            $table->dropColumn(['due_deposit', 'service_charge', 'vat_amount', 'card_number']);
        });
    }
};
