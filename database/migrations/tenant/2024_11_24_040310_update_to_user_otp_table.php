<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_otp', function (Blueprint $table) {
            $table->dateTime('success_at')->nullable()->after('code');
            $table->dateTime('failed_at')->nullable()->after('success_at');
            $table->string('auth_method')->nullable()->after('failed_at');
            $table->json('metadata')->nullable()->after('auth_method');
            $table->string('ip')->nullable()->after('metadata');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_otp', function (Blueprint $table) {
            $table->dropColumn([
                'success_at',
                'failed_at',
                'auth_method',
                'metadata',
                'ip',
            ]);
        });
    }
};
