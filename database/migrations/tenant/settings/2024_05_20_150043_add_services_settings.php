<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->inGroup('services', function (\Spatie\LaravelSettings\Migrations\SettingsBlueprint $blueprint): void {
            $blueprint->add('cloudAccessKeyId', null);
            $blueprint->add('cloudSecretAccessKey', null);
            $blueprint->add('cloudBucket', null);
            $blueprint->add('cloudEndpoint', null);
            $blueprint->add('cloudUrl', null);
            $blueprint->add('taqnyatKey', null);
            $blueprint->add('taqnyatSender', null);
            $blueprint->add('wahaUrl', null);
            $blueprint->add('wahaApiKey', null);
            $blueprint->add('wahaSession', null);
            $blueprint->add('wahaHealth', null);
            $blueprint->add('wahaWorking', null);
        });
    }
};
