<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->inGroup('app', function (\Spatie\LaravelSettings\Migrations\SettingsBlueprint $blueprint): void {
            $blueprint->add('themeColors', [
                'light' => [
                    'primary' => null,
                    'secondary' => null,
                    '--app-svg-color' => null,
                    '--app-svg-second-color' => null,
                ],
                'dark' => [
                    'primary' => null,
                    'secondary' => null,
                    '--app-svg-color' => null,
                    '--app-svg-second-color' => null,
                ],
            ]);
        });
    }
};
