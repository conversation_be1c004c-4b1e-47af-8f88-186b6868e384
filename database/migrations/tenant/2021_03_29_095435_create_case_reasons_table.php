<?php

use App\Enums\CaseStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('case_reasons', function (Blueprint $table) {
            $table->id();
            $table->nullableMorphs('reasonable', 'reasonable');
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->longText('content');
            $table->enum('status', [
                CaseStatus::Approved, CaseStatus::Declined,
                \App\Enums\SupportServiceStatus::CaseA,
                \App\Enums\SupportServiceStatus::CaseB,
                \App\Enums\SupportServiceStatus::CaseC,
            ])->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('case_reasons');
    }
};
