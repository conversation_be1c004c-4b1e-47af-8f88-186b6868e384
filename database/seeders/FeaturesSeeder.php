<?php

namespace Database\Seeders;

use App\Models\AppFeature;
use Illuminate\Database\Seeder;

class FeaturesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        AppFeature::query()->insert([
            ['slug' => 'ActiveMembers', 'title' => 'أعضاء الصندوق'],
            ['slug' => 'Activities', 'title' => 'البرامج والأنشطة'],
            ['slug' => 'Companies', 'title' => 'الشركات'],
            ['slug' => 'Consultations', 'title' => 'الاستشارات'],
            ['slug' => 'Documents', 'title' => 'الوثائق'],
            ['slug' => 'FamilyGraph', 'title' => 'المشجرة'],
            ['slug' => 'HallReservation', 'title' => 'حجز القاعة'],
            ['slug' => 'Literatures', 'title' => 'المؤلفات'],
            ['slug' => 'ManagementCrew', 'title' => 'الهيكل الإداري'],
            ['slug' => 'Memberships', 'title' => 'العضويات'],
            ['slug' => 'News', 'title' => 'الأخبار'],
            ['slug' => 'Store', 'title' => 'المتجر الإلكتروني'],
            ['slug' => 'Strava', 'title' => 'سترافا'],
            ['slug' => 'ActivitySupportAmount', 'title' => 'قيمة البرامج الرعوية'],
            ['slug' => 'ChildForms', 'title' => 'زر إضافة الأبناء'],
            ['slug' => 'Events', 'title' => 'المناسبات'],
            ['slug' => 'WalletPasses', 'title' => 'WalletPasses'],
            //['slug' => 'BankAccounts', 'title' => 'الحسابات البنكية'],
            ['slug' => 'Notifications', 'title' => 'الإشعارات'],
            ['slug' => 'Promotions', 'title' => 'العروض'],
            ['slug' => 'QuranCompetition', 'title' => 'المسابقة القرآنية'],
            ['slug' => 'EidCards', 'title' => 'كروت التهنئة'],
        ]);
    }
}
