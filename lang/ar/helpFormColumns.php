<?php

use App\Enums\CaseStatus;
use App\Enums\Gender;
use App\Enums\ResidenceType;
use App\Enums\UserMaritalStatus;

return [
    'id'                  => 'ID',
    'hijri_year'          => 'السنة الهجرية',
    'help_form_template_id'             => 'نوع النموذج',
    'user_id'             => 'المستفيد',
    'created_by_id'       => 'منشئ النموذج',
    'responsible_user_id' => 'الباحث',
    'breadwinner_id'      => 'عائل الأسرة',
    'phone'               => 'رقم الجوال',
    'name'                => 'اسم المستفيد',
    'form_id'             => 'رقم نموذج المساعدة',
    'status'              => 'حالة القبول/الرفض',
    'notes'               => 'رأي الباحث',
    'gender'              => 'جنس المستفيد',
    'city_id'             => 'المدينة',
    'age'                 => 'العمر',
    'marital_status'      => 'الحالة الاجتماعية',
    'national_id'         => 'رقم الهوية',
    'address'             => 'العنوان',
    'template'            => 'اسم النموذج',
    'user'                => 'المستفيد',

    'salary'                          => 'الدخل',
    'social_support'                  => 'الضمان الاجتماعي',
    'citizen_account'                 => 'حساب المواطن',
    'spouses_salary'                  => 'راتب الزوج/ـة والأبناء',
    'income'                          => 'مجموع الدخل',
    'expenses'                        => 'الالتزامات',
    'final_income'                    => 'صافي الدخل',
    'debts'                           => 'الديون',
    'dowry'                           => 'المهر',
    'bill_type'                       => 'نوع الفاتورة',
    'bill_invoice_no'                 => 'رقم الفاتورة',
    'calculated_income_value'         => 'صافي الدخل',
    'calculated_restricted_value'     => 'مقدار الحد المانع',
    'calculated_annual_helping_value' => 'مقدار الدعم حسب المادة السابعة',
    'suggested_deserved_status'       => 'مستحق الدعم ؟',
    'deserved_helping'                => 'مستحق',
    'undeserved_helping'              => 'غير مستحق',

    'helping_value'           => 'الدعم المقدم',
    'required_helping_value'  => 'الدعم المطلوب',
    'max_income'              => 'الحد المانع',
    'spouses_count'           => 'عدد الزوجات/الأزواج',
    'underage_children_count' => 'عدد الأبناء القصر',
    'adult_children_count'    => 'عدد الأبناء البالغين',
    'children_count'          => 'عدد الأبناء',

    'residence'           => 'السكن',
    'residence_type'      => 'نوع السكن',
    'residence_status'    => 'حالة السكن',
    'residence_size'      => 'مساحة السكن',
    'residence_rent_cost' => 'قيمة الإيجار',
    'residence_services'  => 'خدمات',
    'support_service_type'  => 'نوع الخدمة',
    'support_service_items'  => 'قائمة الخدمات',
    'support_service_type.' . \App\Enums\SupportServiceType::FoodPackage  => 'سلة غذائية',
    'support_service_type.' . \App\Enums\SupportServiceType::HomeMaintenance  => 'صيانة منزلية',
    'support_service_type.' . \App\Enums\SupportServiceType::Qurban  => 'أضحية',
    'support_service_type.' . \App\Enums\SupportServiceType::HomeRenovation  => 'ترميم منزل',

    'completed_at' => 'تاريخ الإضافة',
    'created_at'   => 'تاريخ الإضافة',
    'updated_at'   => 'آخر تعديل',

    'status.' . CaseStatus::Approved    => 'حالة مقبولة',
    'status.' . CaseStatus::Declined    => 'حالة مرفوضة',
    'gender.' . Gender::Male            => 'ذكر',
    'gender.' . Gender::Female          => 'أنثى',
    'residence.' . ResidenceType::Owner => 'ملك',
    'residence.' . ResidenceType::Rent  => 'إيجار',
    'residence.' . ResidenceType::Other => 'أخر',

    'bill_type.' . \App\Enums\BillType::Electricity => 'فاتورة كهرباء',
    'bill_type.' . \App\Enums\BillType::Water       => 'فاتورة مياه',

    'marital_status.' . UserMaritalStatus::Single . '-' . Gender::Male     => 'أعزب',
    'marital_status.' . UserMaritalStatus::Married . '-' . Gender::Male    => 'متزوج',
    'marital_status.' . UserMaritalStatus::Divorced . '-' . Gender::Male   => 'مطلق',
    'marital_status.' . UserMaritalStatus::Widower . '-' . Gender::Male    => 'أرمل',
    'marital_status.' . UserMaritalStatus::Single . '-' . Gender::Female   => 'عزباء',
    'marital_status.' . UserMaritalStatus::Married . '-' . Gender::Female  => 'متزوجة',
    'marital_status.' . UserMaritalStatus::Divorced . '-' . Gender::Female => 'مطلقة',
    'marital_status.' . UserMaritalStatus::Widower . '-' . Gender::Female  => 'أرملة',
];
