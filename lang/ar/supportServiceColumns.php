<?php

return [
    'id' => 'ID',
    'beneficiary_user_id' => 'المستفيد',
    'all_services' => 'كل الخدمات',
    'support_service_type_id' => 'تصنيف الخدمة',
    'name' => 'اسم المستفيد',
    'case_id' => 'رقم ملف المستفيد',
    'status' => 'تصنيف الحالة',
    'national_id' => 'رقم الهوية الوطنية',
    'phone' => 'رقم الجوال',
    'family_members' => 'عدد أفراد الأسرة',
    'family_card_image' => 'صورة بطاقة العائلة',
    'user_name' => 'الباحث',
    'responsible_user_id' => 'الباحث',
    'created_at' => 'تاريخ إضافة ملف المستفيد',
    'updated_at' => 'تاريخ آخر تعديل على ملف المستفيد',

    'status.' . \App\Enums\SupportServiceStatus::CaseA => 'حالة أ',
    'status.' . \App\Enums\SupportServiceStatus::CaseB => 'حالة ب',
    'status.' . \App\Enums\SupportServiceStatus::CaseC => 'حالة ج',
    'status.' . \App\Enums\SupportServiceStatus::Declined => 'حالة مرفوضة',
];
