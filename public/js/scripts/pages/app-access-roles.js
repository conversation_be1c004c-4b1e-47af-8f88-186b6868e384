$((function(){var e=$(".user-list-table"),t="../../../app-assets/",a="app-user-view-account.html",s={1:{title:"Pending",class:"badge-light-warning"},2:{title:"Active",class:"badge-light-success"},3:{title:"Inactive",class:"badge-light-secondary"}};"laravel"===$("body").attr("data-framework")&&(t=$("body").attr("data-asset-path"),a=t+"app/user/view/account"),e.length&&e.DataTable({ajax:t+"data/user-list.json",columns:[{data:""},{data:"id"},{data:"full_name"},{data:"role"},{data:"current_plan"},{data:"billing"},{data:"status"},{data:""}],columnDefs:[{className:"control",orderable:!1,responsivePriority:2,targets:0,render:function(e,t,a,s){return""}},{targets:1,orderable:!1,responsivePriority:3,render:function(e,t,a,s){return'<div class="form-check"> <input class="form-check-input dt-checkboxes" type="checkbox" value="" id="checkbox'+e+'" /><label class="form-check-label" for="checkbox'+e+'"></label></div>'},checkboxes:{selectAllRender:'<div class="form-check"> <input class="form-check-input" type="checkbox" value="" id="checkboxSelectAll" /><label class="form-check-label" for="checkboxSelectAll"></label></div>'}},{targets:2,responsivePriority:4,render:function(e,s,n,l){var r=n.full_name,c=n.email,o=n.avatar;if(o)var i='<img src="'+t+"images/avatars/"+o+'" alt="Avatar" height="32" width="32">';else{var d=["success","danger","warning","info","dark","primary","secondary"][Math.floor(6*Math.random())+1],u=(r=n.full_name).match(/\b\w/g)||[];i='<span class="avatar-content">'+(u=((u.shift()||"")+(u.pop()||"")).toUpperCase())+"</span>"}return'<div class="d-flex justify-content-left align-items-center"><div class="avatar-wrapper"><div class="avatar '+(""===o?" bg-light-"+d+" ":"")+' me-1">'+i+'</div></div><div class="d-flex flex-column"><a href="'+a+'" class="user_name text-body text-truncate"><span class="fw-bolder">'+r+'</span></a><small class="emp_post text-muted">'+c+"</small></div></div>"}},{targets:3,render:function(e,t,a,s){var n=a.role;return"<span class='text-truncate align-middle'>"+{Subscriber:feather.icons.user.toSvg({class:"font-medium-3 text-primary me-50"}),Author:feather.icons.settings.toSvg({class:"font-medium-3 text-warning me-50"}),Maintainer:feather.icons.database.toSvg({class:"font-medium-3 text-success me-50"}),Editor:feather.icons["edit-2"].toSvg({class:"font-medium-3 text-info me-50"}),Admin:feather.icons.slack.toSvg({class:"font-medium-3 text-danger me-50"})}[n]+n+"</span>"}},{targets:5,render:function(e,t,a,s){return'<span class="text-nowrap">'+a.billing+"</span>"}},{targets:6,render:function(e,t,a,n){var l=a.status;return'<span class="badge rounded-pill '+s[l].class+'" text-capitalized>'+s[l].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,render:function(e,t,s,n){return'<a href="'+a+'" class="btn btn-sm btn-icon">'+feather.icons.eye.toSvg({class:"font-medium-3 text-body"})+"</a>"}}],order:[[2,"desc"]],dom:'<"d-flex justify-content-between align-items-center header-actions mx-2 row mt-50 mb-1"<"col-sm-12 col-md-4 col-lg-6" l><"col-sm-12 col-md-8 col-lg-6 ps-xl-75 ps-0"<"dt-action-buttons d-flex align-items-center justify-content-md-end justify-content-center flex-sm-nowrap flex-wrap"<"me-1"f><"user_role mt-50 width-200">>>>t<"d-flex justify-content-between mx-2 row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',language:{sLengthMenu:"Show _MENU_",search:"Search",searchPlaceholder:"Search.."},responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(e){return"Details of "+e.data().full_name}}),type:"column",renderer:function(e,t,a){var s=$.map(a,(function(e,t){return""!==e.title?'<tr data-dt-row="'+e.rowIdx+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""})).join("");return!!s&&$('<table class="table"/>').append("<tbody>"+s+"</tbody>")}}},language:{paginate:{previous:"&nbsp;",next:"&nbsp;"}},initComplete:function(){this.api().columns(4).every((function(){var e=this,t=$('<select id="UserRole" class="form-select text-capitalize"><option value=""> Select Role </option></select>').appendTo(".user_role").on("change",(function(){var t=$.fn.dataTable.util.escapeRegex($(this).val());e.search(t?"^"+t+"$":"",!0,!1).draw()}));e.data().unique().sort().each((function(e,a){t.append('<option value="'+e+'" class="text-capitalize">'+e+"</option>")}))}))}});var n=$(".role-edit-modal"),l=$(".add-new-role"),r=$(".role-title");l.on("click",(function(){r.text("Add New Role")})),n.on("click",(function(){r.text("Edit Role")}))}));
