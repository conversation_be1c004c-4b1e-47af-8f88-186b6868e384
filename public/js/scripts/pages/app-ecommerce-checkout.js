$((function(){"use strict";var t=$(".quantity-counter"),s=document.querySelectorAll(".bs-stepper"),e=document.querySelector(".checkout-tab-steps"),o=$(".remove-wishlist"),i=$(".move-cart"),n="rtl"===$("html").attr("data-textdirection");if(o.on("click",(function(){$(this).closest(".ecommerce-card").remove(),toastr.error("","Removed Item 🗑️",{closeButton:!0,tapToDismiss:!1,rtl:n})})),i.on("click",(function(){$(this).closest(".ecommerce-card").remove(),toastr.success("","Added to wishlist ❤️",{closeButton:!0,tapToDismiss:!1,rtl:n})})),void 0!==typeof s&&null!==s)for(var r=0;r<s.length;++r)s[r].addEventListener("show.bs-stepper",(function(t){for(var s=t.detail.indexStep,e=$(t.target).find(".step").length-1,o=$(t.target).find(".step"),i=0;i<s;i++){o[i].classList.add("crossed");for(var n=s;n<e;n++)o[n].classList.remove("crossed")}if(0==t.detail.to){for(var r=s;r<e;r++)o[r].classList.remove("crossed");o[0].classList.remove("crossed")}}));if(void 0!==typeof e&&null!==e){var a=new Stepper(e,{linear:!1});$(e).find(".btn-next").each((function(){$(this).on("click",(function(t){a.next()}))})),$(e).find(".btn-prev").on("click",(function(){a.previous()}))}t.length>0&&t.TouchSpin({min:1,max:10}).on("touchspin.on.startdownspin",(function(){var t=$(this);$(".bootstrap-touchspin-up").removeClass("disabled-max-min"),1==t.val()&&$(this).siblings().find(".bootstrap-touchspin-down").addClass("disabled-max-min")})).on("touchspin.on.startupspin",(function(){var t=$(this);$(".bootstrap-touchspin-down").removeClass("disabled-max-min"),10==t.val()&&$(this).siblings().find(".bootstrap-touchspin-up").addClass("disabled-max-min")}))}));
