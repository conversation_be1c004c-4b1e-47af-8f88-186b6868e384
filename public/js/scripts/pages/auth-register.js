$((function(){var e="../../../app-assets/",r=document.querySelector(".register-multi-steps-wizard"),t=$(".auth-register-form"),n=$(".select2"),a=$(".credit-card-mask"),i=$(".expiry-date-mask"),s=$(".cvv-code-mask"),d=$(".mobile-number-mask"),o=$(".pin-code-mask");if("laravel"===$("body").attr("data-framework")&&(e=$("body").attr("data-asset-path")),t.length&&t.validate({rules:{"register-username":{required:!0},"register-email":{required:!0,email:!0},"register-password":{required:!0}}}),void 0!==typeof r&&null!==r){var l=new Stepper(r);$(r).find("form").each((function(){$(this).validate({rules:{username:{required:!0},email:{required:!0},password:{required:!0,minlength:8},"confirm-password":{required:!0,minlength:8,equalTo:"#password"},"first-name":{required:!0},"home-address":{required:!0},addCard:{required:!0}},messages:{password:{required:"Enter new password",minlength:"Enter at least 8 characters"},"confirm-password":{required:"Please confirm new password",minlength:"Enter at least 8 characters",equalTo:"The password and its confirm are not the same"}}})})),$(r).find(".btn-next").each((function(){$(this).on("click",(function(e){$(this).parent().siblings("form").valid()?l.next():e.preventDefault()}))})),$(r).find(".btn-prev").on("click",(function(){l.previous()})),$(r).find(".btn-submit").on("click",(function(){$(this).parent().siblings("form").valid()&&alert("Submitted..!!")}))}n.each((function(){var e=$(this);e.wrap('<div class="position-relative"></div>'),e.select2({dropdownAutoWidth:!0,width:"100%",dropdownParent:e.parent()})})),a.length&&a.each((function(){new Cleave($(this),{creditCard:!0,onCreditCardTypeChanged:function(r){const t=document.querySelectorAll(".card-type");if(""!=r&&"unknown"!=r)for(let n=0;n<t.length;n++)t[n].innerHTML='<img src="'+e+"images/icons/payments/"+r+'-cc.png" height="24"/>';else for(let e=0;e<t.length;e++)t[e].innerHTML=""}})})),i.length&&new Cleave(i,{date:!0,delimiter:"/",datePattern:["m","y"]}),s.length&&new Cleave(s,{numeral:!0,numeralPositiveOnly:!0}),d.length&&new Cleave(d,{phone:!0,phoneRegionCode:"US"}),o.length&&new Cleave(o,{delimiter:"",numeral:!0})}));
