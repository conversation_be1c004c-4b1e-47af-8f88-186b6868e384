!function(i,t,e){"use strict";e("form#familyCaseCreateUpdateForm").submit((function(i){})),e(".select2").each((function(){var i=e(this);i.wrap('<div class="position-relative"></div>'),i.select2({theme:"bootstrap-5",dropdownAutoWidth:!0,width:"100%",dropdownParent:i.parent()})})),e("#kinCharityDOB").hijriDatePicker({hijri:!0,viewMode:"days",useCurrent:!1,showSwitcher:!1,showTodayButton:!0,icons:{previous:"<",next:">",today:"تاريخ اليوم",clear:"حذف",close:"اغلاق"},hijriText:"عرض التاريخ الهجري",gregorianText:"عرض التاريخ الميلادي",format:"DD/MM/YYYY",hijriFormat:"iDD/iMM/iYYYY"})}(window,document,jQuery);
