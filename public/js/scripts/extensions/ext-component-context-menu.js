"use strict";var isRtl="rtl"===$("html").attr("data-textdirection");$.contextMenu({selector:"#basic-context-menu",callback:function(t,e){var o="clicked "+t;window.console&&toastr.success("",o,{rtl:isRtl})},items:{"Option 1":{name:"Option 1"},"Option 2":{name:"Option 2"}}}),$.contextMenu({selector:"#left-click-context-menu",trigger:"left",callback:function(t,e){var o="clicked "+t;window.console&&toastr.success("",o,{rtl:isRtl})},items:{"Option 1":{name:"Option 1"},"Option 2":{name:"Option 2"}}}),$.contextMenu({selector:"#hover-context-menu",trigger:"hover",autoHide:!0,callback:function(t,e){var o="clicked "+t;window.console&&toastr.success("",o,{rtl:isRtl})},items:{"Option 1":{name:"Option 1"},"Option 2":{name:"Option 2"}}}),$.contextMenu({selector:"#submenu-context-menu",callback:function(t,e){var o="clicked "+t;window.console&&toastr.success("",o,{rtl:isRtl})},items:{"Option 1":{name:"Option 1"},"Option 2":{name:"Option 2"},fold1:{name:"Sub Group",items:{"Foo Bar":{name:"Foo bar"},fold1a:{name:"Other group",items:{Echo:{name:"echo"},Foxtrot:{name:"foxtrot"},Golf:{name:"golf"}}}}}}});
