(function(){function l(l,n){var u,t=l.split("."),e=V;t[0]in e||!e.execScript||e.execScript("var "+t[0]);for(;t.length&&(u=t.shift());)t.length||void 0===n?e=e[u]?e[u]:e[u]={}:e[u]=n}function n(l,n){function u(){}u.prototype=n.prototype,l.M=n.prototype,l.prototype=new u,l.prototype.constructor=l,l.N=function(l,u,t){for(var e=Array(arguments.length-2),r=2;r<arguments.length;r++)e[r-2]=arguments[r];return n.prototype[u].apply(l,e)}}function u(l,n){null!=l&&this.a.apply(this,arguments)}function t(l){l.b=""}function e(l,n){return l>n?1:l<n?-1:0}function r(l,n){this.b=l,this.a={};for(var u=0;u<n.length;u++){var t=n[u];this.a[t.b]=t}}function i(l){return l=function(l){var n,u=[],t=0;for(n in l)u[t++]=l[n];return u}(l.a),function(l,n){l.sort(n||e)}(l,(function(l,n){return l.b-n.b})),l}function a(l,n){switch(this.b=l,this.g=!!n.v,this.a=n.c,this.i=n.type,this.h=!1,this.a){case T:case U:case Y:case k:case J:case q:case H:this.h=!0}this.f=n.defaultValue}function o(){this.a={},this.f=this.j().a,this.b=this.g=null}function s(l,n){for(var u=i(l.j()),t=0;t<u.length;t++){var e=(a=u[t]).b;if(null!=n.a[e]){l.b&&delete l.b[a.b];var r=11==a.a||10==a.a;if(a.g)for(var a=f(n,e)||[],o=0;o<a.length;o++){var p=l,c=e,h=r?a[o].clone():a[o];p.a[c]||(p.a[c]=[]),p.a[c].push(h),p.b&&delete p.b[c]}else a=f(n,e),r?(r=f(l,e))?s(r,a):g(l,e,a.clone()):g(l,e,a)}}}function f(l,n){var u=l.a[n];if(null==u)return null;if(l.g){if(!(n in l.b)){var t=l.g,e=l.f[n];if(null!=u)if(e.g){for(var r=[],i=0;i<u.length;i++)r[i]=t.b(e,u[i]);u=r}else u=t.b(e,u);return l.b[n]=u}return l.b[n]}return u}function p(l,n,u){var t=f(l,n);return l.f[n].g?t[u||0]:t}function c(l,n){var u;if(null!=l.a[n])u=p(l,n,void 0);else l:{if(void 0===(u=l.f[n]).f){var t=u.i;if(t===Boolean)u.f=!1;else if(t===Number)u.f=0;else{if(t!==String){u=new t;break l}u.f=u.h?"0":""}}u=u.f}return u}function h(l,n){return l.f[n].g?null!=l.a[n]?l.a[n].length:0:null!=l.a[n]?1:0}function g(l,n,u){l.a[n]=u,l.b&&(l.b[n]=u)}function m(l,n){var u,t=[];for(u in n)0!=u&&t.push(new a(u,n[u]));return new r(l,t)}function b(){o.call(this)}function y(){o.call(this)}function v(){o.call(this)}function d(){}function _(){}function S(){}function w(){this.a={}}function x(l){return 0==l.length||ul.test(l)}function A(l,n){if(null==n)return null;n=n.toUpperCase();var u=l.a[n];if(null==u){if(null==(u=W[n]))return null;u=(new S).a(v.j(),u),l.a[n]=u}return u}function N(l){return null==(l=Q[l])?"ZZ":l[0]}function j(l){this.H=RegExp(" "),this.C="",this.m=new u,this.w="",this.i=new u,this.u=new u,this.l=!0,this.A=this.o=this.F=!1,this.G=w.b(),this.s=0,this.b=new u,this.B=!1,this.h="",this.a=new u,this.f=[],this.D=l,this.J=this.g=B(this,this.D)}function B(l,n){var u;if(null!=n&&isNaN(n)&&n.toUpperCase()in W){if(null==(u=A(l.G,n)))throw Error("Invalid region code: "+n);u=c(u,10)}else u=0;return null!=(u=A(l.G,N(u)))?u:tl}function E(l){for(var n=l.f.length,u=0;u<n;++u){var e,r=l.f[u],i=c(r,1);if(l.w==i)return!1;e=l;var a=c(s=r,1);if(-1!=a.indexOf("|"))e=!1;else{var o;a=(a=a.replace(el,"\\d")).replace(rl,"\\d"),t(e.m),o=e;var s=c(s,2),f="999999999999999".match(a)[0];f.length<o.a.b.length?o="":o=(o=f.replace(new RegExp(a,"g"),s)).replace(RegExp("9","g")," "),0<o.length?(e.m.a(o),e=!0):e=!1}if(e)return l.w=i,l.B=al.test(p(r,4)),l.s=0,!0}return l.l=!1}function F(l,n){for(var u=[],t=n.length-3,e=l.f.length,r=0;r<e;++r){var i=l.f[r];0==h(i,3)?u.push(l.f[r]):(i=p(i,3,Math.min(t,h(i,3)-1)),0==n.search(i)&&u.push(l.f[r]))}l.f=u}function $(l){return l.l=!0,l.A=!1,l.f=[],l.s=0,t(l.m),l.w="",I(l)}function R(l){for(var n=l.a.toString(),u=l.f.length,t=0;t<u;++t){var e=l.f[t],r=c(e,1);if(new RegExp("^(?:"+r+")$").test(n))return l.B=al.test(p(e,4)),C(l,n=n.replace(new RegExp(r,"g"),p(e,2)))}return""}function C(l,n){var u=l.b.b.length;return l.B&&0<u&&" "!=l.b.toString().charAt(u-1)?l.b+" "+n:l.b+n}function I(l){var n=l.a.toString();if(3<=n.length){for(var u=l.o&&0==l.h.length&&0<h(l.g,20)?f(l.g,20)||[]:f(l.g,19)||[],t=u.length,e=0;e<t;++e){var r=u[e];0<l.h.length&&x(c(r,4))&&!p(r,6)&&null==r.a[5]||(0!=l.h.length||l.o||x(c(r,4))||p(r,6))&&il.test(c(r,2))&&l.f.push(r)}return F(l,n),0<(n=R(l)).length?n:E(l)?M(l):l.i.toString()}return C(l,n)}function M(l){var n=l.a.toString(),u=n.length;if(0<u){for(var t="",e=0;e<u;e++)t=L(l,n.charAt(e));return l.l?C(l,t):l.i.toString()}return l.b.toString()}function D(l){var n,u=l.a.toString(),e=0;return 1!=p(l.g,10)?n=!1:n="1"==(n=l.a.toString()).charAt(0)&&"0"!=n.charAt(1)&&"1"!=n.charAt(1),n?(e=1,l.b.a("1").a(" "),l.o=!0):null!=l.g.a[15]&&(n=new RegExp("^(?:"+p(l.g,15)+")"),null!=(n=u.match(n))&&null!=n[0]&&0<n[0].length&&(l.o=!0,e=n[0].length,l.b.a(u.substring(0,e)))),t(l.a),l.a.a(u.substring(e)),u.substring(0,e)}function G(l){var n=l.u.toString(),u=new RegExp("^(?:\\+|"+p(l.g,11)+")");return null!=(u=n.match(u))&&null!=u[0]&&0<u[0].length&&(l.o=!0,u=u[0].length,t(l.a),l.a.a(n.substring(u)),t(l.b),l.b.a(n.substring(0,u)),"+"!=n.charAt(0)&&l.b.a(" "),!0)}function P(l){if(0==l.a.b.length)return!1;var n,e=new u;l:{if(0!=(n=l.a.toString()).length&&"0"!=n.charAt(0))for(var r,i=n.length,a=1;3>=a&&a<=i;++a)if((r=parseInt(n.substring(0,a),10))in Q){e.a(n.substring(a)),n=r;break l}n=0}return 0!=n&&(t(l.a),l.a.a(e.toString()),"001"==(e=N(n))?l.g=A(l.G,""+n):e!=l.D&&(l.g=B(l,e)),l.b.a(""+n).a(" "),l.h="",!0)}function L(l,n){if(0<=(e=l.m.toString()).substring(l.s).search(l.H)){var u=e.search(l.H),e=e.replace(l.H,n);return t(l.m),l.m.a(e),l.s=u,e.substring(0,l.s+1)}return 1==l.f.length&&(l.l=!1),l.w="",l.i.toString()}var V=this;u.prototype.b="",u.prototype.set=function(l){this.b=""+l},u.prototype.a=function(l,n,u){if(this.b+=String(l),null!=n)for(var t=1;t<arguments.length;t++)this.b+=arguments[t];return this},u.prototype.toString=function(){return this.b};var H=1,q=2,T=3,U=4,Y=6,k=16,J=18;o.prototype.set=function(l,n){g(this,l.b,n)},o.prototype.clone=function(){var l=new this.constructor;return l!=this&&(l.a={},l.b&&(l.b={}),s(l,this)),l},n(b,o);var K=null;n(y,o);var O=null;n(v,o);var Z=null;b.prototype.j=function(){var l=K;return l||(K=l=m(b,{0:{name:"NumberFormat",I:"i18n.phonenumbers.NumberFormat"},1:{name:"pattern",required:!0,c:9,type:String},2:{name:"format",required:!0,c:9,type:String},3:{name:"leading_digits_pattern",v:!0,c:9,type:String},4:{name:"national_prefix_formatting_rule",c:9,type:String},6:{name:"national_prefix_optional_when_formatting",c:8,defaultValue:!1,type:Boolean},5:{name:"domestic_carrier_code_formatting_rule",c:9,type:String}})),l},b.j=b.prototype.j,y.prototype.j=function(){var l=O;return l||(O=l=m(y,{0:{name:"PhoneNumberDesc",I:"i18n.phonenumbers.PhoneNumberDesc"},2:{name:"national_number_pattern",c:9,type:String},9:{name:"possible_length",v:!0,c:5,type:Number},10:{name:"possible_length_local_only",v:!0,c:5,type:Number},6:{name:"example_number",c:9,type:String}})),l},y.j=y.prototype.j,v.prototype.j=function(){var l=Z;return l||(Z=l=m(v,{0:{name:"PhoneMetadata",I:"i18n.phonenumbers.PhoneMetadata"},1:{name:"general_desc",c:11,type:y},2:{name:"fixed_line",c:11,type:y},3:{name:"mobile",c:11,type:y},4:{name:"toll_free",c:11,type:y},5:{name:"premium_rate",c:11,type:y},6:{name:"shared_cost",c:11,type:y},7:{name:"personal_number",c:11,type:y},8:{name:"voip",c:11,type:y},21:{name:"pager",c:11,type:y},25:{name:"uan",c:11,type:y},27:{name:"emergency",c:11,type:y},28:{name:"voicemail",c:11,type:y},29:{name:"short_code",c:11,type:y},30:{name:"standard_rate",c:11,type:y},31:{name:"carrier_specific",c:11,type:y},33:{name:"sms_services",c:11,type:y},24:{name:"no_international_dialling",c:11,type:y},9:{name:"id",required:!0,c:9,type:String},10:{name:"country_code",c:5,type:Number},11:{name:"international_prefix",c:9,type:String},17:{name:"preferred_international_prefix",c:9,type:String},12:{name:"national_prefix",c:9,type:String},13:{name:"preferred_extn_prefix",c:9,type:String},15:{name:"national_prefix_for_parsing",c:9,type:String},16:{name:"national_prefix_transform_rule",c:9,type:String},18:{name:"same_mobile_and_fixed_line_pattern",c:8,defaultValue:!1,type:Boolean},19:{name:"number_format",v:!0,c:11,type:b},20:{name:"intl_number_format",v:!0,c:11,type:b},22:{name:"main_country_for_code",c:8,defaultValue:!1,type:Boolean},23:{name:"leading_digits",c:9,type:String},26:{name:"leading_zero_possible",c:8,defaultValue:!1,type:Boolean}})),l},v.j=v.prototype.j,d.prototype.a=function(l){throw new l.b,Error("Unimplemented")},d.prototype.b=function(l,n){if(11==l.a||10==l.a)return n instanceof o?n:this.a(l.i.prototype.j(),n);if(14==l.a){if("string"==typeof n&&z.test(n)){var u=Number(n);if(0<u)return u}return n}if(!l.h)return n;if((u=l.i)===String){if("number"==typeof n)return String(n)}else if(u===Number&&"string"==typeof n&&("Infinity"===n||"-Infinity"===n||"NaN"===n||z.test(n)))return Number(n);return n};var z=/^-?[0-9]+$/;n(_,d),_.prototype.a=function(l,n){var u=new l.b;return u.g=this,u.a=n,u.b={},u},n(S,_),S.prototype.b=function(l,n){return 8==l.a?!!n:d.prototype.b.apply(this,arguments)},S.prototype.a=function(l,n){return S.M.a.call(this,l,n)};var Q={590:["GP","BL","MF"]},W={BL:[null,[null,null,"(?:590|69\\d)\\d{6}",null,null,null,null,null,null,[9]],[null,null,"590(?:2[7-9]|5[12]|87)\\d{4}",null,null,null,"590271234"],[null,null,"69(?:0\\d\\d|1(?:2[29]|3[0-5]))\\d{4}",null,null,null,"690001234"],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],"BL",590,"00","0",null,null,"0",null,null,null,null,null,[null,null,null,null,null,null,null,null,null,[-1]],null,null,[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],null,null,[null,null,null,null,null,null,null,null,null,[-1]]],GP:[null,[null,null,"(?:590|69\\d)\\d{6}",null,null,null,null,null,null,[9]],[null,null,"590(?:0[1-68]|1[0-2]|2[0-68]|3[1289]|4[0-24-9]|5[3-579]|6[0189]|7[08]|8[0-689]|9\\d)\\d{4}",null,null,null,"590201234"],[null,null,"69(?:0\\d\\d|1(?:2[29]|3[0-5]))\\d{4}",null,null,null,"690001234"],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],"GP",590,"00","0",null,null,"0",null,null,null,[[null,"(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[56]"],"0$1"]],null,[null,null,null,null,null,null,null,null,null,[-1]],1,null,[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],null,null,[null,null,null,null,null,null,null,null,null,[-1]]],MF:[null,[null,null,"(?:590|69\\d)\\d{6}",null,null,null,null,null,null,[9]],[null,null,"590(?:0[079]|[14]3|[27][79]|30|5[0-268]|87)\\d{4}",null,null,null,"590271234"],[null,null,"69(?:0\\d\\d|1(?:2[29]|3[0-5]))\\d{4}",null,null,null,"690001234"],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],"MF",590,"00","0",null,null,"0",null,null,null,null,null,[null,null,null,null,null,null,null,null,null,[-1]],null,null,[null,null,null,null,null,null,null,null,null,[-1]],[null,null,null,null,null,null,null,null,null,[-1]],null,null,[null,null,null,null,null,null,null,null,null,[-1]]]};w.b=function(){return w.a?w.a:w.a=new w};var X={0:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9","０":"0","１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9"},ll=RegExp("[+＋]+"),nl=RegExp("([0-9０-９٠-٩۰-۹])"),ul=/^\(?\$1\)?$/,tl=new v;g(tl,11,"NA");var el=/\[([^\[\]])*\]/g,rl=/\d(?=[^,}][^,}])/g,il=RegExp("^[-x‐-―−ー－-／  ­​⁠　()（）［］.\\[\\]/~⁓∼～]*(\\$\\d[-x‐-―−ー－-／  ­​⁠　()（）［］.\\[\\]/~⁓∼～]*)+$"),al=/[- ]/;j.prototype.K=function(){this.C="",t(this.i),t(this.u),t(this.m),this.s=0,this.w="",t(this.b),this.h="",t(this.a),this.l=!0,this.A=this.o=this.F=!1,this.f=[],this.B=!1,this.g!=this.J&&(this.g=B(this,this.D))},j.prototype.L=function(l){return this.C=function(l,n){l.i.a(n);var u,e=n;if(nl.test(e)||1==l.i.b.length&&ll.test(e)?("+"==(e=n)?(u=e,l.u.a(e)):(u=X[e],l.u.a(u),l.a.a(u)),n=u):(l.l=!1,l.F=!0),!l.l){if(!l.F)if(G(l)){if(P(l))return $(l)}else if(0<l.h.length&&(e=l.a.toString(),t(l.a),l.a.a(l.h),l.a.a(e),u=(e=l.b.toString()).lastIndexOf(l.h),t(l.b),l.b.a(e.substring(0,u))),l.h!=D(l))return l.b.a(" "),$(l);return l.i.toString()}switch(l.u.b.length){case 0:case 1:case 2:return l.i.toString();case 3:if(!G(l))return l.h=D(l),I(l);l.A=!0;default:return l.A?(P(l)&&(l.A=!1),l.b.toString()+l.a.toString()):0<l.f.length?(e=L(l,n),0<(u=R(l)).length?u:(F(l,l.a.toString()),E(l)?M(l):l.l?C(l,e):l.i.toString())):I(l)}}(this,l)},l("Cleave.AsYouTypeFormatter",j),l("Cleave.AsYouTypeFormatter.prototype.inputDigit",j.prototype.L),l("Cleave.AsYouTypeFormatter.prototype.clear",j.prototype.K)}).call("object"==typeof global&&global?global:window);
