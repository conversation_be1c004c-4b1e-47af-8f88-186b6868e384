Unison=function(){"use strict";var e,n=window,t=document,i=t.head,o={},u=!1,r={parseMQ:function(e){return n.getComputedStyle(e,null).getPropertyValue("font-family").replace(/"/g,"").replace(/'/g,"")},debounce:function(e,n,t){var i;return function(){var o=this,u=arguments;clearTimeout(i),i=setTimeout((function(){i=null,t||e.apply(o,u)}),n),t&&!i&&e.apply(o,u)}},isObject:function(e){return"object"==typeof e},isUndefined:function(e){return void 0===e}},l={on:function(e,n){r.isObject(o[e])||(o[e]=[]),o[e].push(n)},emit:function(e,n){if(r.isObject(o[e]))for(var t=o[e].slice(),i=0;i<t.length;i++)t[i].call(this,n)}},c={all:function(){for(var e={},n=r.parseMQ(t.querySelector("title")).split(","),i=0;i<n.length;i++){var o=n[i].trim().split(" ");e[o[0]]=o[1]}return u?e:null},now:function(e){var n=r.parseMQ(i).split(" "),t={name:n[0],width:n[1]};return u?r.isUndefined(e)?t:e(t):null},update:function(){c.now((function(n){n.name!==e&&(l.emit(n.name),l.emit("change",n),e=n.name)}))}};return n.onresize=r.debounce(c.update,100),t.addEventListener("DOMContentLoaded",(function(){u="none"!==n.getComputedStyle(i,null).getPropertyValue("clear"),c.update()})),{fetch:{all:c.all,now:c.now},on:l.on,emit:l.emit,util:{debounce:r.debounce,isObject:r.isObject}}}();
