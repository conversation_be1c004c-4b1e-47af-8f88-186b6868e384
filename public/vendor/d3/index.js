var chart;
let _container = document.getElementById('chartContainer'),
    _jsonLink = _container.dataset['jsonLink'],
    _usersIndexLink = _container.dataset['usersIndex']
d3.json(_jsonLink).then(data => {
    chart = new d3.OrgChart()
        .container('#chartContainer')
        .data(data)
        .rootMargin(100)
        .nodeWidth((d) => 210)
        .nodeHeight((d) => 140)
        .childrenMargin((d) => 300)
        .compactMarginBetween((d) => 75)
        .compactMarginPair((d) => 80)
        .buttonContent((d) => {
            // get direct children count
            const childrenCount = d.state.allNodes.filter((l) => l.parent?.id === d.node.id).length ?? 0
            return `<div class="more-children"><img style="width: 14px; top: 10px;" src="/vendor/d3/assets/images/people.svg"/> <span style="color: #1C6A4D; text-align: center;margin-right: -5px;">${childrenCount}</span> </div>`;
        })
        .linkUpdate(function (d, i, arr) {
            // @ts-ignore
            d3.select(this)
                .attr('stroke', (d) =>
                    d.data._upToTheRootHighlighted ? '#152785' : 'lightgray'
                )
                .attr('stroke-width', (d) =>
                    d.data._upToTheRootHighlighted ? 5 : 1.5
                )
                .attr('stroke-dasharray', '4,4');

            if (d.data._upToTheRootHighlighted) {
                // @ts-ignore
                d3.select(this).raise();
            }
        })
        .nodeContent(function (d, i, arr, state) {
            const colors = [
                '#6E6B6F',
                '#18A8B6',
                '#F45754',
                '#96C62C',
                '#BD7E16',
                '#802F74',
            ];
            const color = colors[d.depth % colors.length];
            d.data.avatar = d.data.avatar ?? `/vendor/d3/assets/images/${d.data.gender.toLowerCase()}-icon.svg`
            const imageDim = 80;
            const linkIcon = '/vendor/d3/assets/images/arrow-up-right-from-square-solid.svg';
            const membershipIcons = {
                'premium': '/vendor/d3/assets/images/membership-premium.svg',
                'moshark': '/vendor/d3/assets/images/membership-moshark.svg',
                'montseb': '/vendor/d3/assets/images/membership-montseb.svg',
                '3amel': '/vendor/d3/assets/images/membership-3amel.svg',
            }
            const badgeUrl = membershipIcons[d?.data?.membership] ?? null;
            const isDead = d.data.isDead ? `<p style="margin: 0;color: #DB5252; text-align: center; font-style: normal; font-weight: 700; line-height:1;">-${d.data.gender === 'MALE' ? 'رحمه الله' : 'رحمها الله'}-</p>` : '';
            return `
                <div class="card" style="background-color:white;width:${d.width}px;height:${d.height - 20}px;">
                    <div style="display: flex; justify-content: space-between; width: 100%; flex-direction: row; align-items: flex-start;">
                        ${badgeUrl ? `<img src="${badgeUrl}" style="margin-top:5px;margin-right:5px;width:30px;height:30px;" />` : '<span style="margin-top:5px;margin-right:5px;width:30px;height:30px;"></span>'}
                        <img src="${d.data.avatar}" style="margin-top:-${(imageDim / 2) + 'px'};width:100%;border-radius:100px;max-width:${imageDim + 'px'};height:${imageDim + 'px'};" />
                        ${d.data.is_ambassador ? `<img src="/vendor/d3/assets/images/ambasador.svg" style="width: 100%; float: left;margin-top:5px;margin-left:5px;max-width:35px;height:35px;" />` : '<span style="width: 100%; float: left;margin-top:5px;margin-left:5px;max-width:35px;height:35px;"></span>'}
                    </div>
                    <div style="top:${(imageDim / 2) + 'px'};height:auto;width:${d.width + 'px'};">
                        <div style="display: flex;flex-direction: column;color: #1C6A4D; text-align: center; font-size: 1rem; font-family: helvetica, Arial, sans-serif; font-style: normal; font-weight: 700;">
                            <span> ${d.data.name}</span>
                            ${isDead}
                            <p style="margin: 0; letter-spacing: 0.125rem;"> ${d.data.parentId ? d.data.family_user_id : ''}</p>
                        </div>
                    </div>
                    <a href="${_usersIndexLink}/${d.data.id}" target="_blank">
                        <img src="${linkIcon}" style="position: absolute;bottom:0;margin-bottom:5px;margin-right:5px;width:20px;height:20px;" />
                    </a>
                </div>`;
        })
        .render()
});
