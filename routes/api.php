<?php

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\Webhooks;

$webhookRoutes = function () {
    Route::any('webhook/altwijry-eid-campaign', [Webhooks\WebhookHandler::class, 'campaign']);
    Route::any('webhook/waha', [Webhooks\WebhookHandler::class, 'waha']);
    Route::any('webhook/taqnyat', [Webhooks\WebhookHandler::class, 'taqnyat']);
    Route::get('webhook/strava', [Webhooks\WebhookHandler::class, 'strava_verify']);
    Route::post('webhook/strava', [Webhooks\WebhookHandler::class, 'strava']);
    Route::post('webhook/tally', [Webhooks\WebhookHandler::class, 'tally']);
    Route::post('webhook/myfatoorah', Webhooks\MyfatoorahController::class);
    Route::prefix('wallet/v1')->group(function () {
        Route::post('log', Webhooks\WalletLoggerController::class);
        Route::get('devices/{deviceLibraryIdentifier}/registrations/{passTypeIdentifier}', [Webhooks\WalletDeviceController::class, 'show']);
        Route::post('devices/{deviceLibraryIdentifier}/registrations/{passTypeIdentifier}/{serialNumber}', [Webhooks\WalletDeviceController::class, 'store']);
        Route::delete('devices/{deviceLibraryIdentifier}/registrations/{passTypeIdentifier}/{serialNumber}', [Webhooks\WalletDeviceController::class, 'destroy']);
        Route::get('passes/{passTypeIdentifier}/{serialNumber}', [Webhooks\WalletPassController::class, 'show']);
    });
};

Route::middleware([
    \Stancl\Tenancy\Middleware\InitializeTenancyByDomain::class,
    \Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains::class,
    \App\Http\Middleware\CloudDiskMiddleware::class,
])->group(function () use ($webhookRoutes) {
    Route::middleware(['api'])->group(function () use ($webhookRoutes) {
        Route::middleware(['throttle:api'])->group(function () use ($webhookRoutes) {
            Route::post('/tenancy/assets/{path?}', function ($path) {
                return response()->json([
                    'status' => 'success',
                    'filename' => basename($path),
                    'path' => $path,
                    'url' => tenant_asset($path),
                ]);
            })->where('path', '(.*)');

            Route::get('link-preview', \App\Http\Controllers\API\V1\LinkPreviewController::class);

            Route::any('uploads/preview/{disk}/{path}', function ($disk, $path) {
                if (config("filesystems.disks.$disk.driver") === 's3')
                    return redirect()->to(Storage::disk($disk)->temporaryUrl($path, now()->addMinutes(5)));
                return redirect()->to(Storage::disk($disk)->url($path));
            })->where('path', '.*');

            if (
                \Illuminate\Support\Facades\App::environment(['local', 'testing'])
            ) {
                Route::prefix('api/v1')->group(base_path('routes/v1/index.php'));
                Route::prefix('mobile-api')->group(base_path('routes/mobile/index.php'));
            }

            Route::domain('{api_domain}')
                ->group(function () use ($webhookRoutes) {
                    Route::group([], $webhookRoutes)->withoutMiddleware(['throttle:api']);
                    Route::prefix('v1')->group(base_path('routes/v1/index.php'));
                    Route::middleware([
                        //AltMYSQLMiddleware::class
                    ])->group(base_path('routes/mobile/index.php'));
                });
        });

        Route::prefix('api')->group($webhookRoutes);

        Route::domain('{api_domain}')
            ->group(function () use ($webhookRoutes) {
                Route::group([], $webhookRoutes)->withoutMiddleware(['throttle:api']);
            });
    });
});
