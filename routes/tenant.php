<?php

declare(strict_types=1);

use App\Http\Controllers\API\V1\UploadController;
use App\Http\Controllers\Portal;
use App\Http\Controllers\TenantSettingController;
use App\Http\Middleware\CloudDiskMiddleware;
use App\Http\Middleware\EnableDebugMiddleware;
use App\Http\Middleware\ForceJsonResponseMiddleware;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
    CloudDiskMiddleware::class,
])->group(function () {
    Route::redirect('/', '/admin');

    if (file_exists(__DIR__ . DIRECTORY_SEPARATOR . 'test.php'))
        require(__DIR__ . DIRECTORY_SEPARATOR . 'test.php');

    Route::post('web-api/files/upload', UploadController::class)->middleware([
        ForceJsonResponseMiddleware::class
    ]);

    // Template downloads
    Route::get('templates/bank-import', [\App\Http\Controllers\TemplateDownloadController::class, 'bankImportTemplate'])
        ->name('template.bank-import')
        ->middleware('auth');

    Route::match(['get', 'post'], 'family-graph/{family_graph_share:slug}', [Portal\FamilyGraphController::class, 'view'])
        ->name('family-graph')->middleware(array_filter([
            app()->environment([
                'local',
                //'staging'
            ]) ? null : 'signed',
        ]));
    Route::get('family-graph/{family_graph_share:slug}/user/{user}/children', [Portal\FamilyGraphController::class, 'userChildren'])
        ->name('family-graph.user-children');
    Route::post('family-graph/{family_graph_share:slug}/password', [Portal\FamilyGraphController::class, 'password'])
        ->name('family-graph.password');

    Route::name('admin.')->group(function () {
        Route::match(['get', 'post'], '/broadcasting/auth', [\Illuminate\Broadcasting\BroadcastController::class, 'authenticate']);
        Route::match(['get', 'post'], '/broadcasting/user-auth', [\Illuminate\Broadcasting\BroadcastController::class, 'authenticateUser']);
        Route::group([
            'middleware' => ['auth', EnableDebugMiddleware::class],
        ], function () {
            Route::prefix('help-forms')->name('help-forms.')->group(function () {
                Route::get('media/{help_form}/helpForm-{form_id}.pdf', [Portal\HelpFormController::class, 'pdf'])->name('pdf');
            });

            Route::post('web-api/users', [Portal\ApiController::class, 'usersQuery'])->name('web-api.users');
            Route::post('web-api/non-family-users', [Portal\ApiController::class, 'nonFamilyUsersQuery'])->name('web-api.non-family-users');
            Route::post('web-api/companies', [Portal\ApiController::class, 'companiesQuery'])->name('web-api.companies');
            Route::post('web-api/supporters', [Portal\ApiController::class, 'supportersQuery'])->name('web-api.supporters');
        });
    });
});
