<?php

use App\Http\Controllers\API\V1\Store\ProductController;
use App\Http\Controllers\API\V1\StorePromotionController;
use App\Http\Controllers\API\V1\Store\StoreBannerController;

Route::prefix('store')->group(function () {
    Route::get('products/statistics', [ProductController::class, 'statistics']);
    Route::apiResource('products', ProductController::class)->withRestore();
    Route::apiResource('store-banners', StoreBannerController::class)->withRestore();
    Route::post('products/{product}/sorting', [ProductController::class, 'sorting'])->withTrashed();
    Route::post('store-banners/{store_banner}/sorting', [StoreBannerController::class, 'sorting'])->withTrashed();
});
Route::apiResource('store-promotions', StorePromotionController::class)->withRestore();
