<?php

use App\Http\Controllers\API\V1\Supporting;

Route::get('companies/statistics', [Supporting\CompanyController::class, 'statistics']);
Route::apiResource('companies', Supporting\CompanyController::class);

Route::apiResource('bank-accounts', Supporting\BankAccountController::class);
Route::post('bank-accounts/{bank_account}/sorting', [Supporting\BankAccountController::class, 'sorting'])->withTrashed();
Route::apiResource('supporters', Supporting\SupporterController::class)
    ->only(['index', 'show'])->parameters(['supporters' => 'user']);
Route::apiResource('anonymous-transactions', Supporting\AnonymousTransactionController::class)
    ->parameters([
        'anonymous-transactions' => 'bank_account_transaction',
    ]);
Route::post('anonymous-transactions/{bank_account_transaction}/refund', [Supporting\AnonymousTransactionController::class, 'refund']);

Route::apiResource('anonymous-transactions.comments', Supporting\AnonymousTransactionCommentController::class)
    ->parameters([
        'anonymous-transactions' => 'bank_account_transaction',
    ])->only(['store']);
Route::apiResource('bank-account-transactions', Supporting\BankAccountTransactionController::class);
Route::apiResource('bank-transaction-imports', Supporting\BankTransactionImportController::class);
Route::post('bank-transaction-imports/{bank_transaction_import}/actions', [Supporting\BankTransactionImportController::class, 'actions']);
