<?php

use App\Http\Controllers\API\V1\HelpForms;

Route::get('help-forms/drafts', [HelpForms\HelpFormController::class, 'drafts']);
Route::post('help-forms/duplicate', [HelpForms\HelpFormController::class, 'duplicate']);
Route::get('help-forms/templates', [HelpForms\HelpFormController::class, 'templates']);
Route::get('help-forms/statistics', HelpForms\HelpFormStatisticsController::class);
Route::apiResource('help-forms', HelpForms\HelpFormController::class)->withRestore();
Route::apiResource('help-forms.comments', HelpForms\HelpFormCommentController::class)->only(['store', 'destroy']);
Route::apiResource('help-forms.reasons', HelpForms\HelpFormReasonController::class)->only(['store']);
Route::apiResource('help-forms.shares', HelpForms\HelpFormShareController::class)->parameters([
    'shares' => 'help_form_share'
])->only(['index', 'store', 'destroy']);
Route::post('help-forms/{help_form}/payment', [HelpForms\HelpFormController::class, 'payment']);
Route::post('help-forms/{help_form}/basic-data', HelpForms\HelpFormBasicDataController::class);
Route::post('help-forms/{help_form}/user-details', HelpForms\HelpFormUserDetailsController::class);
Route::post('help-forms/{help_form}/family-details', HelpForms\HelpFormFamilyDetailsController::class);
Route::get('help-forms/{help_form}/sections/{help_form_template_section}', [HelpForms\HelpFormSectionController::class, 'show']);
Route::post('help-forms/{help_form}/sections/{help_form_template_section}', [HelpForms\HelpFormSectionController::class, 'update']);
Route::get('help-forms/{help_form}/pdf', [HelpForms\HelpFormController::class, 'pdf']);
Route::post('help-forms/{help_form}/pdf', [HelpForms\HelpFormController::class, 'pdf']);
Route::apiResource('help-forms-regions.users', HelpForms\HelpFormUserRegionController::class)->parameters([
    'help-forms-regions' => 'user_region'
]);
Route::apiResource('help-forms-regions.calculator', HelpForms\HelpFormUserRegionCalculatorController::class)->parameters([
    'help-forms-regions' => 'user_region'
]);
