<?php

use App\Http\Controllers\API\V1\Auth\ProfileController;
use App\Http\Controllers\API\V1\Auth\OTPMethodController;
use App\Http\Controllers\API\V1\Auth\OTPRequestController;
use App\Http\Controllers\API\V1\Auth\LoginController;
use App\Http\Controllers\API\V1\Auth\TokenLoginController;
use App\Http\Controllers\API\V1\Auth\TwoFactorAuthenticationController;

Route::post('login', LoginController::class);
Route::post('otp', OTPRequestController::class);
Route::post('otp-methods', OTPMethodController::class);
Route::post('login-2fa', TwoFactorAuthenticationController::class);
Route::post('token', TokenLoginController::class);
Route::middleware(['auth:api', 'scope:portal'])->group(function () {
    Route::get('me', [ProfileController::class, 'me']);
    Route::post('profile-picture', [ProfileController::class, 'profile_picture_update']);
    Route::delete('profile-picture', [ProfileController::class, 'profile_picture_delete']);
});
