<?php

use App\Models\AppFeature;
use Illuminate\Http\Request;
use Laravel\Pennant\Feature;
use App\Settings\AppSettings;
use App\Settings\FeatureSettings;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\EnumsController;
use App\Http\Controllers\Mobile\CardController;
use App\Http\Controllers\Mobile\NewsController;
use App\Http\Controllers\Mobile\EventController;
use App\Http\Middleware\AppLastActiveMiddleware;
use App\Http\Controllers\Mobile\StravaController;
use App\Http\Controllers\Mobile\AppDataController;
use App\Http\Controllers\Mobile\AppLinkController;
use App\Http\Controllers\Mobile\CompanyController;
use App\Http\Controllers\Mobile\FeatureController;
use App\Http\Controllers\Mobile\PackageController;
use App\Http\Controllers\Mobile\ProductController;
use App\Http\Controllers\Mobile\ServiceController;
use App\Http\Controllers\Mobile\ActivityController;
use App\Http\Controllers\Mobile\CategoryController;
use App\Http\Controllers\Mobile\DocumentController;
use App\Http\Controllers\Mobile\AppBannerController;
use App\Http\Controllers\Mobile\AwardUserController;
use App\Http\Controllers\Mobile\ChildFormController;
use App\Http\Controllers\Mobile\TallyFormController;
use App\Http\Controllers\Mobile\AwardRouteController;
use App\Http\Controllers\Mobile\EventGuestController;
use App\Http\Controllers\Mobile\FriendshipController;
use App\Http\Controllers\Mobile\LiteratureController;
use App\Http\Controllers\Mobile\MembershipController;
use App\Http\Controllers\Mobile\StatisticsController;
use App\Http\Controllers\Mobile\UserRegionController;
use App\Http\Controllers\Mobile\BankAccountController;
use App\Http\Controllers\Mobile\FamilyGraphController;
use App\Http\Controllers\Mobile\StoreBannerController;
use App\Http\Controllers\Mobile\ConsultationController;
use App\Http\Controllers\Mobile\AwardCategoryController;
use App\Http\Controllers\Mobile\FriendRequestController;
use App\Http\Controllers\Mobile\ManagementCrewController;
use App\Http\Controllers\Mobile\StorePromotionController;
use App\Http\Controllers\Mobile\WorkoutProgramController;
use App\Http\Controllers\Mobile\EventAttendanceController;
use App\Http\Controllers\Mobile\HallReservationController;
use App\Http\Controllers\Mobile\RootFamilyGraphController;
use App\Http\Controllers\Mobile\UserFamilyGraphController;
use App\Http\Controllers\API\Webhooks\MyfatoorahController;
use App\Http\Controllers\Mobile\QuranCompetitionController;
use App\Http\Controllers\Mobile\ProductTransactionController;
use App\Http\Controllers\Mobile\ProductTransactionPaymentController;
use App\Http\Controllers\Mobile\GiftController;
use App\Http\Controllers\Mobile\TransactionStatusController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group([], __DIR__ . '/auth.php');

Route::get('news', [NewsController::class, 'index']);
Route::get('news/{news}', [NewsController::class, 'show']);
Route::get('app-data', AppDataController::class);
Route::apiResource('cards', CardController::class)->only(['index', 'show']);

Route::group(['middleware' => [
    'auth:api',
    AppLastActiveMiddleware::class,
]], function () {
    //Route::post('files/upload', \App\Http\Controllers\API\V1\UploadController::class);
    Route::apiResource('award-categories', AwardCategoryController::class)->only(['index', 'show']);
    Route::apiResource('award-routes', AwardRouteController::class)->only(['show']);
    Route::apiResource('award-users', AwardUserController::class)->only(['index', 'store', 'destroy']);

    Route::apiResource('hall-reservations', HallReservationController::class)->only(['index', 'store', 'destroy']);
    Route::apiResource('services', ServiceController::class)->only(['index']);
    Route::apiResource('child-forms', ChildFormController::class)->only(['index', 'store']);

    Route::get('user-family-graph', [UserFamilyGraphController::class, 'view']);
    Route::get('user-family-graph/user/{user}/children', [UserFamilyGraphController::class, 'userChildren']);

    Route::get('root-family-graph', [RootFamilyGraphController::class, 'view']);
    Route::get('root-family-graph/user/{user}/children', [RootFamilyGraphController::class, 'userChildren']);

    Route::apiResource('consultations', ConsultationController::class)->only(['index', 'store']);

    Route::apiResource('quran-competitions', QuranCompetitionController::class)->only(['index', 'show']);
    Route::post('quran-competitions/{quran_competition}/requests', [QuranCompetitionController::class, 'apply_request']);

    Route::post('/activities/{activity}/requests', [ActivityController::class, 'apply_request']);

    Route::apiResource('events', EventController::class)->only(['index', 'show']);
    Route::apiResource('events.attendance', EventAttendanceController::class)->only(['store']);
    Route::apiResource('events.guests', EventGuestController::class)->only(['index', 'store']);

    Route::get('/events/{event}/apple-wallet-invitation', [EventController::class, 'appleWallet']);
    Route::get('/events/{event}/google-wallet-invitation', [EventController::class, 'googleWallet']);
    Route::get('/events/{event}/invitation', [EventController::class, 'invitation']);
    Route::post('/events/{event}/invitations/{event_invitation}/download', [EventController::class, 'download']);

    Route::get('/management-crew', [ManagementCrewController::class, 'view']);
    Route::get('/members', MembershipController::class);
    Route::get('/tally-forms', TallyFormController::class);
    Route::group([
        'prefix' => 'strava',
        'controller' => StravaController::class,
    ], function () {
        Route::post('/auth/callback', 'callback');
        Route::post('/deauthorize', 'deauthorize');
        Route::get('/athlete', 'athlete');
    });

    Route::get('/workout-programs/{workout_program}/timeline', [WorkoutProgramController::class, 'timeline']);
    Route::apiResource('friendships', FriendshipController::class)
        ->parameters(['friendships' => 'friend'])
        ->only(['index', 'destroy']);
    Route::get('friend-requests/statistics', [FriendRequestController::class, 'statistics']);
    Route::apiResource('friend-requests', FriendRequestController::class)->except(['show']);
});

Route::controller(EnumsController::class)->prefix('/enums')->group(function () {
    Route::get('/consultation-types', 'consultationTypes');
});

Route::apiResource('packages', PackageController::class)->only('index', 'show');
Route::get('packages/{package}/upgrade', [PackageController::class, 'upgrade']);
Route::get('packages/{package}/upgrade/pay', [PackageController::class, 'upgradePay']);
Route::get('packages/{package}/pay', [PackageController::class, 'pay']);

Route::get('/statistics', StatisticsController::class);
Route::get('/user-regions', [UserRegionController::class, 'index']);

// Gift functionality routes - consolidated and privacy-focused
Route::get('/users/{familyUserId}/gift-eligibility', [GiftController::class, 'checkGiftEligibility']);
Route::get('/users/{familyUserId}/upgrade-eligibility/{package}', [GiftController::class, 'checkUpgradeEligibility']);

// Transaction status API routes
Route::get('/transactions/{uuid}/status', [TransactionStatusController::class, 'getStatus']);
Route::get('/transactions/{uuid}/details', [TransactionStatusController::class, 'getDetails']);
Route::post('/transactions/{uuid}/reprocess', [TransactionStatusController::class, 'reprocess']);

Route::get('/categories', CategoryController::class);

Route::get('/products', [ProductController::class, 'index']);
Route::get('/products/batch', [ProductController::class, 'getBatch']);
Route::get('/products/{product}', [ProductController::class, 'show'])->withTrashed();

Route::get('/store-banners', [StoreBannerController::class, 'index']);
Route::put('/store-banners/{store_banner}/click', [StoreBannerController::class, 'click']);

Route::get('/app-banners', [AppBannerController::class, 'index']);
Route::put('/app-banners/{app_banner}/click', [AppBannerController::class, 'click']);

Route::post('/cards/{card}/download', [CardController::class, 'download']);

Route::put('/app-links/{app_link}/click', [AppLinkController::class, 'click']);

Route::get('/workout-programs', [WorkoutProgramController::class, 'index']);
Route::get('/workout-programs/{workout_program}', [WorkoutProgramController::class, 'show']);

Route::get('/store-promotions', [StorePromotionController::class, 'index']);
Route::get('/store-promotions/{store_promotion}', [StorePromotionController::class, 'show']);

Route::get('/literatures', [LiteratureController::class, 'index']);
Route::get('/literatures/{literature}', [LiteratureController::class, 'show']);

Route::get('/documents', [DocumentController::class, 'index']);
Route::get('/documents/{document}', [DocumentController::class, 'show']);

Route::get('/companies', [CompanyController::class, 'index']);
Route::get('/companies/{document}', [CompanyController::class, 'show']);

Route::get('/activities', [ActivityController::class, 'index']);
Route::get('/activities/{activity}', [ActivityController::class, 'show']);

Route::get('/user-transactions', [ProductTransactionController::class, 'index']);
Route::get('/user-transactions/{product_transaction}', [ProductTransactionController::class, 'show']);

Route::post('/products/{product}/transactions', [ProductTransactionController::class, 'store']);
Route::get('/transactions/{product_transaction}', [ProductTransactionPaymentController::class, 'show']);
Route::get('/transactions/{product_transaction:slug}/slug', [ProductTransactionPaymentController::class, 'show']);
Route::post('/transactions/{product_transaction}/execute', [ProductTransactionPaymentController::class, 'execute']);
Route::post('/myfatoorah-webhook', MyfatoorahController::class);

Route::match(['get', 'post'], 'family-graph/{family_graph_share:slug}', [FamilyGraphController::class, 'view']);
Route::post('family-graph/{family_graph_share:slug}/password', [FamilyGraphController::class, 'password']);
Route::get('family-graph/{family_graph_share:slug}/user/{user}/children', [FamilyGraphController::class, 'userChildren']);
Route::get('bank-accounts', BankAccountController::class);

Route::any('/features', FeatureController::class);
Route::any('/features-deprecated', function (Request $request) {
    $user = $request->user('api');
    $features = Feature::for('guest')->all();
    $features = array_merge($features, array_map(fn($v) => Arr::first($v), Feature::for('guest')
        ->load(AppFeature::query()->pluck('slug'))));
    $inReview = false;
    if (!is_null($user)) {
        if ($user->roles->count() > 0)
            $features = Arr::map($features, fn($v, $k) => $user->roles->some(fn($role) => Feature::for($role)->active($k) !== false));
        else
            $features = Arr::map($features, fn($v, $k) => Feature::for('auth')->active($k));
        $inReview = $user->is_apple_reviewer && app(AppSettings::class)->reviewMode;
    }
    if ($inReview) {
        $features['ActiveMembers'] = false;
        $features['Memberships'] = false;
        $features['Store'] = false;
        $features['Companies'] = false;
        $features['Strava'] = false;
        $features['ChildForms'] = false;
        $features['Promotions'] = false;
        $features['BankAccounts'] = false;
        $features['ActivitySupportAmount'] = false;
        $features['Notifications'] = false;
        $features['WalletPasses'] = false;
        $features['WQFK'] = false;
        $features['AccountDelete'] = true;
    }
    if ($user?->is_system_admin === true) {
        $features['Debugger'] = true;
    }
    $features['Strava'] = @$features['Strava'] && app(FeatureSettings::class)->stravaEnabled;
    $features['Memberships'] = @$features['Memberships'] && app(FeatureSettings::class)->membershipsEnabled;
    return response()->json(Arr::mapWithKeys($features, function ($v, $k) {
        return [Arr::last(explode('\\', $k)) => $v];
    }));
});

Route::any('/{any}', fn() => 'hello :)')->where('any', '.*');
