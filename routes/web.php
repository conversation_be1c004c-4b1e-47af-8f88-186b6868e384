<?php

use Illuminate\Support\Facades\Route;

Route::domain('{central_domain}')
    ->withoutMiddleware([
        \Stancl\Tenancy\Middleware\InitializeTenancyByDomain::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\TimezoneDetectorMiddleware::class,
        \App\Http\Middleware\TransformArabicToEasternNumbers::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \Stancl\Tenancy\Middleware\CheckTenantForMaintenanceMode::class,
    ])
    ->middleware(['universal'])->group(function () {
        Route::any('/{any?}', fn() => '')->where('any', '.*');
    });

if (file_exists(base_path('routes/tenant.php'))) {
    Route::group([], base_path('routes/tenant.php'));
}
