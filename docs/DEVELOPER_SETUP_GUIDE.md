# Developer Setup Guide - Gift Functionality

## Overview
This guide helps developers set up and work with the gift functionality in any IDE or development environment. The implementation follows clean architecture principles with domain-specific logic separated from the generic transaction model.

## Project Structure

### Core Files (Relative to Project Root)
```
app/Models/ProductTransaction.php          # Generic transaction model (domain-agnostic)
app/Services/GiftTransaction.php           # Gift-specific wrapper class
app/Services/GiftService.php               # Gift processing business logic
app/Http/Controllers/Mobile/GiftController.php  # Mobile API endpoints
app/Http/Requests/Mobile/GiftPaymentRequest.php # Request validation
app/Http/Resources/Mobile/GiftPaymentResource.php # API responses
routes/mobile/index.php                    # Mobile API routes
database/migrations/tenant/                # Database migrations
tests/Feature/Mobile/GiftFunctionalityTest.php  # Feature tests
```

### Documentation Files
```
docs/TRANSACTION_METADATA_SYSTEM.md       # Generic metadata system
docs/GIFT_API_DOCUMENTATION.md            # API documentation
docs/CLEAN_ARCHITECTURE_REFACTOR.md       # Architecture overview
docs/GIFT_SECURITY_IMPROVEMENTS.md        # Security features
docs/DEVELOPER_SETUP_GUIDE.md             # This file
```

## Quick Start

### 1. Database Setup
Run the migrations to set up the database structure:
```bash
# Run tenant migrations
php artisan migrate --path=database/migrations/tenant
```

### 2. Understanding the Architecture

#### Generic Transaction Model
The `ProductTransaction` model contains **only** generic metadata methods:
```php
<?php
// File: app/Models/ProductTransaction.php

use App\Models\ProductTransaction;

$transaction = ProductTransaction::find(1);

// Generic metadata operations
$transaction->setMetadata('transaction_type', 'gift');
$transaction->getMetadata('transaction_type');
$transaction->hasMetadata('transaction_type');
$transaction->isTransactionType('gift');
```

#### Gift-Specific Wrapper
All gift functionality is provided by the wrapper class:
```php
<?php
// File: app/Services/GiftTransaction.php

use App\Models\ProductTransaction;
use App\Services\GiftTransaction;

$transaction = ProductTransaction::find(1);

// Check if it's a gift transaction
if ($transaction->isTransactionType('gift')) {
    // Wrap with gift-specific functionality
    $giftTransaction = GiftTransaction::wrap($transaction);
    
    // Use gift-specific methods
    $giver = $giftTransaction->giftGiver();
    $recipient = $giftTransaction->giftRecipient();
    $message = $giftTransaction->getGiftMessage();
}
```

### 3. Required Imports
When working with gift functionality, include these imports:
```php
<?php

use App\Models\ProductTransaction;
use App\Services\GiftTransaction;
use App\Services\GiftService;
use App\Models\User;
use App\Models\Package;
```

## Development Patterns

### 1. Creating Gift Transactions
```php
<?php

use App\Services\GiftTransaction;

// Method 1: Using wrapper helper
$giftTransaction = GiftTransaction::createGiftTransaction([
    'user_id' => $giver->id,
    'product_id' => $package->product->id,
    'amount' => 299,
    'session_id' => $sessionId,
], [
    'gift_sub_type' => 'new_membership',
    'giver_user_id' => $giver->id,
    'recipient_user_id' => $recipient->id,
    'gift_message' => 'Happy Birthday!',
]);

// Method 2: Manual creation and wrapping
$transaction = ProductTransaction::create([
    'user_id' => $giver->id,
    'product_id' => $package->product->id,
    'amount' => 299,
    'transaction_metadata' => [
        'transaction_type' => 'gift',
        'gift_sub_type' => 'new_membership',
        'giver_user_id' => $giver->id,
        'recipient_user_id' => $recipient->id,
        'gift_message' => 'Happy Birthday!',
    ],
]);
$giftTransaction = GiftTransaction::wrap($transaction);
```

### 2. Querying Gift Transactions
```php
<?php

use App\Services\GiftTransaction;
use App\Models\User;

// Get all gift transactions
$allGifts = GiftTransaction::all();

// Get gifts for specific users
$user = User::find(1);
$sentGifts = GiftTransaction::forGiver($user);
$receivedGifts = GiftTransaction::forRecipient($user);

// Using generic queries with wrapper
$giftTransactions = ProductTransaction::ofType('gift')->get()->map(function ($transaction) {
    return GiftTransaction::wrap($transaction);
});
```

### 3. Processing Gift Transactions
```php
<?php

use App\Services\GiftService;
use App\Services\GiftTransaction;

// In webhook or payment processing
public function processPayment(ProductTransaction $transaction)
{
    if ($transaction->isTransactionType('gift')) {
        $giftService = app(GiftService::class);
        $success = $giftService->processCompletedGift($transaction);
        
        if ($success) {
            // Gift processed successfully
            $giftTransaction = GiftTransaction::wrap($transaction);
            $recipient = $giftTransaction->giftRecipient();
            // Send notifications, etc.
        }
    }
}
```

## Testing

### 1. Running Tests
```bash
# Run all gift functionality tests
php artisan test tests/Feature/Mobile/GiftFunctionalityTest.php

# Run specific test
php artisan test --filter=it_can_create_gift_payment_session_for_new_membership
```

### 2. Writing Tests
```php
<?php
// File: tests/Feature/Mobile/GiftFunctionalityTest.php

use App\Models\ProductTransaction;
use App\Services\GiftTransaction;

/** @test */
public function it_can_wrap_gift_transactions()
{
    $transaction = ProductTransaction::factory()->create([
        'transaction_metadata' => [
            'transaction_type' => 'gift',
            'giver_user_id' => 1,
            'recipient_user_id' => 2,
        ]
    ]);
    
    $giftTransaction = GiftTransaction::wrap($transaction);
    
    $this->assertTrue($giftTransaction->isGift());
    $this->assertInstanceOf(User::class, $giftTransaction->giftGiver());
}
```

## Extending for New Transaction Types

### 1. Create Wrapper Class
```php
<?php
// File: app/Services/SubscriptionTransaction.php

namespace App\Services;

use App\Models\ProductTransaction;

class SubscriptionTransaction
{
    protected ProductTransaction $transaction;

    public function __construct(ProductTransaction $transaction)
    {
        $this->transaction = $transaction;
    }

    public static function wrap(ProductTransaction $transaction): self
    {
        return new self($transaction);
    }

    public function isSubscription(): bool
    {
        return $this->transaction->isTransactionType('subscription');
    }

    public function getBillingCycle(): ?string
    {
        return $this->transaction->getMetadata('billing_cycle');
    }

    public function isAutoRenew(): bool
    {
        return $this->transaction->getMetadata('auto_renew', false);
    }
}
```

### 2. Usage Pattern
```php
<?php

use App\Services\SubscriptionTransaction;

// Check and wrap subscription transactions
if ($transaction->isTransactionType('subscription')) {
    $subscriptionTransaction = SubscriptionTransaction::wrap($transaction);
    
    if ($subscriptionTransaction->isAutoRenew()) {
        $cycle = $subscriptionTransaction->getBillingCycle();
        // Handle subscription logic
    }
}
```

## IDE Configuration

### VS Code
Add to `.vscode/settings.json`:
```json
{
    "php.suggest.basic": false,
    "php.validate.executablePath": "/usr/bin/php",
    "files.associations": {
        "*.php": "php"
    }
}
```

### PhpStorm
1. Configure PHP interpreter: Settings → PHP → CLI Interpreter
2. Enable Laravel plugin: Settings → Plugins → Laravel
3. Configure code style: Settings → Code Style → PHP

### Sublime Text
Install packages:
- PHP Companion
- Laravel Blade Highlighter
- SublimeLinter-php

## Common Issues and Solutions

### 1. Namespace Issues
Always include proper use statements:
```php
<?php

use App\Models\ProductTransaction;
use App\Services\GiftTransaction;
use App\Models\User;
```

### 2. Metadata Not Saving
Remember to save after setting metadata:
```php
$transaction->setMetadata('key', 'value');
$transaction->save(); // Don't forget this!
```

### 3. Wrapper Not Working
Ensure transaction has correct type:
```php
// Check transaction type first
if ($transaction->isTransactionType('gift')) {
    $giftTransaction = GiftTransaction::wrap($transaction);
    // Now use gift methods
}
```

## Performance Tips

### 1. Eager Loading
```php
// Load related data efficiently
$transactions = ProductTransaction::with(['user', 'product'])
    ->ofType('gift')
    ->get();
```

### 2. Batch Operations
```php
// Process multiple gifts efficiently
$giftTransactions = GiftTransaction::all();
foreach ($giftTransactions as $giftTransaction) {
    // Process each gift
}
```

### 3. Query Optimization
```php
// Use specific queries instead of loading all data
$userGifts = GiftTransaction::forGiver($user);
// Instead of loading all gifts and filtering
```

## Deployment Checklist

1. ✅ Run migrations: `php artisan migrate --path=database/migrations/tenant`
2. ✅ Clear caches: `php artisan cache:clear`
3. ✅ Run tests: `php artisan test tests/Feature/Mobile/GiftFunctionalityTest.php`
4. ✅ Verify API endpoints work
5. ✅ Check webhook processing
6. ✅ Monitor error logs

## Support and Documentation

- **API Documentation**: `docs/GIFT_API_DOCUMENTATION.md`
- **Architecture Guide**: `docs/CLEAN_ARCHITECTURE_REFACTOR.md`
- **Security Features**: `docs/GIFT_SECURITY_IMPROVEMENTS.md`
- **Metadata System**: `docs/TRANSACTION_METADATA_SYSTEM.md`
